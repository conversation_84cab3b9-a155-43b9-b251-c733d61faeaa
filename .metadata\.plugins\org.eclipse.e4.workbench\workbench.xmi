<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_ysPRkHKgEfC9doPhCnHTeg" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_ysPRkXKgEfC9doPhCnHTeg" bindingContexts="_ysPRmnKgEfC9doPhCnHTeg">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_ysPRkXKgEfC9doPhCnHTeg" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_zDvyo3KgEfC9doPhCnHTeg" x="160" y="160" width="1050" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1754469541337"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_zDvyo3KgEfC9doPhCnHTeg" selectedElement="_zDwZsXKgEfC9doPhCnHTeg" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_zDwZsHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.perspectivestack" visible="false" containerData="7500" selectedElement="_zJZHoHKgEfC9doPhCnHTeg">
        <tags>Minimized</tags>
        <tags>MinimizedByZoom</tags>
        <children xsi:type="advanced:Perspective" xmi:id="_zJZHoHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_zJZHoXKgEfC9doPhCnHTeg" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.st.stm32cube.ide.mcu.informationcenter.actionSet3</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.ConvertToMakeWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewMakeFromExisting</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizard.project</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectwizard</tags>
          <tags>persp.newWizSC:com.st.stm32cube.common.projectcreation.ui.stm32projectfromiocwizard</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.buildanalyzer.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view</tags>
          <tags>persp.newWizSC:com.st.stm32cube.ide.cmake.newwizard</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view</tags>
          <tags>persp.viewSC:com.st.stm32cube.ide.mcu.sfrview</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_zJZHoXKgEfC9doPhCnHTeg" selectedElement="_zJZHpnKgEfC9doPhCnHTeg" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_zJZHonKgEfC9doPhCnHTeg" elementId="topLeft" containerData="2500" selectedElement="_zJZHo3KgEfC9doPhCnHTeg">
              <children xsi:type="advanced:Placeholder" xmi:id="_zJZHo3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_zJMTUHKgEfC9doPhCnHTeg" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_zJZHpHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_zJPWoHKgEfC9doPhCnHTeg" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:C/C++</tags>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="_zJZHpXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_zJPWoXKgEfC9doPhCnHTeg" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:General</tags>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_zJZHpnKgEfC9doPhCnHTeg" containerData="7500" selectedElement="_zJZHrXKgEfC9doPhCnHTeg">
              <children xsi:type="basic:PartSashContainer" xmi:id="_zJZHp3KgEfC9doPhCnHTeg" containerData="7500" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_zJZHqHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_zJGMsHKgEfC9doPhCnHTeg"/>
                <children xsi:type="basic:PartStack" xmi:id="_zJZHqXKgEfC9doPhCnHTeg" elementId="topRight" containerData="2500" selectedElement="_zJZHqnKgEfC9doPhCnHTeg">
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHqnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ContentOutline" ref="_zJP9s3KgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHq3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_zJU2MHKgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHrHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_zJXScHKgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Make</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_zJZHrXKgEfC9doPhCnHTeg" containerData="2500" selectedElement="_zJZHrnKgEfC9doPhCnHTeg" horizontal="true">
                <children xsi:type="basic:PartStack" xmi:id="_zJZHrnKgEfC9doPhCnHTeg" elementId="bottom" containerData="5000" selectedElement="_zJZHr3KgEfC9doPhCnHTeg">
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHr3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ProblemView" ref="_zJPWonKgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHsHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.TaskList" ref="_zJP9sHKgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHsXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.console.ConsoleView" ref="_zJP9sXKgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHsnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.PropertySheet" ref="_zJP9snKgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_zJZHs3KgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.viewMStack" containerData="5000" selectedElement="_zJZHtHKgEfC9doPhCnHTeg">
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHtHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" ref="_zJX5gHKgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHtXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" ref="_zJX5gXKgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_zJZHtnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" ref="_zJYgkHKgEfC9doPhCnHTeg" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_zDwZsXKgEfC9doPhCnHTeg" elementId="stickyFolderRight" containerData="2500" selectedElement="_zDwZs3KgEfC9doPhCnHTeg">
        <tags>active</tags>
        <tags>Maximized</tags>
        <children xsi:type="advanced:Placeholder" xmi:id="_zDwZsnKgEfC9doPhCnHTeg" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_zDvyoHKgEfC9doPhCnHTeg" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_zDwZs3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.internal.introview" ref="_zDvyoXKgEfC9doPhCnHTeg" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_zDwZtHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_zDvyonKgEfC9doPhCnHTeg" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_zDvyoHKgEfC9doPhCnHTeg" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zDvyoXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Information Center" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;file:///D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/configuration/org.eclipse.osgi/73/0/.cp/welcome/index.html&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_zX9vIHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zX9vIXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zDvyonKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_zJGMsHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_zJGMsXKgEfC9doPhCnHTeg" elementId="org.eclipse.e4.primaryDataStack">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJMTUHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_zNwDMHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zNwDMXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJPWoHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJPWoXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJPWonKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;350&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_zSG-wHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zSG-wXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJP9sHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;350&quot; org.eclipse.ui.ide.locationField=&quot;105&quot; org.eclipse.ui.ide.markerType=&quot;105&quot; org.eclipse.ui.ide.pathField=&quot;140&quot; org.eclipse.ui.ide.priorityField=&quot;35&quot; org.eclipse.ui.ide.resourceField=&quot;105&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_zbLbMHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zbLbMXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJP9sXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_zcCW0HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zcCW0XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.console.ConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJP9snKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_zctFMHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zctFMXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.PropertySheet" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJP9s3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_zRTGcHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zRTGcXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ContentOutline" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJU2MHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJXScHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view horizontalPosition=&quot;0&quot; verticalPosition=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
      <menus xmi:id="_zdehQHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.views.MakeView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zdehQXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.views.MakeView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJX5gHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_zS-hcHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zS-hcXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJX5gXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_zeZHQHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zeZHQXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_zJYgkHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_zfTtQHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zfTtQXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_ysPRknKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_zFhUQHKgEfC9doPhCnHTeg" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_zFhUQXKgEfC9doPhCnHTeg" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zFiiYHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_zFmz0HKgEfC9doPhCnHTeg" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_ysr90XKgEfC9doPhCnHTeg"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zFiiYXKgEfC9doPhCnHTeg" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_zFiiYnKgEfC9doPhCnHTeg" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zFiiY3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_zFna4HKgEfC9doPhCnHTeg" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_ysr9v3KgEfC9doPhCnHTeg"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_zFna4XKgEfC9doPhCnHTeg" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_ysslBnKgEfC9doPhCnHTeg"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zFiiZHKgEfC9doPhCnHTeg" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_zFiiZXKgEfC9doPhCnHTeg" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zKYmIHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.CElementCreationActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zLVoYHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zK15IHKgEfC9doPhCnHTeg" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zKGSQHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zFiiZnKgEfC9doPhCnHTeg" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_zFiiZ3KgEfC9doPhCnHTeg" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zFjJcHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_zFoB83KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" enabled="false" type="Check" command="_ysrWsnKgEfC9doPhCnHTeg"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zFjJcXKgEfC9doPhCnHTeg" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_zFjJcnKgEfC9doPhCnHTeg" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zFjJc3KgEfC9doPhCnHTeg" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_zFjJdHKgEfC9doPhCnHTeg" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_zFjJdXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_zHKTAHKgEfC9doPhCnHTeg" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_zHK6EHKgEfC9doPhCnHTeg" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_ysPRk3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_ysPRlHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_ysPRlXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_ysPRlnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_ysPRl3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_zZeLAHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_ysPRmHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Right"/>
  </children>
  <bindingTables xmi:id="_ysPRmXKgEfC9doPhCnHTeg" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_ysPRmnKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys-RYXKgEfC9doPhCnHTeg" keySequence="CTRL+1" command="_ysrWq3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-Re3KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+L" command="_yssku3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_flHKgEfC9doPhCnHTeg" keySequence="CTRL+V" command="_ysqIfnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGm3KgEfC9doPhCnHTeg" keySequence="CTRL+A" command="_yssk2HKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtoHKgEfC9doPhCnHTeg" keySequence="CTRL+C" command="_ysqIVXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAttnKgEfC9doPhCnHTeg" keySequence="CTRL+X" command="_ysr9x3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtt3KgEfC9doPhCnHTeg" keySequence="CTRL+Y" command="_ysslBnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtunKgEfC9doPhCnHTeg" keySequence="CTRL+Z" command="_ysr9v3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUxHKgEfC9doPhCnHTeg" keySequence="ALT+PAGE_UP" command="_ysslHHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUxXKgEfC9doPhCnHTeg" keySequence="ALT+PAGE_DOWN" command="_ysqvqHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUyHKgEfC9doPhCnHTeg" keySequence="SHIFT+INSERT" command="_ysqIfnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUzXKgEfC9doPhCnHTeg" keySequence="ALT+F11" command="_ysqvfnKgEfC9doPhCnHTeg">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ytDJ6nKgEfC9doPhCnHTeg" keySequence="CTRL+F10" command="_ysqvYHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ73KgEfC9doPhCnHTeg" keySequence="CTRL+INSERT" command="_ysqIVXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ-3KgEfC9doPhCnHTeg" keySequence="CTRL+PAGE_UP" command="_ysr97HKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ_HKgEfC9doPhCnHTeg" keySequence="CTRL+PAGE_DOWN" command="_ysrWs3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ_nKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+F3" command="_ysr93nKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw8XKgEfC9doPhCnHTeg" keySequence="SHIFT+DEL" command="_ysr9x3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYCnKgEfC9doPhCnHTeg" keySequence="ALT+/" command="_ysr9pnKgEfC9doPhCnHTeg">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ys71IHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.textEditorScope" bindingContext="_ysvA0nKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys9DQHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+CR" command="_ysr93HKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys9qUnKgEfC9doPhCnHTeg" keySequence="CTRL+BS" command="_ysphSXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RYHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+Q" command="_ysrWmHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-Rd3KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+J" command="_ysrWh3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-ReXKgEfC9doPhCnHTeg" keySequence="CTRL++" command="_ysqvfHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RfnKgEfC9doPhCnHTeg" keySequence="CTRL+-" command="_ysskvnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fhnKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+J" command="_ysrWpXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fjnKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+A" command="_ysqIc3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fmXKgEfC9doPhCnHTeg" keySequence="CTRL+J" command="_ysqvZnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGk3KgEfC9doPhCnHTeg" keySequence="CTRL+L" command="_ysr9unKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtpHKgEfC9doPhCnHTeg" keySequence="CTRL+D" command="_ysqvcHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtsHKgEfC9doPhCnHTeg" keySequence="CTRL+=" command="_ysqvfHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAts3KgEfC9doPhCnHTeg" keySequence="ALT+CTRL+/" command="_ysskyXKgEfC9doPhCnHTeg">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_ytAttHKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Y" command="_ysphQXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUsHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+DEL" command="_ysr9q3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUsXKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+X" command="_ysqIYHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUsnKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+Y" command="_ysskvXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUtnKgEfC9doPhCnHTeg" keySequence="CTRL+DEL" command="_ysr9vHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUu3KgEfC9doPhCnHTeg" keySequence="ALT+ARROW_UP" command="_ysslGHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUvXKgEfC9doPhCnHTeg" keySequence="ALT+ARROW_DOWN" command="_ysrWdXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUxnKgEfC9doPhCnHTeg" keySequence="SHIFT+END" command="_ysskxHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUznKgEfC9doPhCnHTeg" keySequence="SHIFT+HOME" command="_ysskqXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi03KgEfC9doPhCnHTeg" keySequence="END" command="_ysskm3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi1HKgEfC9doPhCnHTeg" keySequence="INSERT" command="_ysqImHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi1nKgEfC9doPhCnHTeg" keySequence="F2" command="_ysrWtXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi5HKgEfC9doPhCnHTeg" keySequence="HOME" command="_ysskxnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi53KgEfC9doPhCnHTeg" keySequence="ALT+CTRL+ARROW_UP" command="_yssk83KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi6HKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+ARROW_DOWN" command="_yssk5XKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi63KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+INSERT" command="_ysqvrHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ43KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_ysskx3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ5HKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_ysrWeHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ63KgEfC9doPhCnHTeg" keySequence="CTRL+F10" command="_ysr92HKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ7XKgEfC9doPhCnHTeg" keySequence="CTRL+END" command="_ysrWe3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ93KgEfC9doPhCnHTeg" keySequence="CTRL+ARROW_UP" command="_ysqvkHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ-HKgEfC9doPhCnHTeg" keySequence="CTRL+ARROW_DOWN" command="_ysslMHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ-XKgEfC9doPhCnHTeg" keySequence="CTRL+ARROW_LEFT" command="_ysphSnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ-nKgEfC9doPhCnHTeg" keySequence="CTRL+ARROW_RIGHT" command="_ysrWlXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ_XKgEfC9doPhCnHTeg" keySequence="CTRL+HOME" command="_ysqIfXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ_3KgEfC9doPhCnHTeg" keySequence="CTRL+NUMPAD_MULTIPLY" command="_ysrWl3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDKAHKgEfC9doPhCnHTeg" keySequence="CTRL+NUMPAD_ADD" command="_yssk4HKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDKAXKgEfC9doPhCnHTeg" keySequence="CTRL+NUMPAD_SUBTRACT" command="_ysr92nKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw8HKgEfC9doPhCnHTeg" keySequence="CTRL+NUMPAD_DIVIDE" command="_ysqvk3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw9XKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_ysrWo3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw93KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_ysqImXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYDHKgEfC9doPhCnHTeg" keySequence="SHIFT+CR" command="_ysskxXKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ys9qUHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_ysvA5HKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys9qUXKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+SHIFT+C" command="_ysslMnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys9qU3KgEfC9doPhCnHTeg" keySequence="CTRL+TAB" command="_ysslLHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys9qVnKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+P" command="_yssknXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RZ3KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+T" command="_ysr9j3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RbnKgEfC9doPhCnHTeg" keySequence="CTRL+7" command="_ysqIgXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-Rc3KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+H" command="_ysqvaXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4cHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+N" command="_ysrWlHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4dHKgEfC9doPhCnHTeg" keySequence="CTRL+/" command="_ysqIgXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4dXKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+O" command="_ysr913KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4dnKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+A" command="_ysr933KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4eHKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+S" command="_ysslDHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4eXKgEfC9doPhCnHTeg" keySequence="CTRL+#" command="_ysr9hHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4enKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+C" command="_ysqIgXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4fXKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+F" command="_ysslLXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4fnKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+G" command="_ysphRHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4gXKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+H" command="_ysrWrnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fgHKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+I" command="_ysqIhXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fknKgEfC9doPhCnHTeg" keySequence="CTRL+T" command="_ysrWgHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fmHKgEfC9doPhCnHTeg" keySequence="CTRL+I" command="_ysrWhXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGmHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+/" command="_yssk33KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGmXKgEfC9doPhCnHTeg" keySequence="CTRL+O" command="_ysqIaHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGnHKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+R" command="_ysr9sHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGn3KgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+S" command="_ysqvYXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtoXKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+T" command="_ysr9h3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtqXKgEfC9doPhCnHTeg" keySequence="CTRL+G" command="_ysslAXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtrXKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+L" command="_yssk53KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtrnKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+M" command="_ysqIdHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtr3KgEfC9doPhCnHTeg" keySequence="CTRL+=" command="_ysr9hHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtsnKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+O" command="_ysr9yXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAttXKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Z" command="_ysr9ynKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUtXKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+\" command="_ysqIgnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi2HKgEfC9doPhCnHTeg" keySequence="F3" command="_ysslNnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi3nKgEfC9doPhCnHTeg" keySequence="F4" command="_yssk2XKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ4XKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+ARROW_UP" command="_ysr9tHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ4nKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_ysqvqnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ5nKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+ARROW_UP" command="_ysskrHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ6HKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+ARROW_DOWN" command="_ysslLnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ6XKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+ARROW_LEFT" command="_ysr9p3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ7HKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_yssks3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw-3KgEfC9doPhCnHTeg" keySequence="ALT+C" command="_ysqIg3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYC3KgEfC9doPhCnHTeg" keySequence="SHIFT+TAB" command="_ysqva3KgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ys9qVHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.contexts.window" bindingContext="_ysPRm3KgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys9qVXKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+SHIFT+L" command="_ysqIi3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys9qWXKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q O" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ys9qWnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_ys-RYnKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+B" command="_ysqvn3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RY3KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+R" command="_ysslNHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RZHKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q Q" command="_ysqvl3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RZXKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+S" command="_ysqvdXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RZnKgEfC9doPhCnHTeg" keySequence="CTRL+3" command="_ysrWtHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RanKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q S" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ys-Ra3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_ys-Rb3KgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q V" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ys-RcHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_ys-RcXKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+G" command="_ysqviXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RcnKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+W" command="_ysr9xnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RdXKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q H" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ys-RdnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_ys-ReHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+K" command="_ysqvinKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RenKgEfC9doPhCnHTeg" keySequence="CTRL+," command="_ysqIhHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RfHKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q L" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ys-RfXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_ys-4cXKgEfC9doPhCnHTeg" keySequence="CTRL+." command="_yssk9nKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4d3KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+B" command="_ysqvi3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4fHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+E" command="_ysqvpnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fgnKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q X" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ys_fg3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_ys_fhHKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q Y" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ys_fhXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_ys_fh3KgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q Z" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ys_fiHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_ys_fjHKgEfC9doPhCnHTeg" keySequence="CTRL+P" command="_ysr90XKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fjXKgEfC9doPhCnHTeg" keySequence="CTRL+Q" command="_ysr95nKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fkXKgEfC9doPhCnHTeg" keySequence="CTRL+S" command="_ysskv3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_flnKgEfC9doPhCnHTeg" keySequence="CTRL+W" command="_ysslEXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fl3KgEfC9doPhCnHTeg" keySequence="CTRL+H" command="_ysr9pHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGkHKgEfC9doPhCnHTeg" keySequence="CTRL+K" command="_ysqvoXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGlHKgEfC9doPhCnHTeg" keySequence="CTRL+M" command="_ysr9nXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGl3KgEfC9doPhCnHTeg" keySequence="CTRL+N" command="_ysslDXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGmnKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+?" command="_ysr9kHKgEfC9doPhCnHTeg">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_ytAGnnKgEfC9doPhCnHTeg" keySequence="CTRL+B" command="_ysqIiHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGoHKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q B" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ytAGoXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_ytAtonKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+Q C" command="_ysqvl3KgEfC9doPhCnHTeg">
      <parameters xmi:id="_ytAto3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_ytAtpXKgEfC9doPhCnHTeg" keySequence="CTRL+E" command="_ysr9uHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtpnKgEfC9doPhCnHTeg" keySequence="CTRL+F" command="_ysqve3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtrHKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+W" command="_yssk8nKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtsXKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+N" command="_ysr9wnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtu3KgEfC9doPhCnHTeg" keySequence="CTRL+_" command="_ysr9lXKgEfC9doPhCnHTeg">
      <parameters xmi:id="_ytAtvHKgEfC9doPhCnHTeg" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_ytBUs3KgEfC9doPhCnHTeg" keySequence="CTRL+{" command="_ysr9lXKgEfC9doPhCnHTeg">
      <parameters xmi:id="_ytBUtHKgEfC9doPhCnHTeg" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_ytBUvHKgEfC9doPhCnHTeg" keySequence="SHIFT+F9" command="_ysr91HKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUwHKgEfC9doPhCnHTeg" keySequence="ALT+ARROW_LEFT" command="_ysqvZHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUw3KgEfC9doPhCnHTeg" keySequence="ALT+ARROW_RIGHT" command="_ysr93XKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUynKgEfC9doPhCnHTeg" keySequence="SHIFT+F5" command="_yssk7XKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUzHKgEfC9doPhCnHTeg" keySequence="ALT+F7" command="_ysqIf3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi0HKgEfC9doPhCnHTeg" keySequence="F9" command="_ysskoXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi0XKgEfC9doPhCnHTeg" keySequence="F11" command="_yssk1XKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi0nKgEfC9doPhCnHTeg" keySequence="F12" command="_ysr9qXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi1XKgEfC9doPhCnHTeg" keySequence="F2" command="_ysqIhnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi4XKgEfC9doPhCnHTeg" keySequence="F5" command="_yssknHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi5XKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+F7" command="_yssk1nKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi5nKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+F8" command="_ysr9k3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi6XKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+ARROW_LEFT" command="_ysr95nKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi6nKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+ARROW_RIGHT" command="_ysqvlXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi7HKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+F4" command="_ysr9xnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ4HKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+F6" command="_ysqvenKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ5XKgEfC9doPhCnHTeg" keySequence="CTRL+F7" command="_ysqIVnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ53KgEfC9doPhCnHTeg" keySequence="CTRL+F8" command="_ysrWrXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ83KgEfC9doPhCnHTeg" keySequence="CTRL+F4" command="_ysslEXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ9XKgEfC9doPhCnHTeg" keySequence="CTRL+F6" command="_ysqvgnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ9nKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+F7" command="_ysrWf3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw9HKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_ysqvdHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw9nKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_ysr9mHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw-HKgEfC9doPhCnHTeg" keySequence="DEL" command="_ysqvh3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDxAXKgEfC9doPhCnHTeg" keySequence="ALT+?" command="_ysr9kHKgEfC9doPhCnHTeg">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_ytEYCXKgEfC9doPhCnHTeg" keySequence="ALT+-" command="_ysqIa3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYDXKgEfC9doPhCnHTeg" keySequence="ALT+CR" command="_ysr9hXKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ys9qV3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_ysvA23KgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys9qWHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+P" command="_yssksHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4gHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+G" command="_ysskmnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi3XKgEfC9doPhCnHTeg" keySequence="F3" command="_ysr953KgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ys-RaHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_ysvA5XKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys-RaXKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+T" command="_ysr9j3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-RdHKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+H" command="_ysqvaXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4f3KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+G" command="_ysphRHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4gnKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+H" command="_ysrWrnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fgXKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+I" command="_ysqIhXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGnXKgEfC9doPhCnHTeg" keySequence="ALT+SHIFT+R" command="_ysr9sHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtqnKgEfC9doPhCnHTeg" keySequence="CTRL+G" command="_ysslAXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi23KgEfC9doPhCnHTeg" keySequence="F3" command="_ysslNnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi33KgEfC9doPhCnHTeg" keySequence="F4" command="_yssk2XKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ys-RbHKgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_ysvA0HKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys-RbXKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+V" command="_ysskr3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys-4e3KgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+C" command="_ysrWuXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUunKgEfC9doPhCnHTeg" keySequence="ALT+ARROW_UP" command="_ysphSHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUwnKgEfC9doPhCnHTeg" keySequence="ALT+ARROW_RIGHT" command="_yssk0nKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUx3KgEfC9doPhCnHTeg" keySequence="SHIFT+INSERT" command="_ysskr3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ7nKgEfC9doPhCnHTeg" keySequence="CTRL+INSERT" command="_ysrWuXKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ys-4cnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_ysvA43KgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys-4c3KgEfC9doPhCnHTeg" keySequence="CTRL+/" command="_ysqvnHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi13KgEfC9doPhCnHTeg" keySequence="F3" command="_ysqvbnKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ys_fiXKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_ysvA2nKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys_finKgEfC9doPhCnHTeg" keySequence="ALT+CTRL+M" command="_yssk13KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fi3KgEfC9doPhCnHTeg" keySequence="ALT+CTRL+N" command="_yssk5HKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_fk3KgEfC9doPhCnHTeg" keySequence="CTRL+T" command="_ysrWvHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ys_flXKgEfC9doPhCnHTeg" keySequence="CTRL+W" command="_ysqInnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGlnKgEfC9doPhCnHTeg" keySequence="CTRL+N" command="_ysqvf3KgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ys_fj3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.debugging" bindingContext="_ysvA3HKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ys_fkHKgEfC9doPhCnHTeg" keySequence="CTRL+R" command="_ysqIXHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUz3KgEfC9doPhCnHTeg" keySequence="F7" command="_yssk_XKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBU0HKgEfC9doPhCnHTeg" keySequence="F8" command="_ysqIlHKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi4HKgEfC9doPhCnHTeg" keySequence="F5" command="_ysqIlXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi4nKgEfC9doPhCnHTeg" keySequence="F6" command="_ysskzXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ8nKgEfC9doPhCnHTeg" keySequence="CTRL+F2" command="_ysr9rHKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ytAGkXKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_ysvA3XKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ytAGknKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+," command="_ysr96HKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAGlXKgEfC9doPhCnHTeg" keySequence="CTRL+SHIFT+." command="_ysr9mnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytAtq3KgEfC9doPhCnHTeg" keySequence="CTRL+G" command="_ysr9m3KgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ytAtp3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_ysvA4HKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ytAtqHKgEfC9doPhCnHTeg" keySequence="CTRL+G" command="_ysslCXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi43KgEfC9doPhCnHTeg" keySequence="HOME" command="_ysrWt3KgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ytAtuHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.console" bindingContext="_ysvA1HKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ytAtuXKgEfC9doPhCnHTeg" keySequence="CTRL+Z" command="_yssk8HKgEfC9doPhCnHTeg">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ytBUt3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_ysvA5nKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ytBUuHKgEfC9doPhCnHTeg" keySequence="SHIFT+F7" command="_ysqvbXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUuXKgEfC9doPhCnHTeg" keySequence="SHIFT+F8" command="_ysr9knKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUyXKgEfC9doPhCnHTeg" keySequence="SHIFT+F5" command="_ysskznKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUy3KgEfC9doPhCnHTeg" keySequence="SHIFT+F6" command="_ysqIiXKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDJ9HKgEfC9doPhCnHTeg" keySequence="CTRL+F5" command="_ysskynKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ytBUvnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_ysvA13KgEfC9doPhCnHTeg">
    <bindings xmi:id="_ytBUv3KgEfC9doPhCnHTeg" keySequence="ALT+ARROW_LEFT" command="_ysrWgnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytBUwXKgEfC9doPhCnHTeg" keySequence="ALT+ARROW_RIGHT" command="_ysrWnnKgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytCi3HKgEfC9doPhCnHTeg" keySequence="F3" command="_ysslNnKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ytCi2XKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_ysvA2XKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ytCi2nKgEfC9doPhCnHTeg" keySequence="F3" command="_ysslNnKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ytDJ8HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_ysvA2HKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ytDJ8XKgEfC9doPhCnHTeg" keySequence="CTRL+INSERT" command="_ysqvc3KgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ytDw8nKgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_ysvA4nKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ytDw83KgEfC9doPhCnHTeg" keySequence="ALT+Y" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw-XKgEfC9doPhCnHTeg" keySequence="ALT+A" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw-nKgEfC9doPhCnHTeg" keySequence="ALT+B" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw_HKgEfC9doPhCnHTeg" keySequence="ALT+C" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw_XKgEfC9doPhCnHTeg" keySequence="ALT+D" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw_nKgEfC9doPhCnHTeg" keySequence="ALT+E" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDw_3KgEfC9doPhCnHTeg" keySequence="ALT+F" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDxAHKgEfC9doPhCnHTeg" keySequence="ALT+G" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDxAnKgEfC9doPhCnHTeg" keySequence="ALT+P" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytDxA3KgEfC9doPhCnHTeg" keySequence="ALT+R" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYAHKgEfC9doPhCnHTeg" keySequence="ALT+S" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYAXKgEfC9doPhCnHTeg" keySequence="ALT+T" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYAnKgEfC9doPhCnHTeg" keySequence="ALT+V" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYA3KgEfC9doPhCnHTeg" keySequence="ALT+W" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYBHKgEfC9doPhCnHTeg" keySequence="ALT+H" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYB3KgEfC9doPhCnHTeg" keySequence="ALT+L" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
    <bindings xmi:id="_ytEYCHKgEfC9doPhCnHTeg" keySequence="ALT+N" command="_ysqIZ3KgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_ytEYBXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.context" bindingContext="_ysvA4XKgEfC9doPhCnHTeg">
    <bindings xmi:id="_ytEYBnKgEfC9doPhCnHTeg" keySequence="ALT+K" command="_ysrWcHKgEfC9doPhCnHTeg"/>
  </bindingTables>
  <bindingTables xmi:id="_zJIB4XKgEfC9doPhCnHTeg" bindingContext="_zJIB4HKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJIB43KgEfC9doPhCnHTeg" bindingContext="_zJIB4nKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJIB5XKgEfC9doPhCnHTeg" bindingContext="_zJIB5HKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJIo8XKgEfC9doPhCnHTeg" bindingContext="_zJIo8HKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJIo83KgEfC9doPhCnHTeg" bindingContext="_zJIo8nKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJIo9XKgEfC9doPhCnHTeg" bindingContext="_zJIo9HKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJIo93KgEfC9doPhCnHTeg" bindingContext="_zJIo9nKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJIo-XKgEfC9doPhCnHTeg" bindingContext="_zJIo-HKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJIo-3KgEfC9doPhCnHTeg" bindingContext="_zJIo-nKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJQAXKgEfC9doPhCnHTeg" bindingContext="_zJJQAHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJQA3KgEfC9doPhCnHTeg" bindingContext="_zJJQAnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJQBXKgEfC9doPhCnHTeg" bindingContext="_zJJQBHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJQB3KgEfC9doPhCnHTeg" bindingContext="_zJJQBnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJQCXKgEfC9doPhCnHTeg" bindingContext="_zJJQCHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJQC3KgEfC9doPhCnHTeg" bindingContext="_zJJQCnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJ3EXKgEfC9doPhCnHTeg" bindingContext="_zJJ3EHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJ3E3KgEfC9doPhCnHTeg" bindingContext="_zJJ3EnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJ3FXKgEfC9doPhCnHTeg" bindingContext="_zJJ3FHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJ3F3KgEfC9doPhCnHTeg" bindingContext="_zJJ3FnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJ3GXKgEfC9doPhCnHTeg" bindingContext="_zJJ3GHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJ3G3KgEfC9doPhCnHTeg" bindingContext="_zJJ3GnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJ3HXKgEfC9doPhCnHTeg" bindingContext="_zJJ3HHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJJ3H3KgEfC9doPhCnHTeg" bindingContext="_zJJ3HnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJKeIXKgEfC9doPhCnHTeg" bindingContext="_zJKeIHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJKeI3KgEfC9doPhCnHTeg" bindingContext="_zJKeInKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJKeJXKgEfC9doPhCnHTeg" bindingContext="_zJKeJHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJKeJ3KgEfC9doPhCnHTeg" bindingContext="_zJKeJnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJKeKXKgEfC9doPhCnHTeg" bindingContext="_zJKeKHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJKeK3KgEfC9doPhCnHTeg" bindingContext="_zJKeKnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJKeLXKgEfC9doPhCnHTeg" bindingContext="_zJKeLHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJKeL3KgEfC9doPhCnHTeg" bindingContext="_zJKeLnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJLFMXKgEfC9doPhCnHTeg" bindingContext="_zJLFMHKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJLFM3KgEfC9doPhCnHTeg" bindingContext="_zJLFMnKgEfC9doPhCnHTeg"/>
  <bindingTables xmi:id="_zJLFNXKgEfC9doPhCnHTeg" bindingContext="_zJLFNHKgEfC9doPhCnHTeg"/>
  <rootContext xmi:id="_ysPRmnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_ysPRm3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_ysPRnHKgEfC9doPhCnHTeg" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_ysvA0HKgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_ysvA0XKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_ysvA0nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_ysvA2XKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_ysvA23KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_ysvA43KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_ysvA5HKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_ysvA1HKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_ysvA1XKgEfC9doPhCnHTeg" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_ysvA2HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_ysvA2nKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_ysvA3HKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_ysvA3XKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_ysvA3nKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mpu.debug.ui.debugging" name="Debugging C/C++ on MPU - Cortex-M" description="Debugging C/C++ Programs on MPU - Cortex-M"/>
        <children xmi:id="_ysvA33KgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.ui.debugging" name="Debugging C/C++ on MCU" description="Debugging C/C++ Programs on MCU"/>
        <children xmi:id="_ysvA4HKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_ysvA5nKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_ysvA4nKgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_ysvA5XKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
    </children>
    <children xmi:id="_ysPRnXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_ysvA13KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_ysvA03KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_ysvA1nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_ysvA4XKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.context" name="Device Configuration Tool Context" description="Device Configuration Tool  Context"/>
  <rootContext xmi:id="_zJIB4HKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet" name="Auto::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet"/>
  <rootContext xmi:id="_zJIB4nKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.informationcenter.actionSet3" name="Auto::com.st.stm32cube.ide.mcu.informationcenter.actionSet3"/>
  <rootContext xmi:id="_zJIB5HKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_zJIo8HKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_zJIo8nKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_zJIo9HKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_zJIo9nKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_zJIo-HKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_zJIo-nKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_zJJQAHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_zJJQAnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_zJJQBHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_zJJQBnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_zJJQCHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_zJJQCnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_zJJ3EHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_zJJ3EnKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_zJJ3FHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_zJJ3FnKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_zJJ3GHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_zJJ3GnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_zJJ3HHKgEfC9doPhCnHTeg" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_zJJ3HnKgEfC9doPhCnHTeg" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_zJKeIHKgEfC9doPhCnHTeg" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_zJKeInKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_zJKeJHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_zJKeJnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_zJKeKHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_zJKeKnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_zJKeLHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_zJKeLnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_zJLFMHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_zJLFMnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_zJLFNHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_ywopYHKgEfC9doPhCnHTeg" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_zDPcUHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.views.OutputsView" label="Outputs" iconURI="platform:/plugin/com.st.stm32cube.common.mx/icons/MicroXplorer.png" tooltip="" category="Device Configuration Tool" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.common.mx.views.OutPutMxView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.common.mx"/>
    <tags>View</tags>
    <tags>categoryTag:Device Configuration Tool</tags>
  </descriptors>
  <descriptors xmi:id="_zDQqcHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.view" label="Build Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.buildanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="elf_analyzer.ElfAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.buildanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDRRgHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.view" label="Cyclomatic Complexity" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.cyclomaticcomplexity/icons/algorithm.png" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity.CyclomaticView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.cyclomaticcomplexity"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDR4kHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.logview" label="SWV Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.SWVLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_zDR4kXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.statisticalprofiling" label="SWV Statistical Profiling" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_statistical_profiling.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.statisticalprofiling.SWVStatisticalProfilingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_zDR4knKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.itmtrace" label="SWV ITM Data Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/console_view.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.itmtrace.SWVConsole"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_zDR4k3KgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.exceptionlogview" label="SWV Exception Trace Log" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/SWV_Exception_spreadsheet.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.exception.SWVExceptionLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_zDTGsHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceview" label="SWV Data Trace" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/insp_sbook.gif" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatrace.SWVDataTraceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_zDTGsXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.SWVDatatraceTimeline" label="SWV Data Trace Timeline Graph" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.debug.swv/icons/Datatrace_timeline.png" tooltip="" category="SWV" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.debug.swv.core.ui.datatraceTimeline.SWVDatatraceTimeline"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.debug.swv"/>
    <tags>View</tags>
    <tags>categoryTag:SWV</tags>
  </descriptors>
  <descriptors xmi:id="_zDTGsnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.faultanalyzer.view" label="&#x6545;&#x969c;&#x5206;&#x6790;&#x5668;" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.faultanalyzer/icons/clanbomber.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.faultanalyzer.FaultAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.faultanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDTtwHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.freertos.queues" label="FreeRTOS Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.queues.FORtosQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_zDUU0HKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.freertos.tasklist" label="FreeRTOS Task List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.tasklist.FORtosTaskList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_zDUU0XKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.freertos.semaphore" label="FreeRTOS Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.semaphores.FORtosSemaphores"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_zDUU0nKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.freertos.timers" label="FreeRTOS Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.freertos/icons/debugt_obj.gif" tooltip="" category="FreeRTOS" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.freertos.timers.FORtosTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.freertos"/>
    <tags>View</tags>
    <tags>categoryTag:FreeRTOS</tags>
  </descriptors>
  <descriptors xmi:id="_zDU74HKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView" label="&#x73b0;&#x573a;&#x8868;&#x8fbe;&#x5f0f;" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.livewatch/icons/watchlist_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.livewatch.LiveExpressionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.livewatch"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDU74XKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.sfrview" label="SFRs" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.sfrview/icons/memory_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.sfrview.ui.SfrView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.sfrview"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDVi8HKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.stackanalyzer.stackanalyzer.view" label="Static Stack Analyzer" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.stackanalyzer/icons/view_icon.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.stackanalyzer.ui.StackAnalyzerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.stackanalyzer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDWKAHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.tcp.console.view" label="TCP Console" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.tcp.console/icons/console.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.tcp.console.ui.TCPConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.tcp.console"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_zDWKAXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.threadx.threads" label="ThreadX Thread List" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.threadlist.ThreadXThreadList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_zDWxEHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.threadx.semaphores" label="ThreadX Semaphores" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.semaphores.ThreadXSemaphoreList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_zDWxEXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.threadx.mutexes" label="ThreadX Mutexes" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.mutexes.ThreadXMutexList"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_zDXYIHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.threadx.queues" label="ThreadX Message Queues" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.queues.ThreadXMessageQueues"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_zDXYIXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.threadx.eventflags" label="ThreadX Event Flags" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.eventflags.ThreadXEventFlags"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_zDXYInKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.threadx.timer" label="ThreadX Timers" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.timers.ThreadXTimers"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_zDXYI3KgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.threadx.blockpools" label="ThreadX Memory Block Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.blockpools.ThreadXMemoryBlockPools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_zDX_MHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.threadx.bytepools" label="ThreadX Memory Byte Pools" iconURI="platform:/plugin/com.st.stm32cube.ide.mcu.threadx/icons/debugt_obj.gif" tooltip="" category="ThreadX" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mcu.threadx.bytepools.ThreadXMemoryBytePools"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mcu.threadx"/>
    <tags>View</tags>
    <tags>categoryTag:ThreadX</tags>
  </descriptors>
  <descriptors xmi:id="_zDX_MXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mpu.flashmemory.programmer.view" label="Flash Memories Programmer" iconURI="platform:/plugin/com.st.stm32cube.ide.mpu.flashmemory.programmer/icons/soc.png" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.st.stm32cube.ide.mpu.flashmemory.programmer.views.FlashMemoryProgrammerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.st.stm32cube.ide.mpu.flashmemory.programmer"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDX_MnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDYmQHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDZNUHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDZNUXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDZNUnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDZ0YHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDZ0YXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDabcHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDabcXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDabcnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_zDbCgHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDbpkHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDbpkXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDbpknKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDcQoHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_zDcQoXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDjlYHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDkMcHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDkMcXKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDkMcnKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDkMc3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDkzgHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDkzgXKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDkzgnKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_zDkzg3KgEfC9doPhCnHTeg" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_zDlakHKgEfC9doPhCnHTeg" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_zDmBoHKgEfC9doPhCnHTeg" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDmosHKgEfC9doPhCnHTeg" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_zDmosXKgEfC9doPhCnHTeg" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_zDmosnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDn20HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDn20XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_zDod4HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDpE8HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDpsAHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDpsAXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDpsAnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDqTEHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDqTEXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDq6IHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDq6IXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDsIQHKgEfC9doPhCnHTeg" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_zDsIQXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_ysphQHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphQXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphQnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphQ3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphRHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphRXKgEfC9doPhCnHTeg" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_ysnsJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphRnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphR3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphSHKgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_ysnsJHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphSXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphSnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphS3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_ysnsI3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysphTHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIUHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_ysnsHXKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysqIUXKgEfC9doPhCnHTeg" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_ysqIUnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIU3KgEfC9doPhCnHTeg" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_ysoTJnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysqIVHKgEfC9doPhCnHTeg" elementId="trigger" name="trigger"/>
  </commands>
  <commands xmi:id="_ysqIVXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIVnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIV3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_ysnsEnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysqIWHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_ysqIWXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIWnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIW3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIXHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIXXKgEfC9doPhCnHTeg" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_ysoTInKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIXnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIX3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIYHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIYXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIYnKgEfC9doPhCnHTeg" elementId="org.eclipse.epp.mpc.ui.command.showInstalled" commandName="Manage installed plug-ins" description="Update or uninstall plug-ins installed from the Marketplace" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIY3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIZHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIZXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIZnKgEfC9doPhCnHTeg" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIZ3KgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_ysnsJHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIaHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIaXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_ysnsI3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIanKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIa3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIbHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIbXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIbnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_ysoTJHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIb3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_ysoTJ3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIcHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIcXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIcnKgEfC9doPhCnHTeg" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_ysnsG3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIc3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIdHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_ysnsH3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIdXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIdnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqId3KgEfC9doPhCnHTeg" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIeHKgEfC9doPhCnHTeg" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_ysnsGXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIeXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mpu.linux.ide.command.setupopenstlinux" commandName="Setup OpenSTLinux" description="Setup OpenSTLinux" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIenKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIe3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIfHKgEfC9doPhCnHTeg" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_ysnsG3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIfXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIfnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIf3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIgHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIgXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIgnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIg3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_ysnsH3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIhHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIhXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIhnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIh3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIiHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIiXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_ysoTJXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIinKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIi3KgEfC9doPhCnHTeg" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_ysnsHHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIjHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIjXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_ysnsFnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIjnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.ide.command.generatecode" commandName="Generate Code" description="Generate Code (based on .ioc file content)" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIj3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIkHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIkXKgEfC9doPhCnHTeg" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIknKgEfC9doPhCnHTeg" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_ysoTI3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIk3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_ysoTKHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIlHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIlXKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIlnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIl3KgEfC9doPhCnHTeg" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqImHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqImXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqImnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIm3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqInHKgEfC9doPhCnHTeg" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_ysoTJnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysqInXKgEfC9doPhCnHTeg" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_ysqInnKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqIn3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvYHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvYXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvYnKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvY3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvZHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvZXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvZnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvZ3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvaHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvaXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvanKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqva3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvbHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvbXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_ysoTJXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvbnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_ysnsJXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvb3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvcHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvcXKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_ysoTInKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvcnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartConfigurationCommand" commandName="Restart Configuration Command" category="_ysnsHnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvc3KgEfC9doPhCnHTeg" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_ysnsGXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvdHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvdXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvdnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.externaltools.test" commandName="Test ExternalTools" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvd3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqveHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqveXKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvenKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqve3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvfHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvfXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvfnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvf3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvgHKgEfC9doPhCnHTeg" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvgXKgEfC9doPhCnHTeg" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_ysnsEHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvgnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvg3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvhHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvhXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.common.ui.view_export" commandName="Export view data to file" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvhnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvh3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqviHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqviXKgEfC9doPhCnHTeg" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_ysnsJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvinKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvi3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvjHKgEfC9doPhCnHTeg" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_ysnsJ3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvjXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvjnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_ysoTJXKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysqvj3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_ysqvkHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvkXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvknKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvk3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvlHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvlXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvlnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvl3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_ysnsEXKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysqvmHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_ysqvmXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_ysqvmnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_ysqvm3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_ysnsH3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvnHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_ysnsJXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvnXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_ysnsGHKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysqvnnKgEfC9doPhCnHTeg" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_ysqvn3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvoHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvoXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvonKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvo3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvpHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_ysnsGnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysqvpXKgEfC9doPhCnHTeg" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_ysqvpnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvp3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvqHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvqXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvqnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvq3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvrHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysqvrXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_ysnsIHKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysqvrnKgEfC9doPhCnHTeg" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_ysrWcHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.menu.generatecode" commandName="Generate Code" description="Generate Code" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWcXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWcnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWc3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWdHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWdXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWdnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.datarefresh" commandName="Data Refresh" description="Data Refresh" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWd3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWeHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWeXKgEfC9doPhCnHTeg" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWenKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWe3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWfHKgEfC9doPhCnHTeg" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_ysnsJ3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWfXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_ysnsH3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWfnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mpu.flashmemory.programmer.command" commandName="Flash Memory Programmer Command" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWf3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWgHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWgXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showInCommand" commandName="name" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWgnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWg3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWhHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWhXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWhnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWh3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWiHKgEfC9doPhCnHTeg" elementId="org.eclipse.epp.mpc.ui.command.importFavoritesWizard" commandName="Import Marketplace Favorites" description="Import another user's Marketplace Favorites List" category="_ysoTJnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysrWiXKgEfC9doPhCnHTeg" elementId="favoritesUrl" name="favoritesUrl"/>
  </commands>
  <commands xmi:id="_ysrWinKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWi3KgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.pintopincompatibility" commandName="Pin to pin compatibility" description="Pin to pin compatibility" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWjHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWjXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_ysnsI3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWjnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_ysnsFnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWj3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_ysnsF3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWkHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWkXKgEfC9doPhCnHTeg" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_ysnsJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWknKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWk3KgEfC9doPhCnHTeg" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_ysnsJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWlHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWlXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWlnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWl3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWmHKgEfC9doPhCnHTeg" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWmXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWmnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.buildanalyzer.showstate" commandName="name" category="_ysoTJnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysrWm3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_ysrWnHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWnXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWnnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWn3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWoHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.docsandresources" commandName="Docs And Resources" description="Docs And Resources" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWoXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_ysnsIHKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysrWonKgEfC9doPhCnHTeg" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_ysrWo3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWpHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWpXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWpnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWp3KgEfC9doPhCnHTeg" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWqHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWqXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_ysnsFnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWqnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWq3KgEfC9doPhCnHTeg" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWrHKgEfC9doPhCnHTeg" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_ysnsJ3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWrXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWrnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWr3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWsHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.generatereport" commandName="Generate Report" description="Generate Report" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWsXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWsnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWs3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWtHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWtXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWtnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWt3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWuHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWuXKgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_ysnsJHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWunKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWu3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysrWvHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9gHKgEfC9doPhCnHTeg" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_ysnsG3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9gXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.informationcenter.tutorialvideo" commandName="Tutorial Video" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9gnKgEfC9doPhCnHTeg" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_ysoTI3KgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysr9g3KgEfC9doPhCnHTeg" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_ysr9hHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9hXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9hnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9h3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_ysnsH3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9iHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_ysnsHXKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysr9iXKgEfC9doPhCnHTeg" elementId="url" name="URL"/>
    <parameters xmi:id="_ysr9inKgEfC9doPhCnHTeg" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_ysr9i3KgEfC9doPhCnHTeg" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_ysr9jHKgEfC9doPhCnHTeg" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_ysr9jXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9jnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9j3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9kHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9kXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9knKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_ysoTJXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9k3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9lHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.toolbar.generatecode" commandName="Generate Code" description="Generate Code" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9lXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_ysnsHXKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysr9lnKgEfC9doPhCnHTeg" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_ysr9l3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9mHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9mXKgEfC9doPhCnHTeg" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_ysnsG3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9mnKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9m3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9nHKgEfC9doPhCnHTeg" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9nXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9nnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9n3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9oHKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.start_trace" commandName="Start Trace" description="Start Trace" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9oXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9onKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9o3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9pHKgEfC9doPhCnHTeg" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_ysnsJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9pXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.modifyIncludePathsBySelection" commandName="Add/remove include path..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9pnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9p3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9qHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9qXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9qnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9q3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9rHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9rXKgEfC9doPhCnHTeg" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9rnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9r3KgEfC9doPhCnHTeg" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_ysnsG3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9sHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_ysnsH3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9sXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_ysoTKHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9snKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9s3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9tHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9tXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_ysoTInKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysr9tnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_ysr9t3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_ysr9uHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9uXKgEfC9doPhCnHTeg" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_ysnsGXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9unKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9u3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9vHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9vXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9vnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9v3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9wHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9wXKgEfC9doPhCnHTeg" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_ysnsG3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9wnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9w3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9xHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9xXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9xnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9x3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9yHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9yXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9ynKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9y3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9zHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9zXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr9znKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_ysnsGnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysr9z3KgEfC9doPhCnHTeg" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_ysr90HKgEfC9doPhCnHTeg" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_ysoTI3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr90XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr90nKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp" commandName="Convert Project to C or CPP" category="_ysoTJnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysr903KgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.build.ui.commands.convert2ccpp.type" name="Convert Type (C/C++)"/>
  </commands>
  <commands xmi:id="_ysr91HKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr91XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr91nKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr913KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr92HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr92XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr92nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr923KgEfC9doPhCnHTeg" elementId="org.eclipse.epp.mpc.ui.command.showFavorites" commandName="Eclipse Marketplace Favorites" description="Open Marketplace Favorites" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr93HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr93XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr93nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr933KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr94HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr94XKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.ide.connectionToMyST" commandName="Connection to myST" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr94nKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mpu.remote.serial.connectconsole" commandName="Connect Console Command" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr943KgEfC9doPhCnHTeg" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_ysnsJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr95HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr95XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr95nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr953KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr96HKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr96XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr96nKgEfC9doPhCnHTeg" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_ysnsJ3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr963KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysr97HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskkHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_ysoTIXKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysskkXKgEfC9doPhCnHTeg" elementId="title" name="Title"/>
    <parameters xmi:id="_ysskknKgEfC9doPhCnHTeg" elementId="message" name="Message"/>
    <parameters xmi:id="_ysskk3KgEfC9doPhCnHTeg" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_yssklHKgEfC9doPhCnHTeg" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_yssklXKgEfC9doPhCnHTeg" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_yssklnKgEfC9doPhCnHTeg" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_ysskl3KgEfC9doPhCnHTeg" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_ysskmHKgEfC9doPhCnHTeg" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_ysskmXKgEfC9doPhCnHTeg" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_ysskmnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskm3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssknHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssknXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssknnKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskn3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskoHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskoXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskonKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssko3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskpHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskpXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskpnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskp3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_ysoTKHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskqHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_ysnsI3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskqXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskqnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskq3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskrHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskrXKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskrnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.launch.command.restartCommand" commandName="Restart Command" category="_ysnsHnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskr3KgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_ysnsJHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssksHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssksXKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.checkforupdates" commandName="Check For Embedded Software Packages Updates" description="Check For Embedded Software Packages Updates" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssksnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssks3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssktHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_ysnsGHKgEfC9doPhCnHTeg">
    <parameters xmi:id="_yssktXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_yssktnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskt3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskuHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_ysoTJnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysskuXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_ysskunKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_yssku3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskvHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskvXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskvnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskv3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskwHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_ysoTKHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskwXKgEfC9doPhCnHTeg" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_ysnsEHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskwnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_ysoTJ3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskw3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskxHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskxXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskxnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskx3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskyHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskyXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskynKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssky3KgEfC9doPhCnHTeg" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskzHKgEfC9doPhCnHTeg" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_ysnsEHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskzXKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskznKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_ysoTJXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysskz3KgEfC9doPhCnHTeg" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_ysnsG3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk0HKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.threadx.trx_to_file_command" commandName="Export trace" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk0XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk0nKgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_ysnsJHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk03KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_ysnsGnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_yssk1HKgEfC9doPhCnHTeg" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_yssk1XKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk1nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk13KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk2HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk2XKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk2nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk23KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_ysnsFnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk3HKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk3XKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk3nKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.cmake.cmake_run_builder" commandName="cmake_run_builder" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk33KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk4HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk4XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk4nKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.swv.core.openconfig" commandName="Config" description="Configure SWV" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk43KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_ysoTJHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk5HKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk5XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk5nKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_ysnsIXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk53KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_ysnsH3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk6HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk6XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk6nKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk63KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk7HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk7XKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk7nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk73KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk8HKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk8XKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_ysoTKHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk8nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk83KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk9HKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.stlink.fwupgrade" commandName="ST-LINK &#x66f4;&#x65b0;" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk9XKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk9nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk93KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_ysnsHXKgEfC9doPhCnHTeg">
    <parameters xmi:id="_yssk-HKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_yssk-XKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_ysnsIHKgEfC9doPhCnHTeg">
    <parameters xmi:id="_yssk-nKgEfC9doPhCnHTeg" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_yssk-3KgEfC9doPhCnHTeg" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_yssk_HKgEfC9doPhCnHTeg" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_yssk_XKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_yssk_nKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_ysnsHXKgEfC9doPhCnHTeg">
    <parameters xmi:id="_yssk_3KgEfC9doPhCnHTeg" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_ysslAHKgEfC9doPhCnHTeg" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_ysslAXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslAnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.cmake.commands.cmakeimport" commandName="cmakeimport" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslA3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_ysnsIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslBHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_ysoTJHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslBXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslBnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslB3KgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.p2.list" commandName="P2 IU List" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslCHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_ysnsH3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslCXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslCnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslC3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_ysnsInKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslDHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslDXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_ysnsGnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysslDnKgEfC9doPhCnHTeg" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_ysslD3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslEHKgEfC9doPhCnHTeg" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslEXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_ysnsGnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslEnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslE3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslFHKgEfC9doPhCnHTeg" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslFXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslFnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslF3KgEfC9doPhCnHTeg" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslGHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslGXKgEfC9doPhCnHTeg" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_ysoTJnKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysslGnKgEfC9doPhCnHTeg" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_ysslG3KgEfC9doPhCnHTeg" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_ysslHHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_ysnsGHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslHXKgEfC9doPhCnHTeg" elementId="org.eclipse.userstorage.ui.showPullDown" commandName="Show Pull Down Menu" category="_ysnsIHKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysslHnKgEfC9doPhCnHTeg" elementId="intoolbar" name="In Tool Bar" optional="false"/>
  </commands>
  <commands xmi:id="_ysslH3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_ysnsGHKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysslIHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_ysslIXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_ysoTIXKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysslInKgEfC9doPhCnHTeg" elementId="title" name="Title"/>
    <parameters xmi:id="_ysslI3KgEfC9doPhCnHTeg" elementId="message" name="Message"/>
    <parameters xmi:id="_ysslJHKgEfC9doPhCnHTeg" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_ysslJXKgEfC9doPhCnHTeg" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_ysslJnKgEfC9doPhCnHTeg" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_ysnsG3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslJ3KgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslKHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslKXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_ysoTJ3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslKnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslK3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslLHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslLXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslLnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_ysnsEnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslL3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_ysoTIHKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslMHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_ysnsFXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslMXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_ysnsHXKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslMnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslM3KgEfC9doPhCnHTeg" elementId="com.st.stm32cube.common.mx.manageembeddedsoftwarepackages" commandName="Manage Embedded Software Packages" description="Manage Embedded Software Packages" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslNHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_ysnsGHKgEfC9doPhCnHTeg">
    <parameters xmi:id="_ysslNXKgEfC9doPhCnHTeg" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_ysslNnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_ysnsE3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_ysslN3KgEfC9doPhCnHTeg" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_ysnsG3KgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCoYUHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.debugActionSet/com.st.stm32cube.ide.mcu.debug.dsf.oss.ui.action.TerminateAndRelaunch" commandName="&#x7ec8;&#x6b62;&#x5e76;&#x91cd;&#x65b0;&#x542f;&#x52a8;" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCqNgHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::com.st.stm32cube.ide.mcu.informationcenter.actionSet3/com.st.stm32cube.ide.mcu.informationcenter.action1" commandName="Information Center" description="Information Center" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCq0kHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCq0kXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCq0knKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCrboHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCsCsHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCspwHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCspwXKgEfC9doPhCnHTeg" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCspwnKgEfC9doPhCnHTeg" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCspw3KgEfC9doPhCnHTeg" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCtQ0HKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCtQ0XKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCtQ0nKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCtQ03KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCtQ1HKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCtQ1XKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCtQ1nKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCt34HKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCt34XKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCt34nKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCt343KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCt35HKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCt35XKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCt35nKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCt353KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCue8HKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCue8XKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCue8nKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCue83KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCue9HKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCue9XKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvGAHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvGAXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvGAnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvGA3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvGBHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvGBXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvtEHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvtEXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvtEnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvtE3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvtFHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCvtFXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCwUIHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCwUIXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCwUInKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCwUI3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCwUJHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCwUJXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCwUJnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCwUJ3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCwUKHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCxiQHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCxiQXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCxiQnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCxiQ3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCxiRHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCyJUHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCyJUXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCyJUnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCyJU3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCyJVHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCyJVXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCyJVnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCyJV3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCywYHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCywYXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCywYnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCywY3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCywZHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCywZXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCywZnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCzXcHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCzXcXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCzXcnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCzXc3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCzXdHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCzXdXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCzXdnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCzXd3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCzXeHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCz-gHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCz-gXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCz-gnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCz-g3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCz-hHKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCz-hXKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCz-hnKgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <commands xmi:id="_zCz-h3KgEfC9doPhCnHTeg" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_ysoTJnKgEfC9doPhCnHTeg"/>
  <addons xmi:id="_ysPRnnKgEfC9doPhCnHTeg" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_ysPRn3KgEfC9doPhCnHTeg" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_ysPRoHKgEfC9doPhCnHTeg" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_ysPRoXKgEfC9doPhCnHTeg" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_ysPRonKgEfC9doPhCnHTeg" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_ysPRo3KgEfC9doPhCnHTeg" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_ysPRpHKgEfC9doPhCnHTeg" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_ysPRpXKgEfC9doPhCnHTeg" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_ysPRpnKgEfC9doPhCnHTeg" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_ysPRp3KgEfC9doPhCnHTeg" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_ysX0cHKgEfC9doPhCnHTeg" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_IYS0IKimEeS11vbz3f9ezw" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_ysnsEHKgEfC9doPhCnHTeg" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_ysnsEXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_ysnsEnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_ysnsE3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_ysnsFHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_ysnsFXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_ysnsFnKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_ysnsF3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_ysnsGHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_ysnsGXKgEfC9doPhCnHTeg" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_ysnsGnKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_ysnsG3KgEfC9doPhCnHTeg" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_ysnsHHKgEfC9doPhCnHTeg" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_ysnsHXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_ysnsHnKgEfC9doPhCnHTeg" elementId="com.st.stm32cube.ide.mcu.debug.launch.restartCategory" name="Restart Category"/>
  <categories xmi:id="_ysnsH3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_ysnsIHKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_ysnsIXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_ysnsInKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_ysnsI3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_ysnsJHKgEfC9doPhCnHTeg" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_ysnsJXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_ysnsJnKgEfC9doPhCnHTeg" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_ysnsJ3KgEfC9doPhCnHTeg" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_ysoTIHKgEfC9doPhCnHTeg" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_ysoTIXKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_ysoTInKgEfC9doPhCnHTeg" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_ysoTI3KgEfC9doPhCnHTeg" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_ysoTJHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_ysoTJXKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_ysoTJnKgEfC9doPhCnHTeg" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_ysoTJ3KgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_ysoTKHKgEfC9doPhCnHTeg" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
