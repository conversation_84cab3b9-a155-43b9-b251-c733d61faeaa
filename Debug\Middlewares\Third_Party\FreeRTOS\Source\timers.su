../Middlewares/Third_Party/FreeRTOS/Source/timers.c:941:13:prvCheckForValidListAndQueue	32	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:227:12:xTimerCreateTimerTask	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:282:16:xTimerCreate	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:309:16:xTimerCreateStatic	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:381:12:xTimerGenericCommand	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:882:13:prvSwitchTimerLists	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:548:8:prvTimerTask	72	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:424:14:xTimerGetTimerDaemonTaskHandle	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:433:12:xTimerGetPeriod	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:442:6:vTimerSetReloadMode	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:462:13:uxTimerGetReloadMode	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:487:12:xTimerGetExpiryTime	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:498:14:pcTimerGetName	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:992:12:xTimerIsTimerActive	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:1017:7:pvTimerGetTimerID	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:1034:6:vTimerSetTimerID	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:1050:13:xTimerPendFunctionCallFromISR	32	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:1074:13:xTimerPendFunctionCall	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:1103:14:uxTimerGetTimerNumber	0	static
../Middlewares/Third_Party/FreeRTOS/Source/timers.c:1113:7:vTimerSetTimerNumber	0	static
