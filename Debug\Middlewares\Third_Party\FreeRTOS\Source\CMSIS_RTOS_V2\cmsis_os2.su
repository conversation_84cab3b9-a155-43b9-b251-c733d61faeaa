../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:929:13:TimerCallback	8	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:198:12:osKernelInitialize	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:222:12:osKernelGetInfo	8	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:240:17:osKernelGetState	8	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:265:12:osKernelStart	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:288:9:osKernelLock	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:315:9:osKernelUnlock	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:347:9:osKernelRestoreLock	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:383:10:osKernelGetTickCount	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:395:10:osKernelGetTickFreq	0	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:415:10:osKernelGetSysTimerCount	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:438:10:osKernelGetSysTimerFreq	0	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:444:14:osThreadNew	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:512:13:osThreadGetName	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:525:14:osThreadGetId	0	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:533:17:osThreadGetState	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:555:10:osThreadGetStackSpace	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:568:12:osThreadSetPriority	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:586:14:osThreadGetPriority	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:599:12:osThreadYield	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:613:12:osThreadSuspend	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:631:12:osThreadResume	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:650:18:osThreadExit	8	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:657:12:osThreadTerminate	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:686:10:osThreadGetCount	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:699:10:osThreadEnumerate	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:729:10:osThreadFlagsSet	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:757:10:osThreadFlagsClear	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:787:10:osThreadFlagsGet	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:805:10:osThreadFlagsWait	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:881:12:osDelay	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:898:12:osDelayUntil	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:939:13:osTimerNew	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1005:13:osTimerGetName	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1018:12:osTimerStart	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1039:12:osTimerStop	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1065:10:osTimerIsRunning	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1078:12:osTimerDelete	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1110:18:osEventFlagsNew	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1150:10:osEventFlagsSet	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1181:10:osEventFlagsClear	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1207:10:osEventFlagsGet	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1224:10:osEventFlagsWait	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1274:12:osEventFlagsDelete	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1299:13:osMutexNew	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1386:12:osMutexAcquire	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1429:12:osMutexRelease	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1464:14:osMutexGetOwner	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1479:12:osMutexDelete	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1509:17:osSemaphoreNew	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1584:12:osSemaphoreAcquire	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1621:12:osSemaphoreRelease	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1649:10:osSemaphoreGetCount	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1665:12:osSemaphoreDelete	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1693:20:osMessageQueueNew	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1750:12:osMessageQueuePut	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1791:12:osMessageQueueGet	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1832:10:osMessageQueueGetCapacity	0	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1846:10:osMessageQueueGetMsgSize	0	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1860:10:osMessageQueueGetCount	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1877:10:osMessageQueueGetSpace	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1900:12:osMessageQueueReset	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1918:12:osMessageQueueDelete	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:1952:18:osMemoryPoolNew	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2069:13:osMemoryPoolGetName	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2086:7:osMemoryPoolAlloc	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2143:12:osMemoryPoolFree	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2205:10:osMemoryPoolGetCapacity	0	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2229:10:osMemoryPoolGetBlockSize	0	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2253:10:osMemoryPoolGetCount	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2283:10:osMemoryPoolGetSpace	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2311:12:osMemoryPoolDelete	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2459:13:vApplicationGetIdleTaskMemory	4	static
../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c:2473:13:vApplicationGetTimerTaskMemory	4	static
