2025-08-06 16:39:01,559 [INFO] Activator:176 - 


2025-08-06 16:39:01,560 [INFO] Activator:177 - !SESSION log4j initialized
2025-08-06 16:39:03,956 [INFO] LogOutputStream:77 - [STDOUT_REDIRECT] 
2025-08-06 16:39:06,205 [INFO] ApplicationProperties:184 - Using Application install path: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659
2025-08-06 16:39:06,222 [INFO] DbMcusXml:78 - Set database path to: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/mcu/
2025-08-06 16:39:06,224 [INFO] ApiDb:274 - Set plugin database path to: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/boardmanager/
2025-08-06 16:39:06,225 [WARN] ApiDb:259 - Overriding images path with different value:  => D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/mcufinder/images/
2025-08-06 16:39:06,230 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-08-06 16:39:06,232 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-08-06 16:39:06,234 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-08-06 16:39:06,364 [INFO] RulesReader:64 - Compatibility file has been processed (317 Rules)
2025-08-06 16:39:06,455 [INFO] DbMcusXml:78 - Set database path to: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/mcu/
2025-08-06 16:39:06,455 [INFO] ApiDb:274 - Set plugin database path to: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/boardmanager/
2025-08-06 16:39:06,455 [INFO] ApiDb:261 - Set plugin images path to: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/mcufinder/images/
2025-08-06 16:39:06,455 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-08-06 16:39:06,455 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-08-06 16:39:06,455 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-08-06 16:39:06,455 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-08-06 16:39:06,456 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-08-06 16:39:06,456 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-08-06 16:39:06,456 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-08-06 16:39:06,520 [INFO] MainPanel:274 - HeapMemory: 268435456
2025-08-06 16:39:06,606 [INFO] DbMcusXml:78 - Set database path to: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/mcu/
2025-08-06 16:39:06,606 [INFO] ApiDb:274 - Set plugin database path to: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/boardmanager/
2025-08-06 16:39:06,606 [INFO] ApiDb:261 - Set plugin images path to: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659\\db\/plugins/mcufinder/images/
2025-08-06 16:39:06,607 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-08-06 16:39:06,607 [INFO] ApiDb:250 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-08-06 16:39:06,607 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-08-06 16:39:06,607 [INFO] DbMcusAds:125 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/
2025-08-06 16:39:06,607 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-08-06 16:39:06,607 [WARN] DbFile:41 - Overriding database path with different value: C:\Users\<USER>\.stmcufinder\plugins\mcufinder/ => C:\Users\<USER>\.stmcufinder\plugins\mcufinder
2025-08-06 16:39:06,607 [INFO] CrossReferenceDbSqlite:203 - Set database path to: C:\Users\<USER>\.stmcufinder\plugins\mcufinder//mcu/cs/
2025-08-06 16:39:06,625 [INFO] ApplicationProperties:184 - Using Application install path: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659
2025-08-06 16:39:06,626 [INFO] PluginManage:196 - Search for loadable plugins [exclusion list=, ]
2025-08-06 16:39:06,627 [INFO] PluginManage:310 - Check plugin analytics
2025-08-06 16:39:06,903 [INFO] AnalyticsPlugin:253 - Accepted Software Licenses: STM32CubeMX.6.12.1,STM32CubeMX.6.15.0
2025-08-06 16:39:06,903 [INFO] AnalyticsPlugin:255 - Accepted CMSIS Pack Licenses: 
2025-08-06 16:39:06,903 [INFO] AnalyticsPlugin:257 - Accepted Firmware Licenses: FW.F4.1.28.0
2025-08-06 16:39:06,907 [INFO] PluginManage:359 - Loaded plugin analytics (category:tool,tabindex:-1)
2025-08-06 16:39:06,908 [INFO] PluginManage:310 - Check plugin cadmodel
2025-08-06 16:39:06,913 [INFO] CADModel:105 - Init CAD model plugin
2025-08-06 16:39:06,914 [INFO] PluginManage:359 - Loaded plugin cadmodel (category:power,tabindex:5)
2025-08-06 16:39:06,914 [INFO] PluginManage:310 - Check plugin clock
2025-08-06 16:39:06,925 [INFO] PluginManage:359 - Loaded plugin clock (category:base,tabindex:2)
2025-08-06 16:39:06,925 [INFO] PluginManage:310 - Check plugin ddr
2025-08-06 16:39:06,929 [INFO] PluginManage:359 - Loaded plugin ddr (category:tool,tabindex:6)
2025-08-06 16:39:06,930 [INFO] PluginManage:310 - Check plugin filemanager
2025-08-06 16:39:07,082 [INFO] PluginManage:359 - Loaded plugin filemanager (category:base,tabindex:10)
2025-08-06 16:39:07,082 [INFO] PluginManage:310 - Check plugin ipmanager
2025-08-06 16:39:07,090 [INFO] PluginManage:359 - Loaded plugin ipmanager (category:base,tabindex:5)
2025-08-06 16:39:07,090 [INFO] PluginManage:310 - Check plugin lpbam
2025-08-06 16:39:07,098 [INFO] PluginManage:359 - Loaded plugin lpbam (category:base,tabindex:0)
2025-08-06 16:39:07,098 [INFO] PluginManage:310 - Check plugin memorymap
2025-08-06 16:39:07,113 [INFO] PluginManage:359 - Loaded plugin memorymap (category:base,tabindex:4)
2025-08-06 16:39:07,113 [INFO] PluginManage:310 - Check plugin pinoutandconfiguration
2025-08-06 16:39:07,120 [INFO] PluginManage:359 - Loaded plugin pinoutandconfiguration (category:base,tabindex:1)
2025-08-06 16:39:07,120 [INFO] PluginManage:310 - Check plugin pinoutconfig
2025-08-06 16:39:07,219 [WARN] SupportedApi:132 - Cannot load RTOS API schema: s4s-elt-must-match.1: The content of 'definitions' must match (annotation?, (simpleType | complexType)?, (unique | key | keyref)*)). A problem was found starting at: attribute.
2025-08-06 16:39:07,365 [INFO] PluginManage:359 - Loaded plugin pinoutconfig (category:base,tabindex:0)
2025-08-06 16:39:07,365 [INFO] PluginManage:310 - Check plugin power
2025-08-06 16:39:07,372 [INFO] PluginManage:359 - Loaded plugin power (category:power,tabindex:4)
2025-08-06 16:39:07,374 [INFO] PluginManage:310 - Check plugin projectmanager
2025-08-06 16:39:07,393 [INFO] PluginManage:359 - Loaded plugin projectmanager (category:projectmanager,tabindex:4)
2025-08-06 16:39:07,394 [INFO] PluginManage:310 - Check plugin rif
2025-08-06 16:39:07,405 [INFO] PluginManage:359 - Loaded plugin rif (category:base,tabindex:3)
2025-08-06 16:39:07,406 [INFO] PluginManage:310 - Check plugin thirdparty
2025-08-06 16:39:07,535 [INFO] PluginManage:359 - Loaded plugin thirdparty (category:base,tabindex:-1)
2025-08-06 16:39:07,536 [INFO] PluginManage:310 - Check plugin tools
2025-08-06 16:39:07,535 [WARN] IntegrityCheckThread:84 - waiting for thirdparty lock release [integrity check]
2025-08-06 16:39:07,536 [INFO] IntegrityCheckThread:86 - entering critical section [integrity check]
2025-08-06 16:39:07,536 [INFO] ThirdPartyUpdaterWithRetryManager:70 - Updater plugin not ready yet. [1/15]
2025-08-06 16:39:07,538 [INFO] PluginManage:359 - Loaded plugin tools (category:base,tabindex:7)
2025-08-06 16:39:07,538 [INFO] PluginManage:310 - Check plugin tutovideos
2025-08-06 16:39:07,698 [INFO] PluginManage:359 - Loaded plugin tutovideos (category:base,tabindex:-1)
2025-08-06 16:39:07,699 [INFO] PluginManage:310 - Check plugin updater
2025-08-06 16:39:07,717 [INFO] PluginManage:359 - Loaded plugin updater (category:base,tabindex:12)
2025-08-06 16:39:07,717 [INFO] PluginManage:310 - Check plugin userauth
2025-08-06 16:39:07,720 [INFO] UserAuth:118 - Init User Auth plugin
2025-08-06 16:39:07,721 [INFO] PluginManage:359 - Loaded plugin userauth (category:base,tabindex:14)
2025-08-06 16:39:07,722 [INFO] PluginManage:283 - PluginManage : Loaded plugins [18]
2025-08-06 16:39:07,903 [INFO] PinOutPanel:1589 - setPackage(No Configuration,No Configuration)
2025-08-06 16:39:07,981 [INFO] CADModel:165 - CPN selected for project level
2025-08-06 16:39:07,981 [INFO] CADModel:114 - Register for checkConnection events
2025-08-06 16:39:07,995 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:07,995 [INFO] PluginManager:220 - loadIPPluginJar : add adc
2025-08-06 16:39:07,997 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:07,997 [INFO] PluginManager:220 - loadIPPluginJar : add aes
2025-08-06 16:39:07,999 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:07,999 [INFO] PluginManager:220 - loadIPPluginJar : add can
2025-08-06 16:39:08,001 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,001 [INFO] PluginManager:220 - loadIPPluginJar : add comp
2025-08-06 16:39:08,003 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,003 [INFO] PluginManager:220 - loadIPPluginJar : add cryp
2025-08-06 16:39:08,004 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,005 [INFO] PluginManager:220 - loadIPPluginJar : add ddr_ctrl_phy
2025-08-06 16:39:08,006 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,006 [INFO] PluginManager:220 - loadIPPluginJar : add dfsdm
2025-08-06 16:39:08,012 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,012 [INFO] PluginManager:220 - loadIPPluginJar : add dma
2025-08-06 16:39:08,015 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,016 [INFO] PluginManager:220 - loadIPPluginJar : add dma3
2025-08-06 16:39:08,018 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,018 [INFO] PluginManager:220 - loadIPPluginJar : add extmemmanager
2025-08-06 16:39:08,019 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,019 [INFO] PluginManager:220 - loadIPPluginJar : add fatfs
2025-08-06 16:39:08,023 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,024 [INFO] PluginManager:220 - loadIPPluginJar : add fmc
2025-08-06 16:39:08,029 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,030 [INFO] PluginManager:220 - loadIPPluginJar : add freertos
2025-08-06 16:39:08,031 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,031 [INFO] PluginManager:220 - loadIPPluginJar : add genericplugin
2025-08-06 16:39:08,032 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,033 [INFO] PluginManager:220 - loadIPPluginJar : add gfxmmu
2025-08-06 16:39:08,036 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,036 [INFO] PluginManager:220 - loadIPPluginJar : add gic
2025-08-06 16:39:08,040 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,040 [INFO] PluginManager:220 - loadIPPluginJar : add gpio
2025-08-06 16:39:08,043 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,043 [INFO] PluginManager:220 - loadIPPluginJar : add gtzc
2025-08-06 16:39:08,045 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,046 [INFO] PluginManager:220 - loadIPPluginJar : add hash
2025-08-06 16:39:08,048 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,049 [INFO] PluginManager:220 - loadIPPluginJar : add i2c
2025-08-06 16:39:08,050 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,050 [INFO] PluginManager:220 - loadIPPluginJar : add i2s
2025-08-06 16:39:08,052 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,053 [INFO] PluginManager:220 - loadIPPluginJar : add i3c
2025-08-06 16:39:08,056 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,056 [INFO] PluginManager:220 - loadIPPluginJar : add ipddr
2025-08-06 16:39:08,062 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,062 [INFO] PluginManager:220 - loadIPPluginJar : add linkedlist
2025-08-06 16:39:08,065 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,066 [INFO] PluginManager:220 - loadIPPluginJar : add lorawan
2025-08-06 16:39:08,067 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,067 [INFO] PluginManager:220 - loadIPPluginJar : add ltdc
2025-08-06 16:39:08,071 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,072 [INFO] PluginManager:220 - loadIPPluginJar : add mdma
2025-08-06 16:39:08,074 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,075 [INFO] PluginManager:220 - loadIPPluginJar : add nvic
2025-08-06 16:39:08,077 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,078 [INFO] PluginManager:220 - loadIPPluginJar : add opamp
2025-08-06 16:39:08,080 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,080 [INFO] PluginManager:220 - loadIPPluginJar : add openamp
2025-08-06 16:39:08,082 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,082 [INFO] PluginManager:220 - loadIPPluginJar : add pdm2pcm
2025-08-06 16:39:08,087 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,090 [INFO] PluginManager:220 - loadIPPluginJar : add plateformsettings
2025-08-06 16:39:08,092 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,092 [INFO] PluginManager:220 - loadIPPluginJar : add quadspi
2025-08-06 16:39:08,094 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,094 [INFO] PluginManager:220 - loadIPPluginJar : add radio
2025-08-06 16:39:08,097 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,097 [INFO] PluginManager:220 - loadIPPluginJar : add resmgrutility
2025-08-06 16:39:08,101 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,101 [INFO] PluginManager:220 - loadIPPluginJar : add sai
2025-08-06 16:39:08,107 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,107 [INFO] PluginManager:220 - loadIPPluginJar : add spi
2025-08-06 16:39:08,112 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,112 [INFO] PluginManager:220 - loadIPPluginJar : add stm32_wpan
2025-08-06 16:39:08,114 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,114 [INFO] PluginManager:220 - loadIPPluginJar : add tim
2025-08-06 16:39:08,116 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,116 [INFO] PluginManager:220 - loadIPPluginJar : add touchsensing
2025-08-06 16:39:08,118 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,118 [INFO] PluginManager:220 - loadIPPluginJar : add tracer_emb
2025-08-06 16:39:08,120 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,120 [INFO] PluginManager:220 - loadIPPluginJar : add ts
2025-08-06 16:39:08,121 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,121 [INFO] PluginManager:220 - loadIPPluginJar : add tsc
2025-08-06 16:39:08,122 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,122 [INFO] PluginManager:220 - loadIPPluginJar : add ucpd
2025-08-06 16:39:08,125 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,125 [INFO] PluginManager:220 - loadIPPluginJar : add usart
2025-08-06 16:39:08,128 [INFO] IPUIPlugin:80 - create IPUIPlugin
2025-08-06 16:39:08,128 [INFO] PluginManager:220 - loadIPPluginJar : add usbx
2025-08-06 16:39:08,250 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,271 [INFO] RulesReader:64 - Compatibility file has been processed (317 Rules)
2025-08-06 16:39:08,280 [INFO] RulesReader:64 - Compatibility file has been processed (317 Rules)
2025-08-06 16:39:08,288 [INFO] CADModel:165 - CPN selected for project level
2025-08-06 16:39:08,289 [INFO] CADModel:114 - Register for checkConnection events
2025-08-06 16:39:08,289 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,289 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-08-06 16:39:08,416 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,417 [INFO] CADModel:165 - CPN selected for project level
2025-08-06 16:39:08,417 [INFO] CADModel:114 - Register for checkConnection events
2025-08-06 16:39:08,417 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,418 [ERROR] CADModel:125 - Updater not yet initialized, retry later 
2025-08-06 16:39:08,421 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,519 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,523 [INFO] DbMcusAds:53 - JSON generation date=Tue Jul 08 16:14:23 CST 2025 (1751962463582)
2025-08-06 16:39:08,523 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,554 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-08-06 16:39:08,633 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,635 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,635 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,635 [WARN] DetailPanel:346 - Failed to get advertising image, set to default
2025-08-06 16:39:08,636 [FATAL] Updater:351 - Updater called before beeing initialized
2025-08-06 16:39:08,675 [ERROR] Updater:1198 - MainUpdater not yet initialized. External WinMGr cannot be set.
2025-08-06 16:39:08,678 [INFO] Updater:1134 - Updater Version found : 6.15.0
2025-08-06 16:39:08,694 [INFO] ApplicationProperties:184 - Using Application install path: D:\STM32CUBE\STM32CubeIDE_1.19.0\STM32CubeIDE\plugins\com.st.stm32cube.common.mx_6.15.0.202507011659
2025-08-06 16:39:09,153 [INFO] MainUpdater:2872 - connection check result : 10
2025-08-06 16:39:09,154 [INFO] MainUpdater:289 - Updater Check For Update Now.
2025-08-06 16:39:09,154 [INFO] MicroXplorer:498 - Change Database Version : DB.6.0.150
2025-08-06 16:39:09,162 [INFO] McuFinderGlobals:63 - Set McuFinder mode to 2 (CubeIDE integrated)
2025-08-06 16:39:09,162 [INFO] UserAuth:487 - Internet connection configuration mode: 1
2025-08-06 16:39:09,194 [INFO] JxBrowserEngine:152 - Initiate JxBrowser Engine with user profile folder
2025-08-06 16:39:09,339 [INFO] CheckServerUpdateThread:120 - End of CheckServer Thread
2025-08-06 16:39:10,305 [INFO] WebApp:169 - Instantiating new browser for Auth
2025-08-06 16:39:10,988 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-SNS-MOTENVWB1.1.4.0
2025-08-06 16:39:11,006 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-ASTRA1.2.0.2
2025-08-06 16:39:11,016 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.FP-ATR-ASTRA1.2.0.1
2025-08-06 16:39:11,029 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-SMBUS.2.1.0
2025-08-06 16:39:11,041 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-WB05N.1.1.0
2025-08-06 16:39:11,053 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-ST60.1.0.0
2025-08-06 16:39:11,120 [WARN] PackLoader:240 - Cannot read IP mode file for STMicroelectronics.X-CUBE-AZRTOS-F7.1.1.0
