../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1077:13:prvAddNewTaskToReadyList	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:5177:13:prvAddCurrentTaskToDelayedList	24	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:824:13:prvInitialiseNewTask.constprop	32	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3392:8:prvIdleTask	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:581:15:xTaskCreateStatic	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:733:13:xTaskCreate	48	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1162:7:vTaskDelete	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1386:13:eTaskGetState	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1478:14:uxTaskPriorityGet	8	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1500:14:uxTaskPriorityGetFromISR	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1540:7:vTaskPrioritySet	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1851:7:vTaskResume	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1905:13:xTaskResumeFromISR	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1975:6:vTaskStartScheduler	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2099:6:vTaskEndScheduler	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2110:6:vTaskSuspendAll	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2304:12:xTaskGetTickCount	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2319:12:xTaskGetTickCountFromISR	8	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2350:13:uxTaskGetNumberOfTasks	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2358:7:pcTaskGetName	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2707:12:xTaskIncrementTick	48	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2194:12:xTaskResumeAll.part.0	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2194:12:xTaskResumeAll	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2609:12:xTaskCatchUpTicks	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1341:7:vTaskDelay	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1257:7:vTaskDelayUntil	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2989:6:vTaskSwitchContext	4	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:1704:7:vTaskSuspend	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3064:6:vTaskPlaceOnEventList	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3081:6:vTaskPlaceOnUnorderedEventList	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3107:7:vTaskPlaceOnEventListRestricted	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3138:12:xTaskRemoveFromEventList	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3206:6:vTaskRemoveFromUnorderedEventList	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3254:6:vTaskSetTimeOutState	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3266:6:vTaskInternalSetTimeOutState	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3274:12:xTaskCheckForTimeOut	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3337:6:vTaskMissedYield	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3345:14:uxTaskGetTaskNumber	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3368:7:vTaskSetTaskNumber	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3670:7:vTaskGetInfo	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3766:21:prvListTasksWithinSingleList.part.0	32	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:2505:14:uxTaskGetSystemState	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3859:14:uxTaskGetStackHighWaterMark	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3969:15:xTaskGetCurrentTaskHandle	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:3986:13:xTaskGetSchedulerState	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4014:13:xTaskPriorityInherit	24	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4104:13:xTaskPriorityDisinherit	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4184:7:vTaskPriorityDisinheritAfterTimeout	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4602:12:uxTaskResetEventItemValue	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4618:15:pvTaskIncrementMutexHeldCount	0	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4635:11:ulTaskNotifyTake	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4703:13:xTaskNotifyWait	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4783:13:xTaskGenericNotify	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:4897:13:xTaskGenericNotifyFromISR	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:5026:7:vTaskNotifyGiveFromISR	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:5112:13:xTaskNotifyStateClear	8	static
../Middlewares/Third_Party/FreeRTOS/Source/tasks.c:5143:11:ulTaskNotifyValueClear	16	static
