../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2074:19:prvCopyDataToQueue	16	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2171:13:prvUnlockQueue	16	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:255:12:xQueueGenericReset	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:310:16:xQueueGenericCreateStatic	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:368:16:xQueueGenericCreate	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:532:15:xQueueGetMutexHolder	8	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:563:15:xQueueGetMutexHolderFromISR	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:686:16:xQueueCreateCountingSemaphoreStatic	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:714:16:xQueueCreateCountingSemaphore	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:740:12:xQueueGenericSend	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:512:16:xQueueCreateMutexStatic	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:589:13:xQueueGiveMutexRecursive	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:496:16:xQueueCreateMutex	16	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:950:12:xQueueGenericSendFromISR	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1112:12:xQueueGiveFromISR	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1277:12:xQueueReceive	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1418:12:xQueueSemaphoreTake	40	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:644:13:xQueueTakeMutexRecursive	16	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1636:12:xQueuePeek	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1785:12:xQueueReceiveFromISR	32	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1876:12:xQueuePeekFromISR	24	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1930:13:uxQueueMessagesWaiting	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1946:13:uxQueueSpacesAvailable	8	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1963:13:uxQueueMessagesWaitingFromISR	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:1975:6:vQueueDelete	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2019:14:uxQueueGetQueueNumber	0	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2029:7:vQueueSetQueueNumber	0	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2039:10:ucQueueGetQueueType	0	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2312:12:xQueueIsQueueEmptyFromISR	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2352:12:xQueueIsQueueFullFromISR	0	static,ignoring_inline_asm
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2648:7:vQueueAddToRegistry	4	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2677:14:pcQueueGetName	0	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2705:7:vQueueUnregisterQueue	0	static
../Middlewares/Third_Party/FreeRTOS/Source/queue.c:2737:7:vQueueWaitForMessageRestricted	16	static
