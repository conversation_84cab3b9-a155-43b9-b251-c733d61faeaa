!SESSION 2025-08-06 16:38:36.772 -----------------------------------------------
eclipse.buildId=Version 1.19.0
java.version=21.0.3
java.vendor=Eclipse Adoptium
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 4 4 2025-08-06 16:39:01.365
!MESSAGE CubeMX plugin appears to be active, Log4j initialization might be too late.

!ENTRY com.st.stm32cube.ide.mcu.informationcenter 1 1 2025-08-06 16:39:01.367
!MESSAGE Log4j2 initialized with config file D:\D\D\.metadata\.log4j2.xml

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:02.929
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:02.930
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:02.931
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:02.932
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY com.st.stm32cube.ide.mcu.ide 1 1 2025-08-06 16:39:04.390
!MESSAGE Started RMI Server, listening on port 41338

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.677
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.677
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.677
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.678
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.697
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.697
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.698
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.698
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.698
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.699
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.699
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.699
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.700
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.700
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.700
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.700
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.702
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.702
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.703
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.703
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.703
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.704
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.704
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.704
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.705
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.705
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.705
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.705
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.707
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.707
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.707
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.707
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.811
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.812
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.812
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.814
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.815
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.816
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.816
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.816
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.817
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.818
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.818
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.820
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.820
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.821
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.822
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.822
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.822
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.823
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.823
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.825
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.825
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.826
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.827
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.827
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.828
!MESSAGE System property http.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.828
!MESSAGE System property http.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.828
!MESSAGE System property https.proxyHost is set to 127.0.0.1 but should not be set.

!ENTRY org.eclipse.core.net 1 0 2025-08-06 16:39:08.830
!MESSAGE System property https.proxyPort is set to 7897 but should not be set.

!ENTRY org.eclipse.core.jobs 2 2 2025-08-06 16:39:11.091
!MESSAGE Job found still running after platform shutdown.  Jobs should be canceled by the plugin that scheduled them during shutdown: org.eclipse.ui.internal.Workbench$39 RUNNING
	 at java.base/jdk.internal.misc.Unsafe.park(Native Method)
	 at java.base/java.util.concurrent.locks.LockSupport.park(LockSupport.java:221)
	 at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer.acquire(AbstractQueuedSynchronizer.java:754)
	 at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireSharedInterruptibly(AbstractQueuedSynchronizer.java:1099)
	 at java.base/java.util.concurrent.CountDownLatch.await(CountDownLatch.java:230)
	 at com.teamdev.jxbrowser.internal.rpc.transport.CommonThreadResponseConsumer.await(CommonThreadResponseConsumer.java:49)
	 at com.teamdev.jxbrowser.internal.rpc.transport.CommonThreadCallExecutor.execute(CommonThreadCallExecutor.java:29)
	 at com.teamdev.jxbrowser.internal.rpc.ServiceConnectionImpl.invoke(ServiceConnectionImpl.java:213)
	 at com.teamdev.jxbrowser.navigation.internal.NavigationImpl.loadAndWait(NavigationImpl.java:227)
	 at com.teamdev.jxbrowser.navigation.internal.NavigationImpl.loadUrlAndWait(NavigationImpl.java:104)
	 at com.teamdev.jxbrowser.navigation.internal.NavigationImpl.loadUrlAndWait(NavigationImpl.java:91)
	 at com.teamdev.jxbrowser.navigation.internal.NavigationImpl.loadUrlAndWait(NavigationImpl.java:83)
	 at com.teamdev.jxbrowser.browser.internal.BrowserImpl.initializeMainFrame(BrowserImpl.java:412)
	 at com.teamdev.jxbrowser.profile.internal.ProfileImpl.newBrowser(ProfileImpl.java:150)
	 at com.st.microxplorer.plugins.userauth.WebApp.<init>(WebApp.java:174)
	 at com.st.microxplorer.plugins.userauth.WebApp.getInstance(WebApp.java:779)
	 at com.st.microxplorer.plugins.userauth.UserAuth.getBrowserView(UserAuth.java:489)
	 at com.st.microxplorer.plugins.userauth.UserAuth.checkInfile(UserAuth.java:251)
	 at com.st.microxplorer.plugins.userauth.UserAuth.activatePlugin(UserAuth.java:169)
	 at com.st.stm32cube.common.mx.userauth.UserAuthAdapter.<init>(UserAuthAdapter.java:29)
	 at com.st.stm32cube.common.mx.userauth.UserAuthProvider.getUserAuthAdapter(UserAuthProvider.java:16)
	 at com.st.stm32cube.common.ecosystemintegration.ui.core.MCUEcoSystemIntegrationUiHelper.getUserAuth(MCUEcoSystemIntegrationUiHelper.java:100)
	 at com.st.stm32cube.ide.mcu.userauth.UserAuthStartup.earlyStartup(UserAuthStartup.java:32)
	 at org.eclipse.ui.internal.EarlyStartupRunnable.runEarlyStartup(EarlyStartupRunnable.java:79)
	 at org.eclipse.ui.internal.EarlyStartupRunnable.run(EarlyStartupRunnable.java:55)
	 at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:47)
	 at org.eclipse.ui.internal.Workbench$39.run(Workbench.java:2733)
	 at org.eclipse.core.internal.jobs.Worker.run(Worker.java:63)
