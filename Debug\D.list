
D.elf:     file format elf32-littlearm

Sections:
Idx Name          Size      VMA       LMA       File off  Algn
  0 .isr_vector   00000188  08000000  08000000  00001000  2**0
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  1 .text         000037ac  08000190  08000190  00001190  2**4
                  CONTENTS, ALL<PERSON>, LOAD, READ<PERSON><PERSON><PERSON>, CODE
  2 .rodata       00000060  0800393c  0800393c  0000493c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  3 .ARM.extab    00000000  0800399c  0800399c  00005010  2**0
                  CONTENTS, READONLY
  4 .ARM          00000008  0800399c  0800399c  0000499c  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  5 .preinit_array 00000000  080039a4  080039a4  00005010  2**0
                  CONTENTS, ALLOC, LOAD, DATA
  6 .init_array   00000004  080039a4  080039a4  000049a4  2**2
                  CONTENTS, ALLOC, LOAD, R<PERSON><PERSON><PERSON><PERSON><PERSON>, DATA
  7 .fini_array   00000004  080039a8  080039a8  000049a8  2**2
                  CONTENTS, ALLOC, LOAD, READONLY, DATA
  8 .data         00000010  20000000  080039ac  00005000  2**2
                  CONTENTS, ALLOC, LOAD, DATA
  9 .ccmram       00000000  10000000  10000000  00005010  2**0
                  CONTENTS
 10 .bss          00004a68  20000010  20000010  00005010  2**3
                  ALLOC
 11 ._user_heap_stack 00000600  20004a78  20004a78  00005010  2**0
                  ALLOC
 12 .ARM.attributes 00000030  00000000  00000000  00005010  2**0
                  CONTENTS, READONLY
 13 .debug_info   0001c11f  00000000  00000000  00005040  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 14 .debug_abbrev 00004189  00000000  00000000  0002115f  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 15 .debug_loclists 0000e82f  00000000  00000000  000252e8  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 16 .debug_aranges 000011e0  00000000  00000000  00033b18  2**3
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 17 .debug_rnglists 000017b3  00000000  00000000  00034cf8  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 18 .debug_macro  00023f1e  00000000  00000000  000364ab  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 19 .debug_line   0001e73d  00000000  00000000  0005a3c9  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 20 .debug_str    000df6ba  00000000  00000000  00078b06  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 21 .comment      00000043  00000000  00000000  001581c0  2**0
                  CONTENTS, READONLY
 22 .debug_frame  000033c0  00000000  00000000  00158204  2**2
                  CONTENTS, READONLY, DEBUGGING, OCTETS
 23 .debug_line_str 0000003c  00000000  00000000  0015b5c4  2**0
                  CONTENTS, READONLY, DEBUGGING, OCTETS

Disassembly of section .text:

08000190 <__do_global_dtors_aux>:
 8000190:	b510      	push	{r4, lr}
 8000192:	4c05      	ldr	r4, [pc, #20]	@ (80001a8 <__do_global_dtors_aux+0x18>)
 8000194:	7823      	ldrb	r3, [r4, #0]
 8000196:	b933      	cbnz	r3, 80001a6 <__do_global_dtors_aux+0x16>
 8000198:	4b04      	ldr	r3, [pc, #16]	@ (80001ac <__do_global_dtors_aux+0x1c>)
 800019a:	b113      	cbz	r3, 80001a2 <__do_global_dtors_aux+0x12>
 800019c:	4804      	ldr	r0, [pc, #16]	@ (80001b0 <__do_global_dtors_aux+0x20>)
 800019e:	f3af 8000 	nop.w
 80001a2:	2301      	movs	r3, #1
 80001a4:	7023      	strb	r3, [r4, #0]
 80001a6:	bd10      	pop	{r4, pc}
 80001a8:	20000010 	.word	0x20000010
 80001ac:	00000000 	.word	0x00000000
 80001b0:	08003924 	.word	0x08003924

080001b4 <frame_dummy>:
 80001b4:	b508      	push	{r3, lr}
 80001b6:	4b03      	ldr	r3, [pc, #12]	@ (80001c4 <frame_dummy+0x10>)
 80001b8:	b11b      	cbz	r3, 80001c2 <frame_dummy+0xe>
 80001ba:	4903      	ldr	r1, [pc, #12]	@ (80001c8 <frame_dummy+0x14>)
 80001bc:	4803      	ldr	r0, [pc, #12]	@ (80001cc <frame_dummy+0x18>)
 80001be:	f3af 8000 	nop.w
 80001c2:	bd08      	pop	{r3, pc}
 80001c4:	00000000 	.word	0x00000000
 80001c8:	20000014 	.word	0x20000014
 80001cc:	08003924 	.word	0x08003924

080001d0 <__aeabi_uldivmod>:
 80001d0:	b953      	cbnz	r3, 80001e8 <__aeabi_uldivmod+0x18>
 80001d2:	b94a      	cbnz	r2, 80001e8 <__aeabi_uldivmod+0x18>
 80001d4:	2900      	cmp	r1, #0
 80001d6:	bf08      	it	eq
 80001d8:	2800      	cmpeq	r0, #0
 80001da:	bf1c      	itt	ne
 80001dc:	f04f 31ff 	movne.w	r1, #**********
 80001e0:	f04f 30ff 	movne.w	r0, #**********
 80001e4:	f000 b988 	b.w	80004f8 <__aeabi_idiv0>
 80001e8:	f1ad 0c08 	sub.w	ip, sp, #8
 80001ec:	e96d ce04 	strd	ip, lr, [sp, #-16]!
 80001f0:	f000 f806 	bl	8000200 <__udivmoddi4>
 80001f4:	f8dd e004 	ldr.w	lr, [sp, #4]
 80001f8:	e9dd 2302 	ldrd	r2, r3, [sp, #8]
 80001fc:	b004      	add	sp, #16
 80001fe:	4770      	bx	lr

08000200 <__udivmoddi4>:
 8000200:	e92d 47f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, lr}
 8000204:	9d08      	ldr	r5, [sp, #32]
 8000206:	468e      	mov	lr, r1
 8000208:	4604      	mov	r4, r0
 800020a:	4688      	mov	r8, r1
 800020c:	2b00      	cmp	r3, #0
 800020e:	d14a      	bne.n	80002a6 <__udivmoddi4+0xa6>
 8000210:	428a      	cmp	r2, r1
 8000212:	4617      	mov	r7, r2
 8000214:	d962      	bls.n	80002dc <__udivmoddi4+0xdc>
 8000216:	fab2 f682 	clz	r6, r2
 800021a:	b14e      	cbz	r6, 8000230 <__udivmoddi4+0x30>
 800021c:	f1c6 0320 	rsb	r3, r6, #32
 8000220:	fa01 f806 	lsl.w	r8, r1, r6
 8000224:	fa20 f303 	lsr.w	r3, r0, r3
 8000228:	40b7      	lsls	r7, r6
 800022a:	ea43 0808 	orr.w	r8, r3, r8
 800022e:	40b4      	lsls	r4, r6
 8000230:	ea4f 4e17 	mov.w	lr, r7, lsr #16
 8000234:	fa1f fc87 	uxth.w	ip, r7
 8000238:	fbb8 f1fe 	udiv	r1, r8, lr
 800023c:	0c23      	lsrs	r3, r4, #16
 800023e:	fb0e 8811 	mls	r8, lr, r1, r8
 8000242:	ea43 4308 	orr.w	r3, r3, r8, lsl #16
 8000246:	fb01 f20c 	mul.w	r2, r1, ip
 800024a:	429a      	cmp	r2, r3
 800024c:	d909      	bls.n	8000262 <__udivmoddi4+0x62>
 800024e:	18fb      	adds	r3, r7, r3
 8000250:	f101 30ff 	add.w	r0, r1, #**********
 8000254:	f080 80ea 	bcs.w	800042c <__udivmoddi4+0x22c>
 8000258:	429a      	cmp	r2, r3
 800025a:	f240 80e7 	bls.w	800042c <__udivmoddi4+0x22c>
 800025e:	3902      	subs	r1, #2
 8000260:	443b      	add	r3, r7
 8000262:	1a9a      	subs	r2, r3, r2
 8000264:	b2a3      	uxth	r3, r4
 8000266:	fbb2 f0fe 	udiv	r0, r2, lr
 800026a:	fb0e 2210 	mls	r2, lr, r0, r2
 800026e:	ea43 4302 	orr.w	r3, r3, r2, lsl #16
 8000272:	fb00 fc0c 	mul.w	ip, r0, ip
 8000276:	459c      	cmp	ip, r3
 8000278:	d909      	bls.n	800028e <__udivmoddi4+0x8e>
 800027a:	18fb      	adds	r3, r7, r3
 800027c:	f100 32ff 	add.w	r2, r0, #**********
 8000280:	f080 80d6 	bcs.w	8000430 <__udivmoddi4+0x230>
 8000284:	459c      	cmp	ip, r3
 8000286:	f240 80d3 	bls.w	8000430 <__udivmoddi4+0x230>
 800028a:	443b      	add	r3, r7
 800028c:	3802      	subs	r0, #2
 800028e:	ea40 4001 	orr.w	r0, r0, r1, lsl #16
 8000292:	eba3 030c 	sub.w	r3, r3, ip
 8000296:	2100      	movs	r1, #0
 8000298:	b11d      	cbz	r5, 80002a2 <__udivmoddi4+0xa2>
 800029a:	40f3      	lsrs	r3, r6
 800029c:	2200      	movs	r2, #0
 800029e:	e9c5 3200 	strd	r3, r2, [r5]
 80002a2:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
 80002a6:	428b      	cmp	r3, r1
 80002a8:	d905      	bls.n	80002b6 <__udivmoddi4+0xb6>
 80002aa:	b10d      	cbz	r5, 80002b0 <__udivmoddi4+0xb0>
 80002ac:	e9c5 0100 	strd	r0, r1, [r5]
 80002b0:	2100      	movs	r1, #0
 80002b2:	4608      	mov	r0, r1
 80002b4:	e7f5      	b.n	80002a2 <__udivmoddi4+0xa2>
 80002b6:	fab3 f183 	clz	r1, r3
 80002ba:	2900      	cmp	r1, #0
 80002bc:	d146      	bne.n	800034c <__udivmoddi4+0x14c>
 80002be:	4573      	cmp	r3, lr
 80002c0:	d302      	bcc.n	80002c8 <__udivmoddi4+0xc8>
 80002c2:	4282      	cmp	r2, r0
 80002c4:	f200 8105 	bhi.w	80004d2 <__udivmoddi4+0x2d2>
 80002c8:	1a84      	subs	r4, r0, r2
 80002ca:	eb6e 0203 	sbc.w	r2, lr, r3
 80002ce:	2001      	movs	r0, #1
 80002d0:	4690      	mov	r8, r2
 80002d2:	2d00      	cmp	r5, #0
 80002d4:	d0e5      	beq.n	80002a2 <__udivmoddi4+0xa2>
 80002d6:	e9c5 4800 	strd	r4, r8, [r5]
 80002da:	e7e2      	b.n	80002a2 <__udivmoddi4+0xa2>
 80002dc:	2a00      	cmp	r2, #0
 80002de:	f000 8090 	beq.w	8000402 <__udivmoddi4+0x202>
 80002e2:	fab2 f682 	clz	r6, r2
 80002e6:	2e00      	cmp	r6, #0
 80002e8:	f040 80a4 	bne.w	8000434 <__udivmoddi4+0x234>
 80002ec:	1a8a      	subs	r2, r1, r2
 80002ee:	0c03      	lsrs	r3, r0, #16
 80002f0:	ea4f 4e17 	mov.w	lr, r7, lsr #16
 80002f4:	b280      	uxth	r0, r0
 80002f6:	b2bc      	uxth	r4, r7
 80002f8:	2101      	movs	r1, #1
 80002fa:	fbb2 fcfe 	udiv	ip, r2, lr
 80002fe:	fb0e 221c 	mls	r2, lr, ip, r2
 8000302:	ea43 4302 	orr.w	r3, r3, r2, lsl #16
 8000306:	fb04 f20c 	mul.w	r2, r4, ip
 800030a:	429a      	cmp	r2, r3
 800030c:	d907      	bls.n	800031e <__udivmoddi4+0x11e>
 800030e:	18fb      	adds	r3, r7, r3
 8000310:	f10c 38ff 	add.w	r8, ip, #**********
 8000314:	d202      	bcs.n	800031c <__udivmoddi4+0x11c>
 8000316:	429a      	cmp	r2, r3
 8000318:	f200 80e0 	bhi.w	80004dc <__udivmoddi4+0x2dc>
 800031c:	46c4      	mov	ip, r8
 800031e:	1a9b      	subs	r3, r3, r2
 8000320:	fbb3 f2fe 	udiv	r2, r3, lr
 8000324:	fb0e 3312 	mls	r3, lr, r2, r3
 8000328:	ea40 4303 	orr.w	r3, r0, r3, lsl #16
 800032c:	fb02 f404 	mul.w	r4, r2, r4
 8000330:	429c      	cmp	r4, r3
 8000332:	d907      	bls.n	8000344 <__udivmoddi4+0x144>
 8000334:	18fb      	adds	r3, r7, r3
 8000336:	f102 30ff 	add.w	r0, r2, #**********
 800033a:	d202      	bcs.n	8000342 <__udivmoddi4+0x142>
 800033c:	429c      	cmp	r4, r3
 800033e:	f200 80ca 	bhi.w	80004d6 <__udivmoddi4+0x2d6>
 8000342:	4602      	mov	r2, r0
 8000344:	1b1b      	subs	r3, r3, r4
 8000346:	ea42 400c 	orr.w	r0, r2, ip, lsl #16
 800034a:	e7a5      	b.n	8000298 <__udivmoddi4+0x98>
 800034c:	f1c1 0620 	rsb	r6, r1, #32
 8000350:	408b      	lsls	r3, r1
 8000352:	fa22 f706 	lsr.w	r7, r2, r6
 8000356:	431f      	orrs	r7, r3
 8000358:	fa0e f401 	lsl.w	r4, lr, r1
 800035c:	fa20 f306 	lsr.w	r3, r0, r6
 8000360:	fa2e fe06 	lsr.w	lr, lr, r6
 8000364:	ea4f 4917 	mov.w	r9, r7, lsr #16
 8000368:	4323      	orrs	r3, r4
 800036a:	fa00 f801 	lsl.w	r8, r0, r1
 800036e:	fa1f fc87 	uxth.w	ip, r7
 8000372:	fbbe f0f9 	udiv	r0, lr, r9
 8000376:	0c1c      	lsrs	r4, r3, #16
 8000378:	fb09 ee10 	mls	lr, r9, r0, lr
 800037c:	ea44 440e 	orr.w	r4, r4, lr, lsl #16
 8000380:	fb00 fe0c 	mul.w	lr, r0, ip
 8000384:	45a6      	cmp	lr, r4
 8000386:	fa02 f201 	lsl.w	r2, r2, r1
 800038a:	d909      	bls.n	80003a0 <__udivmoddi4+0x1a0>
 800038c:	193c      	adds	r4, r7, r4
 800038e:	f100 3aff 	add.w	sl, r0, #**********
 8000392:	f080 809c 	bcs.w	80004ce <__udivmoddi4+0x2ce>
 8000396:	45a6      	cmp	lr, r4
 8000398:	f240 8099 	bls.w	80004ce <__udivmoddi4+0x2ce>
 800039c:	3802      	subs	r0, #2
 800039e:	443c      	add	r4, r7
 80003a0:	eba4 040e 	sub.w	r4, r4, lr
 80003a4:	fa1f fe83 	uxth.w	lr, r3
 80003a8:	fbb4 f3f9 	udiv	r3, r4, r9
 80003ac:	fb09 4413 	mls	r4, r9, r3, r4
 80003b0:	ea4e 4404 	orr.w	r4, lr, r4, lsl #16
 80003b4:	fb03 fc0c 	mul.w	ip, r3, ip
 80003b8:	45a4      	cmp	ip, r4
 80003ba:	d908      	bls.n	80003ce <__udivmoddi4+0x1ce>
 80003bc:	193c      	adds	r4, r7, r4
 80003be:	f103 3eff 	add.w	lr, r3, #**********
 80003c2:	f080 8082 	bcs.w	80004ca <__udivmoddi4+0x2ca>
 80003c6:	45a4      	cmp	ip, r4
 80003c8:	d97f      	bls.n	80004ca <__udivmoddi4+0x2ca>
 80003ca:	3b02      	subs	r3, #2
 80003cc:	443c      	add	r4, r7
 80003ce:	ea43 4000 	orr.w	r0, r3, r0, lsl #16
 80003d2:	eba4 040c 	sub.w	r4, r4, ip
 80003d6:	fba0 ec02 	umull	lr, ip, r0, r2
 80003da:	4564      	cmp	r4, ip
 80003dc:	4673      	mov	r3, lr
 80003de:	46e1      	mov	r9, ip
 80003e0:	d362      	bcc.n	80004a8 <__udivmoddi4+0x2a8>
 80003e2:	d05f      	beq.n	80004a4 <__udivmoddi4+0x2a4>
 80003e4:	b15d      	cbz	r5, 80003fe <__udivmoddi4+0x1fe>
 80003e6:	ebb8 0203 	subs.w	r2, r8, r3
 80003ea:	eb64 0409 	sbc.w	r4, r4, r9
 80003ee:	fa04 f606 	lsl.w	r6, r4, r6
 80003f2:	fa22 f301 	lsr.w	r3, r2, r1
 80003f6:	431e      	orrs	r6, r3
 80003f8:	40cc      	lsrs	r4, r1
 80003fa:	e9c5 6400 	strd	r6, r4, [r5]
 80003fe:	2100      	movs	r1, #0
 8000400:	e74f      	b.n	80002a2 <__udivmoddi4+0xa2>
 8000402:	fbb1 fcf2 	udiv	ip, r1, r2
 8000406:	0c01      	lsrs	r1, r0, #16
 8000408:	ea41 410e 	orr.w	r1, r1, lr, lsl #16
 800040c:	b280      	uxth	r0, r0
 800040e:	ea40 4201 	orr.w	r2, r0, r1, lsl #16
 8000412:	463b      	mov	r3, r7
 8000414:	4638      	mov	r0, r7
 8000416:	463c      	mov	r4, r7
 8000418:	46b8      	mov	r8, r7
 800041a:	46be      	mov	lr, r7
 800041c:	2620      	movs	r6, #32
 800041e:	fbb1 f1f7 	udiv	r1, r1, r7
 8000422:	eba2 0208 	sub.w	r2, r2, r8
 8000426:	ea41 410c 	orr.w	r1, r1, ip, lsl #16
 800042a:	e766      	b.n	80002fa <__udivmoddi4+0xfa>
 800042c:	4601      	mov	r1, r0
 800042e:	e718      	b.n	8000262 <__udivmoddi4+0x62>
 8000430:	4610      	mov	r0, r2
 8000432:	e72c      	b.n	800028e <__udivmoddi4+0x8e>
 8000434:	f1c6 0220 	rsb	r2, r6, #32
 8000438:	fa2e f302 	lsr.w	r3, lr, r2
 800043c:	40b7      	lsls	r7, r6
 800043e:	40b1      	lsls	r1, r6
 8000440:	fa20 f202 	lsr.w	r2, r0, r2
 8000444:	ea4f 4e17 	mov.w	lr, r7, lsr #16
 8000448:	430a      	orrs	r2, r1
 800044a:	fbb3 f8fe 	udiv	r8, r3, lr
 800044e:	b2bc      	uxth	r4, r7
 8000450:	fb0e 3318 	mls	r3, lr, r8, r3
 8000454:	0c11      	lsrs	r1, r2, #16
 8000456:	ea41 4103 	orr.w	r1, r1, r3, lsl #16
 800045a:	fb08 f904 	mul.w	r9, r8, r4
 800045e:	40b0      	lsls	r0, r6
 8000460:	4589      	cmp	r9, r1
 8000462:	ea4f 4310 	mov.w	r3, r0, lsr #16
 8000466:	b280      	uxth	r0, r0
 8000468:	d93e      	bls.n	80004e8 <__udivmoddi4+0x2e8>
 800046a:	1879      	adds	r1, r7, r1
 800046c:	f108 3cff 	add.w	ip, r8, #**********
 8000470:	d201      	bcs.n	8000476 <__udivmoddi4+0x276>
 8000472:	4589      	cmp	r9, r1
 8000474:	d81f      	bhi.n	80004b6 <__udivmoddi4+0x2b6>
 8000476:	eba1 0109 	sub.w	r1, r1, r9
 800047a:	fbb1 f9fe 	udiv	r9, r1, lr
 800047e:	fb09 f804 	mul.w	r8, r9, r4
 8000482:	fb0e 1119 	mls	r1, lr, r9, r1
 8000486:	b292      	uxth	r2, r2
 8000488:	ea42 4201 	orr.w	r2, r2, r1, lsl #16
 800048c:	4542      	cmp	r2, r8
 800048e:	d229      	bcs.n	80004e4 <__udivmoddi4+0x2e4>
 8000490:	18ba      	adds	r2, r7, r2
 8000492:	f109 31ff 	add.w	r1, r9, #**********
 8000496:	d2c4      	bcs.n	8000422 <__udivmoddi4+0x222>
 8000498:	4542      	cmp	r2, r8
 800049a:	d2c2      	bcs.n	8000422 <__udivmoddi4+0x222>
 800049c:	f1a9 0102 	sub.w	r1, r9, #2
 80004a0:	443a      	add	r2, r7
 80004a2:	e7be      	b.n	8000422 <__udivmoddi4+0x222>
 80004a4:	45f0      	cmp	r8, lr
 80004a6:	d29d      	bcs.n	80003e4 <__udivmoddi4+0x1e4>
 80004a8:	ebbe 0302 	subs.w	r3, lr, r2
 80004ac:	eb6c 0c07 	sbc.w	ip, ip, r7
 80004b0:	3801      	subs	r0, #1
 80004b2:	46e1      	mov	r9, ip
 80004b4:	e796      	b.n	80003e4 <__udivmoddi4+0x1e4>
 80004b6:	eba7 0909 	sub.w	r9, r7, r9
 80004ba:	4449      	add	r1, r9
 80004bc:	f1a8 0c02 	sub.w	ip, r8, #2
 80004c0:	fbb1 f9fe 	udiv	r9, r1, lr
 80004c4:	fb09 f804 	mul.w	r8, r9, r4
 80004c8:	e7db      	b.n	8000482 <__udivmoddi4+0x282>
 80004ca:	4673      	mov	r3, lr
 80004cc:	e77f      	b.n	80003ce <__udivmoddi4+0x1ce>
 80004ce:	4650      	mov	r0, sl
 80004d0:	e766      	b.n	80003a0 <__udivmoddi4+0x1a0>
 80004d2:	4608      	mov	r0, r1
 80004d4:	e6fd      	b.n	80002d2 <__udivmoddi4+0xd2>
 80004d6:	443b      	add	r3, r7
 80004d8:	3a02      	subs	r2, #2
 80004da:	e733      	b.n	8000344 <__udivmoddi4+0x144>
 80004dc:	f1ac 0c02 	sub.w	ip, ip, #2
 80004e0:	443b      	add	r3, r7
 80004e2:	e71c      	b.n	800031e <__udivmoddi4+0x11e>
 80004e4:	4649      	mov	r1, r9
 80004e6:	e79c      	b.n	8000422 <__udivmoddi4+0x222>
 80004e8:	eba1 0109 	sub.w	r1, r1, r9
 80004ec:	46c4      	mov	ip, r8
 80004ee:	fbb1 f9fe 	udiv	r9, r1, lr
 80004f2:	fb09 f804 	mul.w	r8, r9, r4
 80004f6:	e7c4      	b.n	8000482 <__udivmoddi4+0x282>

080004f8 <__aeabi_idiv0>:
 80004f8:	4770      	bx	lr
 80004fa:	bf00      	nop
 80004fc:	0000      	movs	r0, r0
	...

08000500 <MX_ADC1_Init>:
ADC_HandleTypeDef hadc1;
DMA_HandleTypeDef hdma_adc1;

/* ADC1 init function */
void MX_ADC1_Init(void)
{
 8000500:	b500      	push	{lr}

  /* USER CODE END ADC1_Init 1 */

  /** Configure the global features of the ADC (Clock, Resolution, Data Alignment and number of conversion)
  */
  hadc1.Instance = ADC1;
 8000502:	4849      	ldr	r0, [pc, #292]	@ (8000628 <MX_ADC1_Init+0x128>)
 8000504:	4949      	ldr	r1, [pc, #292]	@ (800062c <MX_ADC1_Init+0x12c>)
{
 8000506:	b085      	sub	sp, #20
  ADC_ChannelConfTypeDef sConfig = {0};
 8000508:	2300      	movs	r3, #0
  hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
  hadc1.Init.Resolution = ADC_RESOLUTION_12B;
  hadc1.Init.ScanConvMode = ENABLE;
 800050a:	2201      	movs	r2, #1
  hadc1.Instance = ADC1;
 800050c:	6001      	str	r1, [r0, #0]
  hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
 800050e:	f44f 3180 	mov.w	r1, #65536	@ 0x10000
  ADC_ChannelConfTypeDef sConfig = {0};
 8000512:	e9cd 3300 	strd	r3, r3, [sp]
 8000516:	e9cd 3302 	strd	r3, r3, [sp, #8]
  hadc1.Init.Resolution = ADC_RESOLUTION_12B;
 800051a:	e9c0 1301 	strd	r1, r3, [r0, #4]
  hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_RISING;
  hadc1.Init.ExternalTrigConv = ADC_EXTERNALTRIGCONV_T4_CC4;
  hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
  hadc1.Init.NbrOfConversion = 8;
  hadc1.Init.DMAContinuousRequests = DISABLE;
  hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
 800051e:	e9c0 2204 	strd	r2, r2, [r0, #16]
  hadc1.Init.ContinuousConvMode = DISABLE;
 8000522:	7603      	strb	r3, [r0, #24]
  hadc1.Init.DiscontinuousConvMode = DISABLE;
 8000524:	f880 3020 	strb.w	r3, [r0, #32]
  hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
 8000528:	60c3      	str	r3, [r0, #12]
  hadc1.Init.DMAContinuousRequests = DISABLE;
 800052a:	f880 3030 	strb.w	r3, [r0, #48]	@ 0x30
  hadc1.Init.ExternalTrigConv = ADC_EXTERNALTRIGCONV_T4_CC4;
 800052e:	f04f 6210 	mov.w	r2, #150994944	@ 0x9000000
 8000532:	f04f 5380 	mov.w	r3, #268435456	@ 0x10000000
 8000536:	e9c0 230a 	strd	r2, r3, [r0, #40]	@ 0x28
  hadc1.Init.NbrOfConversion = 8;
 800053a:	2308      	movs	r3, #8
 800053c:	61c3      	str	r3, [r0, #28]
  if (HAL_ADC_Init(&hadc1) != HAL_OK)
 800053e:	f000 fb3d 	bl	8000bbc <HAL_ADC_Init>
 8000542:	2800      	cmp	r0, #0
 8000544:	d14f      	bne.n	80005e6 <MX_ADC1_Init+0xe6>
    Error_Handler();
  }

  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
  */
  sConfig.Channel = ADC_CHANNEL_1;
 8000546:	ed9f 7b36 	vldr	d7, [pc, #216]	@ 8000620 <MX_ADC1_Init+0x120>
  sConfig.Rank = 1;
  sConfig.SamplingTime = ADC_SAMPLETIME_480CYCLES;
 800054a:	2307      	movs	r3, #7
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 800054c:	4836      	ldr	r0, [pc, #216]	@ (8000628 <MX_ADC1_Init+0x128>)
  sConfig.SamplingTime = ADC_SAMPLETIME_480CYCLES;
 800054e:	9302      	str	r3, [sp, #8]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 8000550:	4669      	mov	r1, sp
  sConfig.Channel = ADC_CHANNEL_1;
 8000552:	ed8d 7b00 	vstr	d7, [sp]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 8000556:	f000 fbdd 	bl	8000d14 <HAL_ADC_ConfigChannel>
 800055a:	2800      	cmp	r0, #0
 800055c:	d15d      	bne.n	800061a <MX_ADC1_Init+0x11a>
    Error_Handler();
  }

  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
  */
  sConfig.Channel = ADC_CHANNEL_2;
 800055e:	2202      	movs	r2, #2
 8000560:	2302      	movs	r3, #2
  sConfig.Rank = 2;
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 8000562:	4831      	ldr	r0, [pc, #196]	@ (8000628 <MX_ADC1_Init+0x128>)
 8000564:	4669      	mov	r1, sp
  sConfig.Channel = ADC_CHANNEL_2;
 8000566:	e9cd 2300 	strd	r2, r3, [sp]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 800056a:	f000 fbd3 	bl	8000d14 <HAL_ADC_ConfigChannel>
 800056e:	2800      	cmp	r0, #0
 8000570:	d150      	bne.n	8000614 <MX_ADC1_Init+0x114>
    Error_Handler();
  }

  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
  */
  sConfig.Channel = ADC_CHANNEL_3;
 8000572:	2203      	movs	r2, #3
 8000574:	2303      	movs	r3, #3
  sConfig.Rank = 3;
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 8000576:	482c      	ldr	r0, [pc, #176]	@ (8000628 <MX_ADC1_Init+0x128>)
 8000578:	4669      	mov	r1, sp
  sConfig.Channel = ADC_CHANNEL_3;
 800057a:	e9cd 2300 	strd	r2, r3, [sp]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 800057e:	f000 fbc9 	bl	8000d14 <HAL_ADC_ConfigChannel>
 8000582:	2800      	cmp	r0, #0
 8000584:	d143      	bne.n	800060e <MX_ADC1_Init+0x10e>
    Error_Handler();
  }

  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
  */
  sConfig.Channel = ADC_CHANNEL_8;
 8000586:	2208      	movs	r2, #8
 8000588:	2304      	movs	r3, #4
  sConfig.Rank = 4;
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 800058a:	4827      	ldr	r0, [pc, #156]	@ (8000628 <MX_ADC1_Init+0x128>)
 800058c:	4669      	mov	r1, sp
  sConfig.Channel = ADC_CHANNEL_8;
 800058e:	e9cd 2300 	strd	r2, r3, [sp]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 8000592:	f000 fbbf 	bl	8000d14 <HAL_ADC_ConfigChannel>
 8000596:	bbb8      	cbnz	r0, 8000608 <MX_ADC1_Init+0x108>
    Error_Handler();
  }

  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
  */
  sConfig.Channel = ADC_CHANNEL_9;
 8000598:	2209      	movs	r2, #9
 800059a:	2305      	movs	r3, #5
  sConfig.Rank = 5;
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 800059c:	4822      	ldr	r0, [pc, #136]	@ (8000628 <MX_ADC1_Init+0x128>)
 800059e:	4669      	mov	r1, sp
  sConfig.Channel = ADC_CHANNEL_9;
 80005a0:	e9cd 2300 	strd	r2, r3, [sp]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 80005a4:	f000 fbb6 	bl	8000d14 <HAL_ADC_ConfigChannel>
 80005a8:	bb58      	cbnz	r0, 8000602 <MX_ADC1_Init+0x102>
    Error_Handler();
  }

  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
  */
  sConfig.Channel = ADC_CHANNEL_10;
 80005aa:	220a      	movs	r2, #10
 80005ac:	2306      	movs	r3, #6
  sConfig.Rank = 6;
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 80005ae:	481e      	ldr	r0, [pc, #120]	@ (8000628 <MX_ADC1_Init+0x128>)
 80005b0:	4669      	mov	r1, sp
  sConfig.Channel = ADC_CHANNEL_10;
 80005b2:	e9cd 2300 	strd	r2, r3, [sp]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 80005b6:	f000 fbad 	bl	8000d14 <HAL_ADC_ConfigChannel>
 80005ba:	b9f8      	cbnz	r0, 80005fc <MX_ADC1_Init+0xfc>
    Error_Handler();
  }

  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
  */
  sConfig.Channel = ADC_CHANNEL_11;
 80005bc:	220b      	movs	r2, #11
 80005be:	2307      	movs	r3, #7
  sConfig.Rank = 7;
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 80005c0:	4819      	ldr	r0, [pc, #100]	@ (8000628 <MX_ADC1_Init+0x128>)
 80005c2:	4669      	mov	r1, sp
  sConfig.Channel = ADC_CHANNEL_11;
 80005c4:	e9cd 2300 	strd	r2, r3, [sp]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 80005c8:	f000 fba4 	bl	8000d14 <HAL_ADC_ConfigChannel>
 80005cc:	b998      	cbnz	r0, 80005f6 <MX_ADC1_Init+0xf6>
    Error_Handler();
  }

  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
  */
  sConfig.Channel = ADC_CHANNEL_12;
 80005ce:	220c      	movs	r2, #12
 80005d0:	2308      	movs	r3, #8
  sConfig.Rank = 8;
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 80005d2:	4815      	ldr	r0, [pc, #84]	@ (8000628 <MX_ADC1_Init+0x128>)
 80005d4:	4669      	mov	r1, sp
  sConfig.Channel = ADC_CHANNEL_12;
 80005d6:	e9cd 2300 	strd	r2, r3, [sp]
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
 80005da:	f000 fb9b 	bl	8000d14 <HAL_ADC_ConfigChannel>
 80005de:	b928      	cbnz	r0, 80005ec <MX_ADC1_Init+0xec>
  }
  /* USER CODE BEGIN ADC1_Init 2 */

  /* USER CODE END ADC1_Init 2 */

}
 80005e0:	b005      	add	sp, #20
 80005e2:	f85d fb04 	ldr.w	pc, [sp], #4
    Error_Handler();
 80005e6:	f000 f9d1 	bl	800098c <Error_Handler>
 80005ea:	e7ac      	b.n	8000546 <MX_ADC1_Init+0x46>
    Error_Handler();
 80005ec:	f000 f9ce 	bl	800098c <Error_Handler>
}
 80005f0:	b005      	add	sp, #20
 80005f2:	f85d fb04 	ldr.w	pc, [sp], #4
    Error_Handler();
 80005f6:	f000 f9c9 	bl	800098c <Error_Handler>
 80005fa:	e7e8      	b.n	80005ce <MX_ADC1_Init+0xce>
    Error_Handler();
 80005fc:	f000 f9c6 	bl	800098c <Error_Handler>
 8000600:	e7dc      	b.n	80005bc <MX_ADC1_Init+0xbc>
    Error_Handler();
 8000602:	f000 f9c3 	bl	800098c <Error_Handler>
 8000606:	e7d0      	b.n	80005aa <MX_ADC1_Init+0xaa>
    Error_Handler();
 8000608:	f000 f9c0 	bl	800098c <Error_Handler>
 800060c:	e7c4      	b.n	8000598 <MX_ADC1_Init+0x98>
    Error_Handler();
 800060e:	f000 f9bd 	bl	800098c <Error_Handler>
 8000612:	e7b8      	b.n	8000586 <MX_ADC1_Init+0x86>
    Error_Handler();
 8000614:	f000 f9ba 	bl	800098c <Error_Handler>
 8000618:	e7ab      	b.n	8000572 <MX_ADC1_Init+0x72>
    Error_Handler();
 800061a:	f000 f9b7 	bl	800098c <Error_Handler>
 800061e:	e79e      	b.n	800055e <MX_ADC1_Init+0x5e>
 8000620:	00000001 	.word	0x00000001
 8000624:	00000001 	.word	0x00000001
 8000628:	20000090 	.word	0x20000090
 800062c:	40012000 	.word	0x40012000

08000630 <HAL_ADC_MspInit>:

void HAL_ADC_MspInit(ADC_HandleTypeDef* adcHandle)
{
 8000630:	b570      	push	{r4, r5, r6, lr}

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(adcHandle->Instance==ADC1)
 8000632:	4b38      	ldr	r3, [pc, #224]	@ (8000714 <HAL_ADC_MspInit+0xe4>)
 8000634:	6802      	ldr	r2, [r0, #0]
{
 8000636:	b08a      	sub	sp, #40	@ 0x28
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 8000638:	2400      	movs	r4, #0
  if(adcHandle->Instance==ADC1)
 800063a:	429a      	cmp	r2, r3
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 800063c:	e9cd 4404 	strd	r4, r4, [sp, #16]
 8000640:	e9cd 4406 	strd	r4, r4, [sp, #24]
 8000644:	9408      	str	r4, [sp, #32]
  if(adcHandle->Instance==ADC1)
 8000646:	d001      	beq.n	800064c <HAL_ADC_MspInit+0x1c>

  /* USER CODE BEGIN ADC1_MspInit 1 */

  /* USER CODE END ADC1_MspInit 1 */
  }
}
 8000648:	b00a      	add	sp, #40	@ 0x28
 800064a:	bd70      	pop	{r4, r5, r6, pc}
    __HAL_RCC_ADC1_CLK_ENABLE();
 800064c:	f503 338c 	add.w	r3, r3, #71680	@ 0x11800
 8000650:	9400      	str	r4, [sp, #0]
 8000652:	6c5a      	ldr	r2, [r3, #68]	@ 0x44
    hdma_adc1.Instance = DMA2_Stream0;
 8000654:	4e30      	ldr	r6, [pc, #192]	@ (8000718 <HAL_ADC_MspInit+0xe8>)
    __HAL_RCC_ADC1_CLK_ENABLE();
 8000656:	f442 7280 	orr.w	r2, r2, #256	@ 0x100
 800065a:	645a      	str	r2, [r3, #68]	@ 0x44
 800065c:	6c5a      	ldr	r2, [r3, #68]	@ 0x44
 800065e:	f402 7280 	and.w	r2, r2, #256	@ 0x100
 8000662:	9200      	str	r2, [sp, #0]
 8000664:	9a00      	ldr	r2, [sp, #0]
    __HAL_RCC_GPIOC_CLK_ENABLE();
 8000666:	9401      	str	r4, [sp, #4]
 8000668:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 800066a:	f042 0204 	orr.w	r2, r2, #4
 800066e:	631a      	str	r2, [r3, #48]	@ 0x30
 8000670:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 8000672:	f002 0204 	and.w	r2, r2, #4
 8000676:	9201      	str	r2, [sp, #4]
 8000678:	9a01      	ldr	r2, [sp, #4]
    __HAL_RCC_GPIOA_CLK_ENABLE();
 800067a:	9402      	str	r4, [sp, #8]
 800067c:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 800067e:	f042 0201 	orr.w	r2, r2, #1
 8000682:	631a      	str	r2, [r3, #48]	@ 0x30
 8000684:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 8000686:	f002 0201 	and.w	r2, r2, #1
 800068a:	9202      	str	r2, [sp, #8]
 800068c:	9a02      	ldr	r2, [sp, #8]
    __HAL_RCC_GPIOB_CLK_ENABLE();
 800068e:	9403      	str	r4, [sp, #12]
 8000690:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 8000692:	f042 0202 	orr.w	r2, r2, #2
 8000696:	631a      	str	r2, [r3, #48]	@ 0x30
 8000698:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 800069a:	f003 0302 	and.w	r3, r3, #2
 800069e:	9303      	str	r3, [sp, #12]
    GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_5;
 80006a0:	2227      	movs	r2, #39	@ 0x27
 80006a2:	2303      	movs	r3, #3
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
 80006a4:	a904      	add	r1, sp, #16
 80006a6:	4605      	mov	r5, r0
 80006a8:	481c      	ldr	r0, [pc, #112]	@ (800071c <HAL_ADC_MspInit+0xec>)
    GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_5;
 80006aa:	e9cd 2304 	strd	r2, r3, [sp, #16]
    __HAL_RCC_GPIOB_CLK_ENABLE();
 80006ae:	9b03      	ldr	r3, [sp, #12]
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
 80006b0:	f000 fdb8 	bl	8001224 <HAL_GPIO_Init>
    GPIO_InitStruct.Pin = GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3;
 80006b4:	220e      	movs	r2, #14
 80006b6:	2303      	movs	r3, #3
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 80006b8:	4819      	ldr	r0, [pc, #100]	@ (8000720 <HAL_ADC_MspInit+0xf0>)
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 80006ba:	9406      	str	r4, [sp, #24]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 80006bc:	a904      	add	r1, sp, #16
    GPIO_InitStruct.Pin = GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3;
 80006be:	e9cd 2304 	strd	r2, r3, [sp, #16]
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
 80006c2:	f000 fdaf 	bl	8001224 <HAL_GPIO_Init>
    GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1;
 80006c6:	2203      	movs	r2, #3
 80006c8:	2303      	movs	r3, #3
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 80006ca:	4816      	ldr	r0, [pc, #88]	@ (8000724 <HAL_ADC_MspInit+0xf4>)
    GPIO_InitStruct.Pull = GPIO_NOPULL;
 80006cc:	9406      	str	r4, [sp, #24]
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 80006ce:	a904      	add	r1, sp, #16
    GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1;
 80006d0:	e9cd 2304 	strd	r2, r3, [sp, #16]
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
 80006d4:	f000 fda6 	bl	8001224 <HAL_GPIO_Init>
    hdma_adc1.Instance = DMA2_Stream0;
 80006d8:	4a13      	ldr	r2, [pc, #76]	@ (8000728 <HAL_ADC_MspInit+0xf8>)
    hdma_adc1.Init.FIFOMode = DMA_FIFOMODE_DISABLE;
 80006da:	6274      	str	r4, [r6, #36]	@ 0x24
    hdma_adc1.Init.MemInc = DMA_MINC_ENABLE;
 80006dc:	f44f 6380 	mov.w	r3, #1024	@ 0x400
    hdma_adc1.Init.Channel = DMA_CHANNEL_0;
 80006e0:	e9c6 2400 	strd	r2, r4, [r6]
    hdma_adc1.Init.MemInc = DMA_MINC_ENABLE;
 80006e4:	6133      	str	r3, [r6, #16]
    hdma_adc1.Init.MemDataAlignment = DMA_MDATAALIGN_WORD;
 80006e6:	f44f 5280 	mov.w	r2, #4096	@ 0x1000
 80006ea:	f44f 4380 	mov.w	r3, #16384	@ 0x4000
 80006ee:	e9c6 2305 	strd	r2, r3, [r6, #20]
    if (HAL_DMA_Init(&hdma_adc1) != HAL_OK)
 80006f2:	4630      	mov	r0, r6
    hdma_adc1.Init.Mode = DMA_CIRCULAR;
 80006f4:	f44f 7380 	mov.w	r3, #256	@ 0x100
    hdma_adc1.Init.PeriphInc = DMA_PINC_DISABLE;
 80006f8:	e9c6 4402 	strd	r4, r4, [r6, #8]
    hdma_adc1.Init.Priority = DMA_PRIORITY_LOW;
 80006fc:	e9c6 3407 	strd	r3, r4, [r6, #28]
    if (HAL_DMA_Init(&hdma_adc1) != HAL_OK)
 8000700:	f000 fc26 	bl	8000f50 <HAL_DMA_Init>
 8000704:	b918      	cbnz	r0, 800070e <HAL_ADC_MspInit+0xde>
    __HAL_LINKDMA(adcHandle,DMA_Handle,hdma_adc1);
 8000706:	63ae      	str	r6, [r5, #56]	@ 0x38
 8000708:	63b5      	str	r5, [r6, #56]	@ 0x38
}
 800070a:	b00a      	add	sp, #40	@ 0x28
 800070c:	bd70      	pop	{r4, r5, r6, pc}
      Error_Handler();
 800070e:	f000 f93d 	bl	800098c <Error_Handler>
 8000712:	e7f8      	b.n	8000706 <HAL_ADC_MspInit+0xd6>
 8000714:	40012000 	.word	0x40012000
 8000718:	2000002c 	.word	0x2000002c
 800071c:	40020800 	.word	0x40020800
 8000720:	40020000 	.word	0x40020000
 8000724:	40020400 	.word	0x40020400
 8000728:	40026410 	.word	0x40026410

0800072c <MX_DMA_Init>:

/**
  * Enable DMA controller clock
  */
void MX_DMA_Init(void)
{
 800072c:	b500      	push	{lr}
 800072e:	b083      	sub	sp, #12

  /* DMA controller clock enable */
  __HAL_RCC_DMA2_CLK_ENABLE();
 8000730:	4b0a      	ldr	r3, [pc, #40]	@ (800075c <MX_DMA_Init+0x30>)
 8000732:	2200      	movs	r2, #0
 8000734:	9201      	str	r2, [sp, #4]
 8000736:	6b19      	ldr	r1, [r3, #48]	@ 0x30
 8000738:	f441 0180 	orr.w	r1, r1, #4194304	@ 0x400000
 800073c:	6319      	str	r1, [r3, #48]	@ 0x30
 800073e:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8000740:	f403 0380 	and.w	r3, r3, #4194304	@ 0x400000
 8000744:	9301      	str	r3, [sp, #4]

  /* DMA interrupt init */
  /* DMA2_Stream0_IRQn interrupt configuration */
  HAL_NVIC_SetPriority(DMA2_Stream0_IRQn, 5, 0);
 8000746:	2105      	movs	r1, #5
 8000748:	2038      	movs	r0, #56	@ 0x38
  __HAL_RCC_DMA2_CLK_ENABLE();
 800074a:	9b01      	ldr	r3, [sp, #4]
  HAL_NVIC_SetPriority(DMA2_Stream0_IRQn, 5, 0);
 800074c:	f000 fb9c 	bl	8000e88 <HAL_NVIC_SetPriority>
  HAL_NVIC_EnableIRQ(DMA2_Stream0_IRQn);
 8000750:	2038      	movs	r0, #56	@ 0x38

}
 8000752:	b003      	add	sp, #12
 8000754:	f85d eb04 	ldr.w	lr, [sp], #4
  HAL_NVIC_EnableIRQ(DMA2_Stream0_IRQn);
 8000758:	f000 bbd2 	b.w	8000f00 <HAL_NVIC_EnableIRQ>
 800075c:	40023800 	.word	0x40023800

08000760 <StartDefaultTask>:
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartDefaultTask */
void StartDefaultTask(void *argument)
{
 8000760:	b508      	push	{r3, lr}
  /* USER CODE BEGIN StartDefaultTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
 8000762:	2001      	movs	r0, #1
 8000764:	f001 fb2c 	bl	8001dc0 <osDelay>
  for(;;)
 8000768:	e7fb      	b.n	8000762 <StartDefaultTask+0x2>
 800076a:	bf00      	nop

0800076c <MX_FREERTOS_Init>:
void MX_FREERTOS_Init(void) {
 800076c:	b508      	push	{r3, lr}
  defaultTaskHandle = osThreadNew(StartDefaultTask, NULL, &defaultTask_attributes);
 800076e:	4a04      	ldr	r2, [pc, #16]	@ (8000780 <MX_FREERTOS_Init+0x14>)
 8000770:	4804      	ldr	r0, [pc, #16]	@ (8000784 <MX_FREERTOS_Init+0x18>)
 8000772:	2100      	movs	r1, #0
 8000774:	f001 fade 	bl	8001d34 <osThreadNew>
 8000778:	4b03      	ldr	r3, [pc, #12]	@ (8000788 <MX_FREERTOS_Init+0x1c>)
 800077a:	6018      	str	r0, [r3, #0]
}
 800077c:	bd08      	pop	{r3, pc}
 800077e:	bf00      	nop
 8000780:	08003960 	.word	0x08003960
 8000784:	08000761 	.word	0x08000761
 8000788:	200000d8 	.word	0x200000d8

0800078c <MX_GPIO_Init>:
        * Output
        * EVENT_OUT
        * EXTI
*/
void MX_GPIO_Init(void)
{
 800078c:	b5f0      	push	{r4, r5, r6, r7, lr}

  GPIO_InitTypeDef GPIO_InitStruct = {0};
 800078e:	2400      	movs	r4, #0
{
 8000790:	b08f      	sub	sp, #60	@ 0x3c
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 8000792:	e9cd 4408 	strd	r4, r4, [sp, #32]
 8000796:	e9cd 440a 	strd	r4, r4, [sp, #40]	@ 0x28

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOC_CLK_ENABLE();
 800079a:	4b43      	ldr	r3, [pc, #268]	@ (80008a8 <MX_GPIO_Init+0x11c>)
 800079c:	9401      	str	r4, [sp, #4]
  GPIO_InitTypeDef GPIO_InitStruct = {0};
 800079e:	940c      	str	r4, [sp, #48]	@ 0x30
  __HAL_RCC_GPIOC_CLK_ENABLE();
 80007a0:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
  __HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_GPIOD_CLK_ENABLE();
  __HAL_RCC_GPIOG_CLK_ENABLE();

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11
 80007a2:	4d42      	ldr	r5, [pc, #264]	@ (80008ac <MX_GPIO_Init+0x120>)
                          |GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOG, GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5
 80007a4:	4f42      	ldr	r7, [pc, #264]	@ (80008b0 <MX_GPIO_Init+0x124>)
                          |GPIO_PIN_6|GPIO_PIN_7|GPIO_PIN_8, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_6, GPIO_PIN_RESET);
 80007a6:	4e43      	ldr	r6, [pc, #268]	@ (80008b4 <MX_GPIO_Init+0x128>)
  __HAL_RCC_GPIOC_CLK_ENABLE();
 80007a8:	f042 0204 	orr.w	r2, r2, #4
 80007ac:	631a      	str	r2, [r3, #48]	@ 0x30
 80007ae:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 80007b0:	f002 0204 	and.w	r2, r2, #4
 80007b4:	9201      	str	r2, [sp, #4]
 80007b6:	9a01      	ldr	r2, [sp, #4]
  __HAL_RCC_GPIOF_CLK_ENABLE();
 80007b8:	9402      	str	r4, [sp, #8]
 80007ba:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 80007bc:	f042 0220 	orr.w	r2, r2, #32
 80007c0:	631a      	str	r2, [r3, #48]	@ 0x30
 80007c2:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 80007c4:	f002 0220 	and.w	r2, r2, #32
 80007c8:	9202      	str	r2, [sp, #8]
 80007ca:	9a02      	ldr	r2, [sp, #8]
  __HAL_RCC_GPIOH_CLK_ENABLE();
 80007cc:	9403      	str	r4, [sp, #12]
 80007ce:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 80007d0:	f042 0280 	orr.w	r2, r2, #128	@ 0x80
 80007d4:	631a      	str	r2, [r3, #48]	@ 0x30
 80007d6:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 80007d8:	f002 0280 	and.w	r2, r2, #128	@ 0x80
 80007dc:	9203      	str	r2, [sp, #12]
 80007de:	9a03      	ldr	r2, [sp, #12]
  __HAL_RCC_GPIOA_CLK_ENABLE();
 80007e0:	9404      	str	r4, [sp, #16]
 80007e2:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 80007e4:	f042 0201 	orr.w	r2, r2, #1
 80007e8:	631a      	str	r2, [r3, #48]	@ 0x30
 80007ea:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 80007ec:	f002 0201 	and.w	r2, r2, #1
 80007f0:	9204      	str	r2, [sp, #16]
 80007f2:	9a04      	ldr	r2, [sp, #16]
  __HAL_RCC_GPIOB_CLK_ENABLE();
 80007f4:	9405      	str	r4, [sp, #20]
 80007f6:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 80007f8:	f042 0202 	orr.w	r2, r2, #2
 80007fc:	631a      	str	r2, [r3, #48]	@ 0x30
 80007fe:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 8000800:	f002 0202 	and.w	r2, r2, #2
 8000804:	9205      	str	r2, [sp, #20]
 8000806:	9a05      	ldr	r2, [sp, #20]
  __HAL_RCC_GPIOD_CLK_ENABLE();
 8000808:	9406      	str	r4, [sp, #24]
 800080a:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 800080c:	f042 0208 	orr.w	r2, r2, #8
 8000810:	631a      	str	r2, [r3, #48]	@ 0x30
 8000812:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 8000814:	f002 0208 	and.w	r2, r2, #8
 8000818:	9206      	str	r2, [sp, #24]
 800081a:	9a06      	ldr	r2, [sp, #24]
  __HAL_RCC_GPIOG_CLK_ENABLE();
 800081c:	9407      	str	r4, [sp, #28]
 800081e:	6b1a      	ldr	r2, [r3, #48]	@ 0x30
 8000820:	f042 0240 	orr.w	r2, r2, #64	@ 0x40
 8000824:	631a      	str	r2, [r3, #48]	@ 0x30
 8000826:	6b1b      	ldr	r3, [r3, #48]	@ 0x30
 8000828:	f003 0340 	and.w	r3, r3, #64	@ 0x40
 800082c:	9307      	str	r3, [sp, #28]
  HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11
 800082e:	4622      	mov	r2, r4
 8000830:	4628      	mov	r0, r5
 8000832:	f44f 417f 	mov.w	r1, #65280	@ 0xff00
  __HAL_RCC_GPIOG_CLK_ENABLE();
 8000836:	9b07      	ldr	r3, [sp, #28]
  HAL_GPIO_WritePin(GPIOD, GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11
 8000838:	f000 fe10 	bl	800145c <HAL_GPIO_WritePin>
  HAL_GPIO_WritePin(GPIOG, GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5
 800083c:	4622      	mov	r2, r4
 800083e:	4638      	mov	r0, r7
 8000840:	f44f 71fe 	mov.w	r1, #508	@ 0x1fc
 8000844:	f000 fe0a 	bl	800145c <HAL_GPIO_WritePin>
  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_6, GPIO_PIN_RESET);
 8000848:	4622      	mov	r2, r4
 800084a:	4630      	mov	r0, r6
 800084c:	2140      	movs	r1, #64	@ 0x40
 800084e:	f000 fe05 	bl	800145c <HAL_GPIO_WritePin>

  /*Configure GPIO pins : PF0 PF1 PF2 PF3
                           PF4 PF5 PF6 PF7
                           PF8 PF9 PF10 */
  GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3
 8000852:	f240 72ff 	movw	r2, #2047	@ 0x7ff
                          |GPIO_PIN_4|GPIO_PIN_5|GPIO_PIN_6|GPIO_PIN_7
                          |GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOF, &GPIO_InitStruct);
 8000856:	a908      	add	r1, sp, #32
  GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3
 8000858:	2300      	movs	r3, #0
  HAL_GPIO_Init(GPIOF, &GPIO_InitStruct);
 800085a:	4817      	ldr	r0, [pc, #92]	@ (80008b8 <MX_GPIO_Init+0x12c>)
  GPIO_InitStruct.Pull = GPIO_NOPULL;
 800085c:	940a      	str	r4, [sp, #40]	@ 0x28
  GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1|GPIO_PIN_2|GPIO_PIN_3
 800085e:	e9cd 2308 	strd	r2, r3, [sp, #32]
  HAL_GPIO_Init(GPIOF, &GPIO_InitStruct);
 8000862:	f000 fcdf 	bl	8001224 <HAL_GPIO_Init>
  GPIO_InitStruct.Pin = GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10|GPIO_PIN_11
                          |GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14|GPIO_PIN_15;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
 8000866:	4628      	mov	r0, r5
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 8000868:	f44f 437f 	mov.w	r3, #65280	@ 0xff00
 800086c:	2501      	movs	r5, #1
  HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
 800086e:	a908      	add	r1, sp, #32
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 8000870:	e9cd 3508 	strd	r3, r5, [sp, #32]
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 8000874:	e9cd 440a 	strd	r4, r4, [sp, #40]	@ 0x28
  HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
 8000878:	f000 fcd4 	bl	8001224 <HAL_GPIO_Init>
  GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5
                          |GPIO_PIN_6|GPIO_PIN_7|GPIO_PIN_8;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);
 800087c:	4638      	mov	r0, r7
  GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5
 800087e:	f44f 73fe 	mov.w	r3, #508	@ 0x1fc
  HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);
 8000882:	a908      	add	r1, sp, #32
  GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5
 8000884:	9308      	str	r3, [sp, #32]
  GPIO_InitStruct.Pull = GPIO_NOPULL;
 8000886:	e9cd 5409 	strd	r5, r4, [sp, #36]	@ 0x24
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 800088a:	940b      	str	r4, [sp, #44]	@ 0x2c
  HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);
 800088c:	f000 fcca 	bl	8001224 <HAL_GPIO_Init>

  /*Configure GPIO pin : PC6 */
  GPIO_InitStruct.Pin = GPIO_PIN_6;
 8000890:	2340      	movs	r3, #64	@ 0x40
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
 8000892:	a908      	add	r1, sp, #32
 8000894:	4630      	mov	r0, r6
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
 8000896:	e9cd 3508 	strd	r3, r5, [sp, #32]
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
 800089a:	e9cd 440a 	strd	r4, r4, [sp, #40]	@ 0x28
  HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
 800089e:	f000 fcc1 	bl	8001224 <HAL_GPIO_Init>

}
 80008a2:	b00f      	add	sp, #60	@ 0x3c
 80008a4:	bdf0      	pop	{r4, r5, r6, r7, pc}
 80008a6:	bf00      	nop
 80008a8:	40023800 	.word	0x40023800
 80008ac:	40020c00 	.word	0x40020c00
 80008b0:	40021800 	.word	0x40021800
 80008b4:	40020800 	.word	0x40020800
 80008b8:	40021400 	.word	0x40021400

080008bc <SystemClock_Config>:
/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
 80008bc:	b530      	push	{r4, r5, lr}
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 80008be:	2300      	movs	r3, #0
{
 80008c0:	b095      	sub	sp, #84	@ 0x54
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
 80008c2:	e9cd 330a 	strd	r3, r3, [sp, #40]	@ 0x28
 80008c6:	e9cd 330c 	strd	r3, r3, [sp, #48]	@ 0x30
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 80008ca:	e9cd 3303 	strd	r3, r3, [sp, #12]
 80008ce:	e9cd 3305 	strd	r3, r3, [sp, #20]

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
 80008d2:	4922      	ldr	r1, [pc, #136]	@ (800095c <SystemClock_Config+0xa0>)
 80008d4:	9301      	str	r3, [sp, #4]
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
 80008d6:	9307      	str	r3, [sp, #28]
  __HAL_RCC_PWR_CLK_ENABLE();
 80008d8:	6c08      	ldr	r0, [r1, #64]	@ 0x40
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
 80008da:	4a21      	ldr	r2, [pc, #132]	@ (8000960 <SystemClock_Config+0xa4>)
  __HAL_RCC_PWR_CLK_ENABLE();
 80008dc:	f040 5080 	orr.w	r0, r0, #268435456	@ 0x10000000
 80008e0:	6408      	str	r0, [r1, #64]	@ 0x40
 80008e2:	6c09      	ldr	r1, [r1, #64]	@ 0x40
 80008e4:	f001 5180 	and.w	r1, r1, #268435456	@ 0x10000000
 80008e8:	9101      	str	r1, [sp, #4]
 80008ea:	9901      	ldr	r1, [sp, #4]
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
 80008ec:	9302      	str	r3, [sp, #8]
 80008ee:	6813      	ldr	r3, [r2, #0]
 80008f0:	f443 4380 	orr.w	r3, r3, #16384	@ 0x4000
 80008f4:	6013      	str	r3, [r2, #0]
 80008f6:	6813      	ldr	r3, [r2, #0]

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
 80008f8:	2001      	movs	r0, #1
 80008fa:	f44f 3180 	mov.w	r1, #65536	@ 0x10000
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
 80008fe:	f403 4380 	and.w	r3, r3, #16384	@ 0x4000
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
 8000902:	e9cd 0108 	strd	r0, r1, [sp, #32]
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
 8000906:	9302      	str	r3, [sp, #8]
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
 8000908:	f44f 0180 	mov.w	r1, #4194304	@ 0x400000
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
 800090c:	2402      	movs	r4, #2
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
 800090e:	e9cd 410e 	strd	r4, r1, [sp, #56]	@ 0x38
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
 8000912:	9802      	ldr	r0, [sp, #8]
  RCC_OscInitStruct.PLL.PLLM = 25;
  RCC_OscInitStruct.PLL.PLLN = 336;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
 8000914:	2104      	movs	r1, #4
 8000916:	2002      	movs	r0, #2
 8000918:	e9cd 0112 	strd	r0, r1, [sp, #72]	@ 0x48
  RCC_OscInitStruct.PLL.PLLM = 25;
 800091c:	2219      	movs	r2, #25
  RCC_OscInitStruct.PLL.PLLN = 336;
 800091e:	f44f 73a8 	mov.w	r3, #336	@ 0x150
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 8000922:	a808      	add	r0, sp, #32
  RCC_OscInitStruct.PLL.PLLM = 25;
 8000924:	9210      	str	r2, [sp, #64]	@ 0x40
  RCC_OscInitStruct.PLL.PLLN = 336;
 8000926:	9311      	str	r3, [sp, #68]	@ 0x44
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
 8000928:	f000 fd9c 	bl	8001464 <HAL_RCC_OscConfig>
 800092c:	b108      	cbz	r0, 8000932 <SystemClock_Config+0x76>
  \details Disables IRQ interrupts by setting special-purpose register PRIMASK.
           Can only be executed in Privileged modes.
 */
__STATIC_FORCEINLINE void __disable_irq(void)
{
  __ASM volatile ("cpsid i" : : : "memory");
 800092e:	b672      	cpsid	i
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
 8000930:	e7fe      	b.n	8000930 <SystemClock_Config+0x74>
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
 8000932:	220f      	movs	r2, #15
 8000934:	4603      	mov	r3, r0
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
 8000936:	e9cd 2403 	strd	r2, r4, [sp, #12]
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
 800093a:	f44f 55a0 	mov.w	r5, #5120	@ 0x1400
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;
 800093e:	f44f 5280 	mov.w	r2, #4096	@ 0x1000
  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
 8000942:	a803      	add	r0, sp, #12
 8000944:	2105      	movs	r1, #5
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
 8000946:	e9cd 3505 	strd	r3, r5, [sp, #20]
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;
 800094a:	9207      	str	r2, [sp, #28]
  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
 800094c:	f000 ffa0 	bl	8001890 <HAL_RCC_ClockConfig>
 8000950:	b108      	cbz	r0, 8000956 <SystemClock_Config+0x9a>
 8000952:	b672      	cpsid	i
  while (1)
 8000954:	e7fe      	b.n	8000954 <SystemClock_Config+0x98>
}
 8000956:	b015      	add	sp, #84	@ 0x54
 8000958:	bd30      	pop	{r4, r5, pc}
 800095a:	bf00      	nop
 800095c:	40023800 	.word	0x40023800
 8000960:	40007000 	.word	0x40007000

08000964 <main>:
{
 8000964:	b508      	push	{r3, lr}
  HAL_Init();
 8000966:	f000 f8fd 	bl	8000b64 <HAL_Init>
  SystemClock_Config();
 800096a:	f7ff ffa7 	bl	80008bc <SystemClock_Config>
  MX_GPIO_Init();
 800096e:	f7ff ff0d 	bl	800078c <MX_GPIO_Init>
  MX_DMA_Init();
 8000972:	f7ff fedb 	bl	800072c <MX_DMA_Init>
  MX_ADC1_Init();
 8000976:	f7ff fdc3 	bl	8000500 <MX_ADC1_Init>
  MX_TIM4_Init();
 800097a:	f000 f851 	bl	8000a20 <MX_TIM4_Init>
  osKernelInitialize();  /* Call init function for freertos objects (in cmsis_os2.c) */
 800097e:	f001 f9ad 	bl	8001cdc <osKernelInitialize>
  MX_FREERTOS_Init();
 8000982:	f7ff fef3 	bl	800076c <MX_FREERTOS_Init>
  osKernelStart();
 8000986:	f001 f9bb 	bl	8001d00 <osKernelStart>
  while (1)
 800098a:	e7fe      	b.n	800098a <main+0x26>

0800098c <Error_Handler>:
 800098c:	b672      	cpsid	i
  while (1)
 800098e:	e7fe      	b.n	800098e <Error_Handler+0x2>

08000990 <HAL_MspInit>:
/* USER CODE END 0 */
/**
  * Initializes the Global MSP.
  */
void HAL_MspInit(void)
{
 8000990:	b082      	sub	sp, #8

  /* USER CODE BEGIN MspInit 0 */

  /* USER CODE END MspInit 0 */

  __HAL_RCC_SYSCFG_CLK_ENABLE();
 8000992:	4b0e      	ldr	r3, [pc, #56]	@ (80009cc <HAL_MspInit+0x3c>)
 8000994:	2200      	movs	r2, #0
 8000996:	9200      	str	r2, [sp, #0]
 8000998:	6c59      	ldr	r1, [r3, #68]	@ 0x44
 800099a:	f441 4180 	orr.w	r1, r1, #16384	@ 0x4000
 800099e:	6459      	str	r1, [r3, #68]	@ 0x44
 80009a0:	6c58      	ldr	r0, [r3, #68]	@ 0x44
 80009a2:	f400 4080 	and.w	r0, r0, #16384	@ 0x4000
 80009a6:	9000      	str	r0, [sp, #0]
 80009a8:	9800      	ldr	r0, [sp, #0]
  __HAL_RCC_PWR_CLK_ENABLE();
 80009aa:	9201      	str	r2, [sp, #4]
 80009ac:	6c18      	ldr	r0, [r3, #64]	@ 0x40
 80009ae:	f040 5080 	orr.w	r0, r0, #268435456	@ 0x10000000
 80009b2:	6418      	str	r0, [r3, #64]	@ 0x40
 80009b4:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 80009b6:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 80009ba:	9301      	str	r3, [sp, #4]
 80009bc:	9b01      	ldr	r3, [sp, #4]

  /* System interrupt init*/
  /* PendSV_IRQn interrupt configuration */
  HAL_NVIC_SetPriority(PendSV_IRQn, 15, 0);
 80009be:	210f      	movs	r1, #15
 80009c0:	f06f 0001 	mvn.w	r0, #1

  /* USER CODE BEGIN MspInit 1 */

  /* USER CODE END MspInit 1 */
}
 80009c4:	b002      	add	sp, #8
  HAL_NVIC_SetPriority(PendSV_IRQn, 15, 0);
 80009c6:	f000 ba5f 	b.w	8000e88 <HAL_NVIC_SetPriority>
 80009ca:	bf00      	nop
 80009cc:	40023800 	.word	0x40023800

080009d0 <NMI_Handler>:
{
  /* USER CODE BEGIN NonMaskableInt_IRQn 0 */

  /* USER CODE END NonMaskableInt_IRQn 0 */
  /* USER CODE BEGIN NonMaskableInt_IRQn 1 */
   while (1)
 80009d0:	e7fe      	b.n	80009d0 <NMI_Handler>
 80009d2:	bf00      	nop

080009d4 <HardFault_Handler>:
void HardFault_Handler(void)
{
  /* USER CODE BEGIN HardFault_IRQn 0 */

  /* USER CODE END HardFault_IRQn 0 */
  while (1)
 80009d4:	e7fe      	b.n	80009d4 <HardFault_Handler>
 80009d6:	bf00      	nop

080009d8 <MemManage_Handler>:
void MemManage_Handler(void)
{
  /* USER CODE BEGIN MemoryManagement_IRQn 0 */

  /* USER CODE END MemoryManagement_IRQn 0 */
  while (1)
 80009d8:	e7fe      	b.n	80009d8 <MemManage_Handler>
 80009da:	bf00      	nop

080009dc <BusFault_Handler>:
void BusFault_Handler(void)
{
  /* USER CODE BEGIN BusFault_IRQn 0 */

  /* USER CODE END BusFault_IRQn 0 */
  while (1)
 80009dc:	e7fe      	b.n	80009dc <BusFault_Handler>
 80009de:	bf00      	nop

080009e0 <UsageFault_Handler>:
void UsageFault_Handler(void)
{
  /* USER CODE BEGIN UsageFault_IRQn 0 */

  /* USER CODE END UsageFault_IRQn 0 */
  while (1)
 80009e0:	e7fe      	b.n	80009e0 <UsageFault_Handler>
 80009e2:	bf00      	nop

080009e4 <DebugMon_Handler>:

  /* USER CODE END DebugMonitor_IRQn 0 */
  /* USER CODE BEGIN DebugMonitor_IRQn 1 */

  /* USER CODE END DebugMonitor_IRQn 1 */
}
 80009e4:	4770      	bx	lr
 80009e6:	bf00      	nop

080009e8 <SysTick_Handler>:

/**
  * @brief This function handles System tick timer.
  */
void SysTick_Handler(void)
{
 80009e8:	b508      	push	{r3, lr}
  /* USER CODE BEGIN SysTick_IRQn 0 */

  /* USER CODE END SysTick_IRQn 0 */
  HAL_IncTick();
 80009ea:	f000 f8d5 	bl	8000b98 <HAL_IncTick>
#if (INCLUDE_xTaskGetSchedulerState == 1 )
  if (xTaskGetSchedulerState() != taskSCHEDULER_NOT_STARTED)
 80009ee:	f002 fa4f 	bl	8002e90 <xTaskGetSchedulerState>
 80009f2:	2801      	cmp	r0, #1
 80009f4:	d100      	bne.n	80009f8 <SysTick_Handler+0x10>
  }
#endif /* INCLUDE_xTaskGetSchedulerState */
  /* USER CODE BEGIN SysTick_IRQn 1 */

  /* USER CODE END SysTick_IRQn 1 */
}
 80009f6:	bd08      	pop	{r3, pc}
 80009f8:	e8bd 4008 	ldmia.w	sp!, {r3, lr}
  xPortSysTickHandler();
 80009fc:	f002 bd72 	b.w	80034e4 <xPortSysTickHandler>

08000a00 <DMA2_Stream0_IRQHandler>:
void DMA2_Stream0_IRQHandler(void)
{
  /* USER CODE BEGIN DMA2_Stream0_IRQn 0 */

  /* USER CODE END DMA2_Stream0_IRQn 0 */
  HAL_DMA_IRQHandler(&hdma_adc1);
 8000a00:	4801      	ldr	r0, [pc, #4]	@ (8000a08 <DMA2_Stream0_IRQHandler+0x8>)
 8000a02:	f000 bb39 	b.w	8001078 <HAL_DMA_IRQHandler>
 8000a06:	bf00      	nop
 8000a08:	2000002c 	.word	0x2000002c

08000a0c <SystemInit>:
  */
void SystemInit(void)
{
  /* FPU settings ------------------------------------------------------------*/
  #if (__FPU_PRESENT == 1) && (__FPU_USED == 1)
    SCB->CPACR |= ((3UL << 10*2)|(3UL << 11*2));  /* set CP10 and CP11 Full Access */
 8000a0c:	4a03      	ldr	r2, [pc, #12]	@ (8000a1c <SystemInit+0x10>)
 8000a0e:	f8d2 3088 	ldr.w	r3, [r2, #136]	@ 0x88
 8000a12:	f443 0370 	orr.w	r3, r3, #15728640	@ 0xf00000
 8000a16:	f8c2 3088 	str.w	r3, [r2, #136]	@ 0x88

  /* Configure the Vector Table location -------------------------------------*/
#if defined(USER_VECT_TAB_ADDRESS)
  SCB->VTOR = VECT_TAB_BASE_ADDRESS | VECT_TAB_OFFSET; /* Vector Table Relocation in Internal SRAM */
#endif /* USER_VECT_TAB_ADDRESS */
}
 8000a1a:	4770      	bx	lr
 8000a1c:	e000ed00 	.word	0xe000ed00

08000a20 <MX_TIM4_Init>:

TIM_HandleTypeDef htim4;

/* TIM4 init function */
void MX_TIM4_Init(void)
{
 8000a20:	b500      	push	{lr}
  TIM_MasterConfigTypeDef sMasterConfig = {0};

  /* USER CODE BEGIN TIM4_Init 1 */

  /* USER CODE END TIM4_Init 1 */
  htim4.Instance = TIM4;
 8000a22:	481b      	ldr	r0, [pc, #108]	@ (8000a90 <MX_TIM4_Init+0x70>)
 8000a24:	4a1b      	ldr	r2, [pc, #108]	@ (8000a94 <MX_TIM4_Init+0x74>)
{
 8000a26:	b087      	sub	sp, #28
  htim4.Instance = TIM4;
 8000a28:	6002      	str	r2, [r0, #0]
  htim4.Init.Prescaler = 41;
 8000a2a:	2229      	movs	r2, #41	@ 0x29
  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
 8000a2c:	2300      	movs	r3, #0
  htim4.Init.Prescaler = 41;
 8000a2e:	6042      	str	r2, [r0, #4]
  htim4.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim4.Init.Period = 624;
 8000a30:	f44f 721c 	mov.w	r2, #624	@ 0x270
 8000a34:	e9c0 3202 	strd	r3, r2, [r0, #8]
  htim4.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim4.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
 8000a38:	2280      	movs	r2, #128	@ 0x80
  TIM_ClockConfigTypeDef sClockSourceConfig = {0};
 8000a3a:	e9cd 3302 	strd	r3, r3, [sp, #8]
 8000a3e:	e9cd 3304 	strd	r3, r3, [sp, #16]
  TIM_MasterConfigTypeDef sMasterConfig = {0};
 8000a42:	e9cd 3300 	strd	r3, r3, [sp]
  htim4.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
 8000a46:	6103      	str	r3, [r0, #16]
  htim4.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_ENABLE;
 8000a48:	6182      	str	r2, [r0, #24]
  if (HAL_TIM_Base_Init(&htim4) != HAL_OK)
 8000a4a:	f001 f8d9 	bl	8001c00 <HAL_TIM_Base_Init>
 8000a4e:	b998      	cbnz	r0, 8000a78 <MX_TIM4_Init+0x58>
  {
    Error_Handler();
  }
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
 8000a50:	f44f 5380 	mov.w	r3, #4096	@ 0x1000
  if (HAL_TIM_ConfigClockSource(&htim4, &sClockSourceConfig) != HAL_OK)
 8000a54:	480e      	ldr	r0, [pc, #56]	@ (8000a90 <MX_TIM4_Init+0x70>)
  sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
 8000a56:	9302      	str	r3, [sp, #8]
  if (HAL_TIM_ConfigClockSource(&htim4, &sClockSourceConfig) != HAL_OK)
 8000a58:	a902      	add	r1, sp, #8
 8000a5a:	f000 ffb7 	bl	80019cc <HAL_TIM_ConfigClockSource>
 8000a5e:	b998      	cbnz	r0, 8000a88 <MX_TIM4_Init+0x68>
  {
    Error_Handler();
  }
  sMasterConfig.MasterOutputTrigger = TIM_TRGO_OC1;
 8000a60:	2230      	movs	r2, #48	@ 0x30
 8000a62:	2380      	movs	r3, #128	@ 0x80
  sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_ENABLE;
  if (HAL_TIMEx_MasterConfigSynchronization(&htim4, &sMasterConfig) != HAL_OK)
 8000a64:	480a      	ldr	r0, [pc, #40]	@ (8000a90 <MX_TIM4_Init+0x70>)
 8000a66:	4669      	mov	r1, sp
  sMasterConfig.MasterOutputTrigger = TIM_TRGO_OC1;
 8000a68:	e9cd 2300 	strd	r2, r3, [sp]
  if (HAL_TIMEx_MasterConfigSynchronization(&htim4, &sMasterConfig) != HAL_OK)
 8000a6c:	f001 f8f6 	bl	8001c5c <HAL_TIMEx_MasterConfigSynchronization>
 8000a70:	b928      	cbnz	r0, 8000a7e <MX_TIM4_Init+0x5e>
  }
  /* USER CODE BEGIN TIM4_Init 2 */

  /* USER CODE END TIM4_Init 2 */

}
 8000a72:	b007      	add	sp, #28
 8000a74:	f85d fb04 	ldr.w	pc, [sp], #4
    Error_Handler();
 8000a78:	f7ff ff88 	bl	800098c <Error_Handler>
 8000a7c:	e7e8      	b.n	8000a50 <MX_TIM4_Init+0x30>
    Error_Handler();
 8000a7e:	f7ff ff85 	bl	800098c <Error_Handler>
}
 8000a82:	b007      	add	sp, #28
 8000a84:	f85d fb04 	ldr.w	pc, [sp], #4
    Error_Handler();
 8000a88:	f7ff ff80 	bl	800098c <Error_Handler>
 8000a8c:	e7e8      	b.n	8000a60 <MX_TIM4_Init+0x40>
 8000a8e:	bf00      	nop
 8000a90:	200000dc 	.word	0x200000dc
 8000a94:	40000800 	.word	0x40000800

08000a98 <HAL_TIM_Base_MspInit>:

void HAL_TIM_Base_MspInit(TIM_HandleTypeDef* tim_baseHandle)
{

  if(tim_baseHandle->Instance==TIM4)
 8000a98:	4b0a      	ldr	r3, [pc, #40]	@ (8000ac4 <HAL_TIM_Base_MspInit+0x2c>)
 8000a9a:	6802      	ldr	r2, [r0, #0]
 8000a9c:	429a      	cmp	r2, r3
 8000a9e:	d000      	beq.n	8000aa2 <HAL_TIM_Base_MspInit+0xa>
 8000aa0:	4770      	bx	lr
{
 8000aa2:	b082      	sub	sp, #8
  {
  /* USER CODE BEGIN TIM4_MspInit 0 */

  /* USER CODE END TIM4_MspInit 0 */
    /* TIM4 clock enable */
    __HAL_RCC_TIM4_CLK_ENABLE();
 8000aa4:	f503 330c 	add.w	r3, r3, #143360	@ 0x23000
 8000aa8:	2200      	movs	r2, #0
 8000aaa:	9201      	str	r2, [sp, #4]
 8000aac:	6c1a      	ldr	r2, [r3, #64]	@ 0x40
 8000aae:	f042 0204 	orr.w	r2, r2, #4
 8000ab2:	641a      	str	r2, [r3, #64]	@ 0x40
 8000ab4:	6c1b      	ldr	r3, [r3, #64]	@ 0x40
 8000ab6:	f003 0304 	and.w	r3, r3, #4
 8000aba:	9301      	str	r3, [sp, #4]
 8000abc:	9b01      	ldr	r3, [sp, #4]
  /* USER CODE BEGIN TIM4_MspInit 1 */

  /* USER CODE END TIM4_MspInit 1 */
  }
}
 8000abe:	b002      	add	sp, #8
 8000ac0:	4770      	bx	lr
 8000ac2:	bf00      	nop
 8000ac4:	40000800 	.word	0x40000800

08000ac8 <Reset_Handler>:

    .section  .text.Reset_Handler
  .weak  Reset_Handler
  .type  Reset_Handler, %function
Reset_Handler:  
  ldr   sp, =_estack     /* set stack pointer */
 8000ac8:	f8df d034 	ldr.w	sp, [pc, #52]	@ 8000b00 <LoopFillZerobss+0xe>
  
/* Call the clock system initialization function.*/
  bl  SystemInit  
 8000acc:	f7ff ff9e 	bl	8000a0c <SystemInit>

/* Copy the data segment initializers from flash to SRAM */  
  ldr r0, =_sdata
 8000ad0:	480c      	ldr	r0, [pc, #48]	@ (8000b04 <LoopFillZerobss+0x12>)
  ldr r1, =_edata
 8000ad2:	490d      	ldr	r1, [pc, #52]	@ (8000b08 <LoopFillZerobss+0x16>)
  ldr r2, =_sidata
 8000ad4:	4a0d      	ldr	r2, [pc, #52]	@ (8000b0c <LoopFillZerobss+0x1a>)
  movs r3, #0
 8000ad6:	2300      	movs	r3, #0
  b LoopCopyDataInit
 8000ad8:	e002      	b.n	8000ae0 <LoopCopyDataInit>

08000ada <CopyDataInit>:

CopyDataInit:
  ldr r4, [r2, r3]
 8000ada:	58d4      	ldr	r4, [r2, r3]
  str r4, [r0, r3]
 8000adc:	50c4      	str	r4, [r0, r3]
  adds r3, r3, #4
 8000ade:	3304      	adds	r3, #4

08000ae0 <LoopCopyDataInit>:

LoopCopyDataInit:
  adds r4, r0, r3
 8000ae0:	18c4      	adds	r4, r0, r3
  cmp r4, r1
 8000ae2:	428c      	cmp	r4, r1
  bcc CopyDataInit
 8000ae4:	d3f9      	bcc.n	8000ada <CopyDataInit>
  
/* Zero fill the bss segment. */
  ldr r2, =_sbss
 8000ae6:	4a0a      	ldr	r2, [pc, #40]	@ (8000b10 <LoopFillZerobss+0x1e>)
  ldr r4, =_ebss
 8000ae8:	4c0a      	ldr	r4, [pc, #40]	@ (8000b14 <LoopFillZerobss+0x22>)
  movs r3, #0
 8000aea:	2300      	movs	r3, #0
  b LoopFillZerobss
 8000aec:	e001      	b.n	8000af2 <LoopFillZerobss>

08000aee <FillZerobss>:

FillZerobss:
  str  r3, [r2]
 8000aee:	6013      	str	r3, [r2, #0]
  adds r2, r2, #4
 8000af0:	3204      	adds	r2, #4

08000af2 <LoopFillZerobss>:

LoopFillZerobss:
  cmp r2, r4
 8000af2:	42a2      	cmp	r2, r4
  bcc FillZerobss
 8000af4:	d3fb      	bcc.n	8000aee <FillZerobss>

/* Call static constructors */
    bl __libc_init_array
 8000af6:	f002 fee3 	bl	80038c0 <__libc_init_array>
/* Call the application's entry point.*/
  bl  main
 8000afa:	f7ff ff33 	bl	8000964 <main>
  bx  lr    
 8000afe:	4770      	bx	lr
  ldr   sp, =_estack     /* set stack pointer */
 8000b00:	20020000 	.word	0x20020000
  ldr r0, =_sdata
 8000b04:	20000000 	.word	0x20000000
  ldr r1, =_edata
 8000b08:	20000010 	.word	0x20000010
  ldr r2, =_sidata
 8000b0c:	080039ac 	.word	0x080039ac
  ldr r2, =_sbss
 8000b10:	20000010 	.word	0x20000010
  ldr r4, =_ebss
 8000b14:	20004a78 	.word	0x20004a78

08000b18 <ADC_IRQHandler>:
 * @retval None       
*/
    .section  .text.Default_Handler,"ax",%progbits
Default_Handler:
Infinite_Loop:
  b  Infinite_Loop
 8000b18:	e7fe      	b.n	8000b18 <ADC_IRQHandler>
	...

08000b1c <HAL_InitTick>:
  *       implementation  in user file.
  * @param TickPriority Tick interrupt priority.
  * @retval HAL status
  */
__weak HAL_StatusTypeDef HAL_InitTick(uint32_t TickPriority)
{
 8000b1c:	b538      	push	{r3, r4, r5, lr}
  /* Configure the SysTick to have interrupt in 1ms time basis*/
  if (HAL_SYSTICK_Config(SystemCoreClock / (1000U / uwTickFreq)) > 0U)
 8000b1e:	4a0e      	ldr	r2, [pc, #56]	@ (8000b58 <HAL_InitTick+0x3c>)
 8000b20:	4b0e      	ldr	r3, [pc, #56]	@ (8000b5c <HAL_InitTick+0x40>)
 8000b22:	7812      	ldrb	r2, [r2, #0]
{
 8000b24:	4605      	mov	r5, r0
  if (HAL_SYSTICK_Config(SystemCoreClock / (1000U / uwTickFreq)) > 0U)
 8000b26:	6818      	ldr	r0, [r3, #0]
 8000b28:	f44f 737a 	mov.w	r3, #1000	@ 0x3e8
 8000b2c:	fbb3 f3f2 	udiv	r3, r3, r2
 8000b30:	fbb0 f0f3 	udiv	r0, r0, r3
 8000b34:	f000 f9f2 	bl	8000f1c <HAL_SYSTICK_Config>
 8000b38:	b908      	cbnz	r0, 8000b3e <HAL_InitTick+0x22>
  {
    return HAL_ERROR;
  }

  /* Configure the SysTick IRQ priority */
  if (TickPriority < (1UL << __NVIC_PRIO_BITS))
 8000b3a:	2d0f      	cmp	r5, #15
 8000b3c:	d901      	bls.n	8000b42 <HAL_InitTick+0x26>
    return HAL_ERROR;
 8000b3e:	2001      	movs	r0, #1
    return HAL_ERROR;
  }

  /* Return function status */
  return HAL_OK;
}
 8000b40:	bd38      	pop	{r3, r4, r5, pc}
 8000b42:	4604      	mov	r4, r0
    HAL_NVIC_SetPriority(SysTick_IRQn, TickPriority, 0U);
 8000b44:	4602      	mov	r2, r0
 8000b46:	4629      	mov	r1, r5
 8000b48:	f04f 30ff 	mov.w	r0, #**********
 8000b4c:	f000 f99c 	bl	8000e88 <HAL_NVIC_SetPriority>
    uwTickPrio = TickPriority;
 8000b50:	4b03      	ldr	r3, [pc, #12]	@ (8000b60 <HAL_InitTick+0x44>)
 8000b52:	4620      	mov	r0, r4
 8000b54:	601d      	str	r5, [r3, #0]
}
 8000b56:	bd38      	pop	{r3, r4, r5, pc}
 8000b58:	20000004 	.word	0x20000004
 8000b5c:	20000000 	.word	0x20000000
 8000b60:	20000008 	.word	0x20000008

08000b64 <HAL_Init>:
{
 8000b64:	b508      	push	{r3, lr}
  __HAL_FLASH_INSTRUCTION_CACHE_ENABLE();
 8000b66:	4b0b      	ldr	r3, [pc, #44]	@ (8000b94 <HAL_Init+0x30>)
 8000b68:	681a      	ldr	r2, [r3, #0]
 8000b6a:	f442 7200 	orr.w	r2, r2, #512	@ 0x200
 8000b6e:	601a      	str	r2, [r3, #0]
  __HAL_FLASH_DATA_CACHE_ENABLE();
 8000b70:	681a      	ldr	r2, [r3, #0]
 8000b72:	f442 6280 	orr.w	r2, r2, #1024	@ 0x400
 8000b76:	601a      	str	r2, [r3, #0]
  __HAL_FLASH_PREFETCH_BUFFER_ENABLE();
 8000b78:	681a      	ldr	r2, [r3, #0]
 8000b7a:	f442 7280 	orr.w	r2, r2, #256	@ 0x100
 8000b7e:	601a      	str	r2, [r3, #0]
  HAL_NVIC_SetPriorityGrouping(NVIC_PRIORITYGROUP_4);
 8000b80:	2003      	movs	r0, #3
 8000b82:	f000 f96f 	bl	8000e64 <HAL_NVIC_SetPriorityGrouping>
  HAL_InitTick(TICK_INT_PRIORITY);
 8000b86:	200f      	movs	r0, #15
 8000b88:	f7ff ffc8 	bl	8000b1c <HAL_InitTick>
  HAL_MspInit();
 8000b8c:	f7ff ff00 	bl	8000990 <HAL_MspInit>
}
 8000b90:	2000      	movs	r0, #0
 8000b92:	bd08      	pop	{r3, pc}
 8000b94:	40023c00 	.word	0x40023c00

08000b98 <HAL_IncTick>:
  *      implementations in user file.
  * @retval None
  */
__weak void HAL_IncTick(void)
{
  uwTick += uwTickFreq;
 8000b98:	4a03      	ldr	r2, [pc, #12]	@ (8000ba8 <HAL_IncTick+0x10>)
 8000b9a:	4b04      	ldr	r3, [pc, #16]	@ (8000bac <HAL_IncTick+0x14>)
 8000b9c:	6811      	ldr	r1, [r2, #0]
 8000b9e:	781b      	ldrb	r3, [r3, #0]
 8000ba0:	440b      	add	r3, r1
 8000ba2:	6013      	str	r3, [r2, #0]
}
 8000ba4:	4770      	bx	lr
 8000ba6:	bf00      	nop
 8000ba8:	20000124 	.word	0x20000124
 8000bac:	20000004 	.word	0x20000004

08000bb0 <HAL_GetTick>:
  *       implementations in user file.
  * @retval tick value
  */
__weak uint32_t HAL_GetTick(void)
{
  return uwTick;
 8000bb0:	4b01      	ldr	r3, [pc, #4]	@ (8000bb8 <HAL_GetTick+0x8>)
 8000bb2:	6818      	ldr	r0, [r3, #0]
}
 8000bb4:	4770      	bx	lr
 8000bb6:	bf00      	nop
 8000bb8:	20000124 	.word	0x20000124

08000bbc <HAL_ADC_Init>:
HAL_StatusTypeDef HAL_ADC_Init(ADC_HandleTypeDef *hadc)
{
  HAL_StatusTypeDef tmp_hal_status = HAL_OK;

  /* Check ADC handle */
  if (hadc == NULL)
 8000bbc:	2800      	cmp	r0, #0
 8000bbe:	f000 809e 	beq.w	8000cfe <HAL_ADC_Init+0x142>
{
 8000bc2:	b538      	push	{r3, r4, r5, lr}
  if (hadc->Init.ExternalTrigConv != ADC_SOFTWARE_START)
  {
    assert_param(IS_ADC_EXT_TRIG_EDGE(hadc->Init.ExternalTrigConvEdge));
  }

  if (hadc->State == HAL_ADC_STATE_RESET)
 8000bc4:	6c05      	ldr	r5, [r0, #64]	@ 0x40
 8000bc6:	4604      	mov	r4, r0
 8000bc8:	b13d      	cbz	r5, 8000bda <HAL_ADC_Init+0x1e>
    hadc->Lock = HAL_UNLOCKED;
  }

  /* Configuration of ADC parameters if previous preliminary actions are      */
  /* correctly completed.                                                     */
  if (HAL_IS_BIT_CLR(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL))
 8000bca:	6c23      	ldr	r3, [r4, #64]	@ 0x40
 8000bcc:	06db      	lsls	r3, r3, #27
 8000bce:	d50c      	bpl.n	8000bea <HAL_ADC_Init+0x2e>
  {
    tmp_hal_status = HAL_ERROR;
  }

  /* Release Lock */
  __HAL_UNLOCK(hadc);
 8000bd0:	2300      	movs	r3, #0
 8000bd2:	f884 303c 	strb.w	r3, [r4, #60]	@ 0x3c
    tmp_hal_status = HAL_ERROR;
 8000bd6:	2001      	movs	r0, #1

  /* Return function status */
  return tmp_hal_status;
}
 8000bd8:	bd38      	pop	{r3, r4, r5, pc}
    HAL_ADC_MspInit(hadc);
 8000bda:	f7ff fd29 	bl	8000630 <HAL_ADC_MspInit>
    ADC_CLEAR_ERRORCODE(hadc);
 8000bde:	6465      	str	r5, [r4, #68]	@ 0x44
  if (HAL_IS_BIT_CLR(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL))
 8000be0:	6c23      	ldr	r3, [r4, #64]	@ 0x40
    hadc->Lock = HAL_UNLOCKED;
 8000be2:	f884 503c 	strb.w	r5, [r4, #60]	@ 0x3c
  if (HAL_IS_BIT_CLR(hadc->State, HAL_ADC_STATE_ERROR_INTERNAL))
 8000be6:	06db      	lsls	r3, r3, #27
 8000be8:	d4f2      	bmi.n	8000bd0 <HAL_ADC_Init+0x14>
    ADC_STATE_CLR_SET(hadc->State,
 8000bea:	6c23      	ldr	r3, [r4, #64]	@ 0x40
  /* (Depending on STM32F4 product, there may be up to 3 ADCs and 1 common */
  /* control register)                                                    */
  tmpADC_Common = ADC_COMMON_REGISTER(hadc);

  /* Set the ADC clock prescaler */
  tmpADC_Common->CCR &= ~(ADC_CCR_ADCPRE);
 8000bec:	4a47      	ldr	r2, [pc, #284]	@ (8000d0c <HAL_ADC_Init+0x150>)
    ADC_STATE_CLR_SET(hadc->State,
 8000bee:	f423 5388 	bic.w	r3, r3, #4352	@ 0x1100
 8000bf2:	f023 0302 	bic.w	r3, r3, #2
 8000bf6:	f043 0302 	orr.w	r3, r3, #2
 8000bfa:	6423      	str	r3, [r4, #64]	@ 0x40
  tmpADC_Common->CCR &= ~(ADC_CCR_ADCPRE);
 8000bfc:	6851      	ldr	r1, [r2, #4]
  tmpADC_Common->CCR |=  hadc->Init.ClockPrescaler;

  /* Set ADC scan mode */
  hadc->Instance->CR1 &= ~(ADC_CR1_SCAN);
 8000bfe:	6823      	ldr	r3, [r4, #0]
  tmpADC_Common->CCR &= ~(ADC_CCR_ADCPRE);
 8000c00:	f421 3140 	bic.w	r1, r1, #196608	@ 0x30000
 8000c04:	6051      	str	r1, [r2, #4]
  tmpADC_Common->CCR |=  hadc->Init.ClockPrescaler;
 8000c06:	6851      	ldr	r1, [r2, #4]
 8000c08:	6860      	ldr	r0, [r4, #4]
 8000c0a:	4301      	orrs	r1, r0
 8000c0c:	6051      	str	r1, [r2, #4]
  hadc->Instance->CR1 &= ~(ADC_CR1_SCAN);
 8000c0e:	685a      	ldr	r2, [r3, #4]
  hadc->Instance->CR1 |=  ADC_CR1_SCANCONV(hadc->Init.ScanConvMode);
 8000c10:	6920      	ldr	r0, [r4, #16]

  /* Set ADC resolution */
  hadc->Instance->CR1 &= ~(ADC_CR1_RES);
  hadc->Instance->CR1 |=  hadc->Init.Resolution;
 8000c12:	68a1      	ldr	r1, [r4, #8]
  hadc->Instance->CR1 &= ~(ADC_CR1_SCAN);
 8000c14:	f422 7280 	bic.w	r2, r2, #256	@ 0x100
 8000c18:	605a      	str	r2, [r3, #4]
  hadc->Instance->CR1 |=  ADC_CR1_SCANCONV(hadc->Init.ScanConvMode);
 8000c1a:	685a      	ldr	r2, [r3, #4]
 8000c1c:	ea42 2200 	orr.w	r2, r2, r0, lsl #8
 8000c20:	605a      	str	r2, [r3, #4]
  hadc->Instance->CR1 &= ~(ADC_CR1_RES);
 8000c22:	685a      	ldr	r2, [r3, #4]

  /* Set ADC data alignment */
  hadc->Instance->CR2 &= ~(ADC_CR2_ALIGN);
  hadc->Instance->CR2 |= hadc->Init.DataAlign;
 8000c24:	68e0      	ldr	r0, [r4, #12]
  hadc->Instance->CR1 &= ~(ADC_CR1_RES);
 8000c26:	f022 7240 	bic.w	r2, r2, #50331648	@ 0x3000000
 8000c2a:	605a      	str	r2, [r3, #4]
  hadc->Instance->CR1 |=  hadc->Init.Resolution;
 8000c2c:	685a      	ldr	r2, [r3, #4]
 8000c2e:	430a      	orrs	r2, r1
 8000c30:	605a      	str	r2, [r3, #4]
  hadc->Instance->CR2 &= ~(ADC_CR2_ALIGN);
 8000c32:	689a      	ldr	r2, [r3, #8]
  /* Enable external trigger if trigger selection is different of software  */
  /* start.                                                                 */
  /* Note: This configuration keeps the hardware feature of parameter       */
  /*       ExternalTrigConvEdge "trigger edge none" equivalent to           */
  /*       software start.                                                  */
  if (hadc->Init.ExternalTrigConv != ADC_SOFTWARE_START)
 8000c34:	6aa1      	ldr	r1, [r4, #40]	@ 0x28
  hadc->Instance->CR2 &= ~(ADC_CR2_ALIGN);
 8000c36:	f422 6200 	bic.w	r2, r2, #2048	@ 0x800
 8000c3a:	609a      	str	r2, [r3, #8]
  hadc->Instance->CR2 |= hadc->Init.DataAlign;
 8000c3c:	689a      	ldr	r2, [r3, #8]
 8000c3e:	4302      	orrs	r2, r0
 8000c40:	609a      	str	r2, [r3, #8]
  if (hadc->Init.ExternalTrigConv != ADC_SOFTWARE_START)
 8000c42:	4a33      	ldr	r2, [pc, #204]	@ (8000d10 <HAL_ADC_Init+0x154>)
 8000c44:	4291      	cmp	r1, r2
  {
    /* Select external trigger to start conversion */
    hadc->Instance->CR2 &= ~(ADC_CR2_EXTSEL);
 8000c46:	689a      	ldr	r2, [r3, #8]
  if (hadc->Init.ExternalTrigConv != ADC_SOFTWARE_START)
 8000c48:	d051      	beq.n	8000cee <HAL_ADC_Init+0x132>
    hadc->Instance->CR2 &= ~(ADC_CR2_EXTSEL);
 8000c4a:	f022 6270 	bic.w	r2, r2, #251658240	@ 0xf000000
    hadc->Instance->CR2 |= hadc->Init.ExternalTrigConv;

    /* Select external trigger polarity */
    hadc->Instance->CR2 &= ~(ADC_CR2_EXTEN);
    hadc->Instance->CR2 |= hadc->Init.ExternalTrigConvEdge;
 8000c4e:	6ae0      	ldr	r0, [r4, #44]	@ 0x2c
    hadc->Instance->CR2 &= ~(ADC_CR2_EXTSEL);
 8000c50:	609a      	str	r2, [r3, #8]
    hadc->Instance->CR2 |= hadc->Init.ExternalTrigConv;
 8000c52:	689a      	ldr	r2, [r3, #8]
 8000c54:	4311      	orrs	r1, r2
 8000c56:	6099      	str	r1, [r3, #8]
    hadc->Instance->CR2 &= ~(ADC_CR2_EXTEN);
 8000c58:	689a      	ldr	r2, [r3, #8]
 8000c5a:	f022 5240 	bic.w	r2, r2, #805306368	@ 0x30000000
 8000c5e:	609a      	str	r2, [r3, #8]
    hadc->Instance->CR2 |= hadc->Init.ExternalTrigConvEdge;
 8000c60:	689a      	ldr	r2, [r3, #8]
 8000c62:	4302      	orrs	r2, r0
 8000c64:	609a      	str	r2, [r3, #8]
    hadc->Instance->CR2 &= ~(ADC_CR2_EXTSEL);
    hadc->Instance->CR2 &= ~(ADC_CR2_EXTEN);
  }

  /* Enable or disable ADC continuous conversion mode */
  hadc->Instance->CR2 &= ~(ADC_CR2_CONT);
 8000c66:	689a      	ldr	r2, [r3, #8]
 8000c68:	f022 0202 	bic.w	r2, r2, #2
 8000c6c:	609a      	str	r2, [r3, #8]
  hadc->Instance->CR2 |= ADC_CR2_CONTINUOUS((uint32_t)hadc->Init.ContinuousConvMode);
 8000c6e:	689a      	ldr	r2, [r3, #8]
 8000c70:	7e21      	ldrb	r1, [r4, #24]
 8000c72:	ea42 0241 	orr.w	r2, r2, r1, lsl #1
 8000c76:	609a      	str	r2, [r3, #8]

  if (hadc->Init.DiscontinuousConvMode != DISABLE)
 8000c78:	f894 2020 	ldrb.w	r2, [r4, #32]
 8000c7c:	2a00      	cmp	r2, #0
 8000c7e:	d040      	beq.n	8000d02 <HAL_ADC_Init+0x146>
  {
    assert_param(IS_ADC_REGULAR_DISC_NUMBER(hadc->Init.NbrOfDiscConversion));

    /* Enable the selected ADC regular discontinuous mode */
    hadc->Instance->CR1 |= (uint32_t)ADC_CR1_DISCEN;
 8000c80:	685a      	ldr	r2, [r3, #4]

    /* Set the number of channels to be converted in discontinuous mode */
    hadc->Instance->CR1 &= ~(ADC_CR1_DISCNUM);
    hadc->Instance->CR1 |=  ADC_CR1_DISCONTINUOUS(hadc->Init.NbrOfDiscConversion);
 8000c82:	6a61      	ldr	r1, [r4, #36]	@ 0x24
    hadc->Instance->CR1 |= (uint32_t)ADC_CR1_DISCEN;
 8000c84:	f442 6200 	orr.w	r2, r2, #2048	@ 0x800
 8000c88:	605a      	str	r2, [r3, #4]
    hadc->Instance->CR1 &= ~(ADC_CR1_DISCNUM);
 8000c8a:	685a      	ldr	r2, [r3, #4]
 8000c8c:	f422 4260 	bic.w	r2, r2, #57344	@ 0xe000
 8000c90:	605a      	str	r2, [r3, #4]
    hadc->Instance->CR1 |=  ADC_CR1_DISCONTINUOUS(hadc->Init.NbrOfDiscConversion);
 8000c92:	685a      	ldr	r2, [r3, #4]
 8000c94:	3901      	subs	r1, #1
 8000c96:	ea42 3241 	orr.w	r2, r2, r1, lsl #13
 8000c9a:	605a      	str	r2, [r3, #4]
    /* Disable the selected ADC regular discontinuous mode */
    hadc->Instance->CR1 &= ~(ADC_CR1_DISCEN);
  }

  /* Set ADC number of conversion */
  hadc->Instance->SQR1 &= ~(ADC_SQR1_L);
 8000c9c:	6ada      	ldr	r2, [r3, #44]	@ 0x2c
  hadc->Instance->SQR1 |=  ADC_SQR1(hadc->Init.NbrOfConversion);
 8000c9e:	69e1      	ldr	r1, [r4, #28]
  hadc->Instance->SQR1 &= ~(ADC_SQR1_L);
 8000ca0:	f422 0270 	bic.w	r2, r2, #15728640	@ 0xf00000
 8000ca4:	62da      	str	r2, [r3, #44]	@ 0x2c
  hadc->Instance->SQR1 |=  ADC_SQR1(hadc->Init.NbrOfConversion);
 8000ca6:	6ada      	ldr	r2, [r3, #44]	@ 0x2c
 8000ca8:	3901      	subs	r1, #1
 8000caa:	ea42 5201 	orr.w	r2, r2, r1, lsl #20
 8000cae:	62da      	str	r2, [r3, #44]	@ 0x2c

  /* Enable or disable ADC DMA continuous request */
  hadc->Instance->CR2 &= ~(ADC_CR2_DDS);
 8000cb0:	689a      	ldr	r2, [r3, #8]
  hadc->Instance->CR2 |= ADC_CR2_DMAContReq((uint32_t)hadc->Init.DMAContinuousRequests);

  /* Enable or disable ADC end of conversion selection */
  hadc->Instance->CR2 &= ~(ADC_CR2_EOCS);
  hadc->Instance->CR2 |= ADC_CR2_EOCSelection(hadc->Init.EOCSelection);
 8000cb2:	6961      	ldr	r1, [r4, #20]
  hadc->Instance->CR2 &= ~(ADC_CR2_DDS);
 8000cb4:	f422 7200 	bic.w	r2, r2, #512	@ 0x200
 8000cb8:	609a      	str	r2, [r3, #8]
  hadc->Instance->CR2 |= ADC_CR2_DMAContReq((uint32_t)hadc->Init.DMAContinuousRequests);
 8000cba:	689a      	ldr	r2, [r3, #8]
 8000cbc:	f894 0030 	ldrb.w	r0, [r4, #48]	@ 0x30
 8000cc0:	ea42 2240 	orr.w	r2, r2, r0, lsl #9
 8000cc4:	609a      	str	r2, [r3, #8]
  hadc->Instance->CR2 &= ~(ADC_CR2_EOCS);
 8000cc6:	689a      	ldr	r2, [r3, #8]
 8000cc8:	f422 6280 	bic.w	r2, r2, #1024	@ 0x400
 8000ccc:	609a      	str	r2, [r3, #8]
  hadc->Instance->CR2 |= ADC_CR2_EOCSelection(hadc->Init.EOCSelection);
 8000cce:	689a      	ldr	r2, [r3, #8]
    ADC_CLEAR_ERRORCODE(hadc);
 8000cd0:	2000      	movs	r0, #0
  hadc->Instance->CR2 |= ADC_CR2_EOCSelection(hadc->Init.EOCSelection);
 8000cd2:	ea42 2281 	orr.w	r2, r2, r1, lsl #10
 8000cd6:	609a      	str	r2, [r3, #8]
    ADC_CLEAR_ERRORCODE(hadc);
 8000cd8:	6460      	str	r0, [r4, #68]	@ 0x44
    ADC_STATE_CLR_SET(hadc->State,
 8000cda:	6c23      	ldr	r3, [r4, #64]	@ 0x40
 8000cdc:	f023 0303 	bic.w	r3, r3, #3
 8000ce0:	f043 0301 	orr.w	r3, r3, #1
 8000ce4:	6423      	str	r3, [r4, #64]	@ 0x40
  __HAL_UNLOCK(hadc);
 8000ce6:	2300      	movs	r3, #0
 8000ce8:	f884 303c 	strb.w	r3, [r4, #60]	@ 0x3c
}
 8000cec:	bd38      	pop	{r3, r4, r5, pc}
    hadc->Instance->CR2 &= ~(ADC_CR2_EXTSEL);
 8000cee:	f022 6270 	bic.w	r2, r2, #251658240	@ 0xf000000
 8000cf2:	609a      	str	r2, [r3, #8]
    hadc->Instance->CR2 &= ~(ADC_CR2_EXTEN);
 8000cf4:	689a      	ldr	r2, [r3, #8]
 8000cf6:	f022 5240 	bic.w	r2, r2, #805306368	@ 0x30000000
 8000cfa:	609a      	str	r2, [r3, #8]
 8000cfc:	e7b3      	b.n	8000c66 <HAL_ADC_Init+0xaa>
    return HAL_ERROR;
 8000cfe:	2001      	movs	r0, #1
}
 8000d00:	4770      	bx	lr
    hadc->Instance->CR1 &= ~(ADC_CR1_DISCEN);
 8000d02:	685a      	ldr	r2, [r3, #4]
 8000d04:	f422 6200 	bic.w	r2, r2, #2048	@ 0x800
 8000d08:	605a      	str	r2, [r3, #4]
 8000d0a:	e7c7      	b.n	8000c9c <HAL_ADC_Init+0xe0>
 8000d0c:	40012300 	.word	0x40012300
 8000d10:	0f000001 	.word	0x0f000001

08000d14 <HAL_ADC_ConfigChannel>:
{
 8000d14:	b570      	push	{r4, r5, r6, lr}
  __HAL_LOCK(hadc);
 8000d16:	f890 303c 	ldrb.w	r3, [r0, #60]	@ 0x3c
{
 8000d1a:	b082      	sub	sp, #8
 8000d1c:	4602      	mov	r2, r0
  __HAL_LOCK(hadc);
 8000d1e:	2b01      	cmp	r3, #1
  __IO uint32_t counter = 0U;
 8000d20:	f04f 0000 	mov.w	r0, #0
 8000d24:	9001      	str	r0, [sp, #4]
  __HAL_LOCK(hadc);
 8000d26:	f000 8093 	beq.w	8000e50 <HAL_ADC_ConfigChannel+0x13c>
 8000d2a:	2301      	movs	r3, #1
 8000d2c:	f882 303c 	strb.w	r3, [r2, #60]	@ 0x3c
  if (sConfig->Channel > ADC_CHANNEL_9)
 8000d30:	680d      	ldr	r5, [r1, #0]
    hadc->Instance->SMPR1 &= ~ADC_SMPR1(ADC_SMPR1_SMP10, sConfig->Channel);
 8000d32:	6813      	ldr	r3, [r2, #0]
    hadc->Instance->SMPR1 |= ADC_SMPR1(sConfig->SamplingTime, sConfig->Channel);
 8000d34:	688c      	ldr	r4, [r1, #8]
  if (sConfig->Channel > ADC_CHANNEL_9)
 8000d36:	2d09      	cmp	r5, #9
 8000d38:	b2a8      	uxth	r0, r5
 8000d3a:	d828      	bhi.n	8000d8e <HAL_ADC_ConfigChannel+0x7a>
    hadc->Instance->SMPR2 &= ~ADC_SMPR2(ADC_SMPR2_SMP0, sConfig->Channel);
 8000d3c:	691e      	ldr	r6, [r3, #16]
 8000d3e:	eb05 0e45 	add.w	lr, r5, r5, lsl #1
 8000d42:	f04f 0c07 	mov.w	ip, #7
 8000d46:	fa0c fc0e 	lsl.w	ip, ip, lr
 8000d4a:	ea26 060c 	bic.w	r6, r6, ip
 8000d4e:	611e      	str	r6, [r3, #16]
    hadc->Instance->SMPR2 |= ADC_SMPR2(sConfig->SamplingTime, sConfig->Channel);
 8000d50:	691e      	ldr	r6, [r3, #16]
 8000d52:	fa04 f40e 	lsl.w	r4, r4, lr
 8000d56:	4334      	orrs	r4, r6
 8000d58:	611c      	str	r4, [r3, #16]
  if (sConfig->Rank < 7U)
 8000d5a:	684c      	ldr	r4, [r1, #4]
 8000d5c:	2c06      	cmp	r4, #6
    hadc->Instance->SQR3 &= ~ADC_SQR3_RK(ADC_SQR3_SQ1, sConfig->Rank);
 8000d5e:	eb04 0184 	add.w	r1, r4, r4, lsl #2
  if (sConfig->Rank < 7U)
 8000d62:	d82a      	bhi.n	8000dba <HAL_ADC_ConfigChannel+0xa6>
    hadc->Instance->SQR3 &= ~ADC_SQR3_RK(ADC_SQR3_SQ1, sConfig->Rank);
 8000d64:	6b5c      	ldr	r4, [r3, #52]	@ 0x34
 8000d66:	3905      	subs	r1, #5
 8000d68:	f04f 0c1f 	mov.w	ip, #31
    hadc->Instance->SQR3 |= ADC_SQR3_RK(sConfig->Channel, sConfig->Rank);
 8000d6c:	4088      	lsls	r0, r1
    hadc->Instance->SQR3 &= ~ADC_SQR3_RK(ADC_SQR3_SQ1, sConfig->Rank);
 8000d6e:	fa0c f101 	lsl.w	r1, ip, r1
 8000d72:	ea24 0101 	bic.w	r1, r4, r1
 8000d76:	6359      	str	r1, [r3, #52]	@ 0x34
    hadc->Instance->SQR3 |= ADC_SQR3_RK(sConfig->Channel, sConfig->Rank);
 8000d78:	6b59      	ldr	r1, [r3, #52]	@ 0x34
 8000d7a:	4308      	orrs	r0, r1
 8000d7c:	6358      	str	r0, [r3, #52]	@ 0x34
  if ((hadc->Instance == ADC1) && (sConfig->Channel == ADC_CHANNEL_VBAT))
 8000d7e:	4936      	ldr	r1, [pc, #216]	@ (8000e58 <HAL_ADC_ConfigChannel+0x144>)
 8000d80:	428b      	cmp	r3, r1
 8000d82:	d02b      	beq.n	8000ddc <HAL_ADC_ConfigChannel+0xc8>
  __HAL_UNLOCK(hadc);
 8000d84:	2000      	movs	r0, #0
 8000d86:	f882 003c 	strb.w	r0, [r2, #60]	@ 0x3c
}
 8000d8a:	b002      	add	sp, #8
 8000d8c:	bd70      	pop	{r4, r5, r6, pc}
    hadc->Instance->SMPR1 &= ~ADC_SMPR1(ADC_SMPR1_SMP10, sConfig->Channel);
 8000d8e:	eb00 0c40 	add.w	ip, r0, r0, lsl #1
 8000d92:	68de      	ldr	r6, [r3, #12]
 8000d94:	f1ac 0c1e 	sub.w	ip, ip, #30
 8000d98:	f04f 0e07 	mov.w	lr, #7
    hadc->Instance->SMPR1 |= ADC_SMPR1(sConfig->SamplingTime, sConfig->Channel);
 8000d9c:	fa04 f40c 	lsl.w	r4, r4, ip
    hadc->Instance->SMPR1 &= ~ADC_SMPR1(ADC_SMPR1_SMP10, sConfig->Channel);
 8000da0:	fa0e fc0c 	lsl.w	ip, lr, ip
 8000da4:	ea26 060c 	bic.w	r6, r6, ip
 8000da8:	60de      	str	r6, [r3, #12]
    hadc->Instance->SMPR1 |= ADC_SMPR1(sConfig->SamplingTime, sConfig->Channel);
 8000daa:	68de      	ldr	r6, [r3, #12]
 8000dac:	4334      	orrs	r4, r6
 8000dae:	60dc      	str	r4, [r3, #12]
  if (sConfig->Rank < 7U)
 8000db0:	684c      	ldr	r4, [r1, #4]
 8000db2:	2c06      	cmp	r4, #6
    hadc->Instance->SQR3 &= ~ADC_SQR3_RK(ADC_SQR3_SQ1, sConfig->Rank);
 8000db4:	eb04 0184 	add.w	r1, r4, r4, lsl #2
  if (sConfig->Rank < 7U)
 8000db8:	d9d4      	bls.n	8000d64 <HAL_ADC_ConfigChannel+0x50>
  else if (sConfig->Rank < 13U)
 8000dba:	2c0c      	cmp	r4, #12
 8000dbc:	d834      	bhi.n	8000e28 <HAL_ADC_ConfigChannel+0x114>
    hadc->Instance->SQR2 &= ~ADC_SQR2_RK(ADC_SQR2_SQ7, sConfig->Rank);
 8000dbe:	6b1c      	ldr	r4, [r3, #48]	@ 0x30
 8000dc0:	3923      	subs	r1, #35	@ 0x23
 8000dc2:	261f      	movs	r6, #31
    hadc->Instance->SQR2 |= ADC_SQR2_RK(sConfig->Channel, sConfig->Rank);
 8000dc4:	4088      	lsls	r0, r1
    hadc->Instance->SQR2 &= ~ADC_SQR2_RK(ADC_SQR2_SQ7, sConfig->Rank);
 8000dc6:	fa06 f101 	lsl.w	r1, r6, r1
 8000dca:	ea24 0101 	bic.w	r1, r4, r1
 8000dce:	6319      	str	r1, [r3, #48]	@ 0x30
    hadc->Instance->SQR2 |= ADC_SQR2_RK(sConfig->Channel, sConfig->Rank);
 8000dd0:	6b19      	ldr	r1, [r3, #48]	@ 0x30
 8000dd2:	4308      	orrs	r0, r1
  if ((hadc->Instance == ADC1) && (sConfig->Channel == ADC_CHANNEL_VBAT))
 8000dd4:	4920      	ldr	r1, [pc, #128]	@ (8000e58 <HAL_ADC_ConfigChannel+0x144>)
    hadc->Instance->SQR2 |= ADC_SQR2_RK(sConfig->Channel, sConfig->Rank);
 8000dd6:	6318      	str	r0, [r3, #48]	@ 0x30
  if ((hadc->Instance == ADC1) && (sConfig->Channel == ADC_CHANNEL_VBAT))
 8000dd8:	428b      	cmp	r3, r1
 8000dda:	d1d3      	bne.n	8000d84 <HAL_ADC_ConfigChannel+0x70>
 8000ddc:	2d12      	cmp	r5, #18
 8000dde:	d030      	beq.n	8000e42 <HAL_ADC_ConfigChannel+0x12e>
  if ((hadc->Instance == ADC1) && ((sConfig->Channel == ADC_CHANNEL_TEMPSENSOR) || (sConfig->Channel == ADC_CHANNEL_VREFINT)))
 8000de0:	f1a5 0310 	sub.w	r3, r5, #16
 8000de4:	2b01      	cmp	r3, #1
 8000de6:	d8cd      	bhi.n	8000d84 <HAL_ADC_ConfigChannel+0x70>
    tmpADC_Common->CCR |= ADC_CCR_TSVREFE;
 8000de8:	491c      	ldr	r1, [pc, #112]	@ (8000e5c <HAL_ADC_ConfigChannel+0x148>)
 8000dea:	684b      	ldr	r3, [r1, #4]
    if (sConfig->Channel == ADC_CHANNEL_TEMPSENSOR)
 8000dec:	2d10      	cmp	r5, #16
    tmpADC_Common->CCR |= ADC_CCR_TSVREFE;
 8000dee:	f443 0300 	orr.w	r3, r3, #8388608	@ 0x800000
 8000df2:	604b      	str	r3, [r1, #4]
    if (sConfig->Channel == ADC_CHANNEL_TEMPSENSOR)
 8000df4:	d1c6      	bne.n	8000d84 <HAL_ADC_ConfigChannel+0x70>
      counter = (ADC_TEMPSENSOR_DELAY_US * (SystemCoreClock / 1000000U));
 8000df6:	4b1a      	ldr	r3, [pc, #104]	@ (8000e60 <HAL_ADC_ConfigChannel+0x14c>)
 8000df8:	f101 7146 	add.w	r1, r1, #51904512	@ 0x3180000
 8000dfc:	681b      	ldr	r3, [r3, #0]
 8000dfe:	f501 312e 	add.w	r1, r1, #178176	@ 0x2b800
 8000e02:	f201 3183 	addw	r1, r1, #899	@ 0x383
 8000e06:	fba1 1303 	umull	r1, r3, r1, r3
 8000e0a:	0c9b      	lsrs	r3, r3, #18
 8000e0c:	eb03 0383 	add.w	r3, r3, r3, lsl #2
 8000e10:	005b      	lsls	r3, r3, #1
 8000e12:	9301      	str	r3, [sp, #4]
      while (counter != 0U)
 8000e14:	9b01      	ldr	r3, [sp, #4]
 8000e16:	2b00      	cmp	r3, #0
 8000e18:	d0b4      	beq.n	8000d84 <HAL_ADC_ConfigChannel+0x70>
        counter--;
 8000e1a:	9b01      	ldr	r3, [sp, #4]
 8000e1c:	3b01      	subs	r3, #1
 8000e1e:	9301      	str	r3, [sp, #4]
      while (counter != 0U)
 8000e20:	9b01      	ldr	r3, [sp, #4]
 8000e22:	2b00      	cmp	r3, #0
 8000e24:	d1f9      	bne.n	8000e1a <HAL_ADC_ConfigChannel+0x106>
 8000e26:	e7ad      	b.n	8000d84 <HAL_ADC_ConfigChannel+0x70>
    hadc->Instance->SQR1 &= ~ADC_SQR1_RK(ADC_SQR1_SQ13, sConfig->Rank);
 8000e28:	6adc      	ldr	r4, [r3, #44]	@ 0x2c
 8000e2a:	3941      	subs	r1, #65	@ 0x41
 8000e2c:	261f      	movs	r6, #31
    hadc->Instance->SQR1 |= ADC_SQR1_RK(sConfig->Channel, sConfig->Rank);
 8000e2e:	4088      	lsls	r0, r1
    hadc->Instance->SQR1 &= ~ADC_SQR1_RK(ADC_SQR1_SQ13, sConfig->Rank);
 8000e30:	fa06 f101 	lsl.w	r1, r6, r1
 8000e34:	ea24 0101 	bic.w	r1, r4, r1
 8000e38:	62d9      	str	r1, [r3, #44]	@ 0x2c
    hadc->Instance->SQR1 |= ADC_SQR1_RK(sConfig->Channel, sConfig->Rank);
 8000e3a:	6ad9      	ldr	r1, [r3, #44]	@ 0x2c
 8000e3c:	4308      	orrs	r0, r1
 8000e3e:	62d8      	str	r0, [r3, #44]	@ 0x2c
 8000e40:	e79d      	b.n	8000d7e <HAL_ADC_ConfigChannel+0x6a>
    tmpADC_Common->CCR |= ADC_CCR_VBATE;
 8000e42:	f8d1 3304 	ldr.w	r3, [r1, #772]	@ 0x304
 8000e46:	f443 0380 	orr.w	r3, r3, #4194304	@ 0x400000
 8000e4a:	f8c1 3304 	str.w	r3, [r1, #772]	@ 0x304
  if ((hadc->Instance == ADC1) && ((sConfig->Channel == ADC_CHANNEL_TEMPSENSOR) || (sConfig->Channel == ADC_CHANNEL_VREFINT)))
 8000e4e:	e799      	b.n	8000d84 <HAL_ADC_ConfigChannel+0x70>
  __HAL_LOCK(hadc);
 8000e50:	2002      	movs	r0, #2
}
 8000e52:	b002      	add	sp, #8
 8000e54:	bd70      	pop	{r4, r5, r6, pc}
 8000e56:	bf00      	nop
 8000e58:	40012000 	.word	0x40012000
 8000e5c:	40012300 	.word	0x40012300
 8000e60:	20000000 	.word	0x20000000

08000e64 <HAL_NVIC_SetPriorityGrouping>:
__STATIC_INLINE void __NVIC_SetPriorityGrouping(uint32_t PriorityGroup)
{
  uint32_t reg_value;
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);             /* only values 0..7 are used          */

  reg_value  =  SCB->AIRCR;                                                   /* read old register configuration    */
 8000e64:	4907      	ldr	r1, [pc, #28]	@ (8000e84 <HAL_NVIC_SetPriorityGrouping+0x20>)
 8000e66:	68ca      	ldr	r2, [r1, #12]
  reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to change               */
  reg_value  =  (reg_value                                   |
                ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
                (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos)  );              /* Insert write key and priority group */
 8000e68:	0203      	lsls	r3, r0, #8
  reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to change               */
 8000e6a:	f64f 00ff 	movw	r0, #63743	@ 0xf8ff
                (PriorityGroupTmp << SCB_AIRCR_PRIGROUP_Pos)  );              /* Insert write key and priority group */
 8000e6e:	f403 63e0 	and.w	r3, r3, #1792	@ 0x700
  reg_value &= ~((uint32_t)(SCB_AIRCR_VECTKEY_Msk | SCB_AIRCR_PRIGROUP_Msk)); /* clear bits to change               */
 8000e72:	4002      	ands	r2, r0
                ((uint32_t)0x5FAUL << SCB_AIRCR_VECTKEY_Pos) |
 8000e74:	4313      	orrs	r3, r2
  reg_value  =  (reg_value                                   |
 8000e76:	f043 63bf 	orr.w	r3, r3, #100139008	@ 0x5f80000
 8000e7a:	f443 3300 	orr.w	r3, r3, #131072	@ 0x20000
  SCB->AIRCR =  reg_value;
 8000e7e:	60cb      	str	r3, [r1, #12]
  /* Check the parameters */
  assert_param(IS_NVIC_PRIORITY_GROUP(PriorityGroup));
  
  /* Set the PRIGROUP[10:8] bits according to the PriorityGroup parameter value */
  NVIC_SetPriorityGrouping(PriorityGroup);
}
 8000e80:	4770      	bx	lr
 8000e82:	bf00      	nop
 8000e84:	e000ed00 	.word	0xe000ed00

08000e88 <HAL_NVIC_SetPriority>:
  \details Reads the priority grouping field from the NVIC Interrupt Controller.
  \return                Priority grouping field (SCB->AIRCR [10:8] PRIGROUP field).
 */
__STATIC_INLINE uint32_t __NVIC_GetPriorityGrouping(void)
{
  return ((uint32_t)((SCB->AIRCR & SCB_AIRCR_PRIGROUP_Msk) >> SCB_AIRCR_PRIGROUP_Pos));
 8000e88:	4b1b      	ldr	r3, [pc, #108]	@ (8000ef8 <HAL_NVIC_SetPriority+0x70>)
 8000e8a:	68db      	ldr	r3, [r3, #12]
 8000e8c:	f3c3 2302 	ubfx	r3, r3, #8, #3
  *         This parameter can be a value between 0 and 15
  *         A lower priority value indicates a higher priority.          
  * @retval None
  */
void HAL_NVIC_SetPriority(IRQn_Type IRQn, uint32_t PreemptPriority, uint32_t SubPriority)
{ 
 8000e90:	b500      	push	{lr}
{
  uint32_t PriorityGroupTmp = (PriorityGroup & (uint32_t)0x07UL);   /* only values 0..7 are used          */
  uint32_t PreemptPriorityBits;
  uint32_t SubPriorityBits;

  PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NVIC_PRIO_BITS) : (uint32_t)(7UL - PriorityGroupTmp);
 8000e92:	f1c3 0e07 	rsb	lr, r3, #7
 8000e96:	f1be 0f04 	cmp.w	lr, #4
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 8000e9a:	f103 0c04 	add.w	ip, r3, #4
  PreemptPriorityBits = ((7UL - PriorityGroupTmp) > (uint32_t)(__NVIC_PRIO_BITS)) ? (uint32_t)(__NVIC_PRIO_BITS) : (uint32_t)(7UL - PriorityGroupTmp);
 8000e9e:	bf28      	it	cs
 8000ea0:	f04f 0e04 	movcs.w	lr, #4
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 8000ea4:	f1bc 0f06 	cmp.w	ip, #6
 8000ea8:	d91c      	bls.n	8000ee4 <HAL_NVIC_SetPriority+0x5c>
 8000eaa:	f1a3 0c03 	sub.w	ip, r3, #3

  return (
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
           ((SubPriority     & (uint32_t)((1UL << (SubPriorityBits    )) - 1UL)))
 8000eae:	f04f 33ff 	mov.w	r3, #**********
 8000eb2:	fa03 f30c 	lsl.w	r3, r3, ip
 8000eb6:	ea22 0203 	bic.w	r2, r2, r3
           ((PreemptPriority & (uint32_t)((1UL << (PreemptPriorityBits)) - 1UL)) << SubPriorityBits) |
 8000eba:	f04f 33ff 	mov.w	r3, #**********
 8000ebe:	fa03 f30e 	lsl.w	r3, r3, lr
 8000ec2:	ea21 0303 	bic.w	r3, r1, r3
 8000ec6:	fa03 f30c 	lsl.w	r3, r3, ip
 8000eca:	4313      	orrs	r3, r2
    NVIC->IP[((uint32_t)IRQn)]               = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000ecc:	011b      	lsls	r3, r3, #4
  if ((int32_t)(IRQn) >= 0)
 8000ece:	2800      	cmp	r0, #0
    NVIC->IP[((uint32_t)IRQn)]               = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000ed0:	b2db      	uxtb	r3, r3
  if ((int32_t)(IRQn) >= 0)
 8000ed2:	db0a      	blt.n	8000eea <HAL_NVIC_SetPriority+0x62>
    NVIC->IP[((uint32_t)IRQn)]               = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000ed4:	f100 4060 	add.w	r0, r0, #3758096384	@ 0xe0000000
 8000ed8:	f500 4061 	add.w	r0, r0, #57600	@ 0xe100
 8000edc:	f880 3300 	strb.w	r3, [r0, #768]	@ 0x300
  assert_param(IS_NVIC_PREEMPTION_PRIORITY(PreemptPriority));
  
  prioritygroup = NVIC_GetPriorityGrouping();
  
  NVIC_SetPriority(IRQn, NVIC_EncodePriority(prioritygroup, PreemptPriority, SubPriority));
}
 8000ee0:	f85d fb04 	ldr.w	pc, [sp], #4
 8000ee4:	2200      	movs	r2, #0
  SubPriorityBits     = ((PriorityGroupTmp + (uint32_t)(__NVIC_PRIO_BITS)) < (uint32_t)7UL) ? (uint32_t)0UL : (uint32_t)((PriorityGroupTmp - 7UL) + (uint32_t)(__NVIC_PRIO_BITS));
 8000ee6:	4694      	mov	ip, r2
 8000ee8:	e7e7      	b.n	8000eba <HAL_NVIC_SetPriority+0x32>
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000eea:	4a04      	ldr	r2, [pc, #16]	@ (8000efc <HAL_NVIC_SetPriority+0x74>)
 8000eec:	f000 000f 	and.w	r0, r0, #15
 8000ef0:	4402      	add	r2, r0
 8000ef2:	7613      	strb	r3, [r2, #24]
 8000ef4:	f85d fb04 	ldr.w	pc, [sp], #4
 8000ef8:	e000ed00 	.word	0xe000ed00
 8000efc:	e000ecfc 	.word	0xe000ecfc

08000f00 <HAL_NVIC_EnableIRQ>:
  if ((int32_t)(IRQn) >= 0)
 8000f00:	2800      	cmp	r0, #0
 8000f02:	db07      	blt.n	8000f14 <HAL_NVIC_EnableIRQ+0x14>
    NVIC->ISER[(((uint32_t)IRQn) >> 5UL)] = (uint32_t)(1UL << (((uint32_t)IRQn) & 0x1FUL));
 8000f04:	4a04      	ldr	r2, [pc, #16]	@ (8000f18 <HAL_NVIC_EnableIRQ+0x18>)
 8000f06:	0941      	lsrs	r1, r0, #5
 8000f08:	2301      	movs	r3, #1
 8000f0a:	f000 001f 	and.w	r0, r0, #31
 8000f0e:	4083      	lsls	r3, r0
 8000f10:	f842 3021 	str.w	r3, [r2, r1, lsl #2]
  /* Check the parameters */
  assert_param(IS_NVIC_DEVICE_IRQ(IRQn));
  
  /* Enable interrupt */
  NVIC_EnableIRQ(IRQn);
}
 8000f14:	4770      	bx	lr
 8000f16:	bf00      	nop
 8000f18:	e000e100 	.word	0xe000e100

08000f1c <HAL_SYSTICK_Config>:
           function <b>SysTick_Config</b> is not included. In this case, the file <b><i>device</i>.h</b>
           must contain a vendor-specific implementation of this function.
 */
__STATIC_INLINE uint32_t SysTick_Config(uint32_t ticks)
{
  if ((ticks - 1UL) > SysTick_LOAD_RELOAD_Msk)
 8000f1c:	3801      	subs	r0, #1
 8000f1e:	f1b0 7f80 	cmp.w	r0, #16777216	@ 0x1000000
 8000f22:	d301      	bcc.n	8000f28 <HAL_SYSTICK_Config+0xc>
  {
    return (1UL);                                                   /* Reload value impossible */
 8000f24:	2001      	movs	r0, #1
  *                  - 1  Function failed.
  */
uint32_t HAL_SYSTICK_Config(uint32_t TicksNumb)
{
   return SysTick_Config(TicksNumb);
}
 8000f26:	4770      	bx	lr
{
 8000f28:	b410      	push	{r4}
  }

  SysTick->LOAD  = (uint32_t)(ticks - 1UL);                         /* set reload register */
 8000f2a:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000f2e:	4c07      	ldr	r4, [pc, #28]	@ (8000f4c <HAL_SYSTICK_Config+0x30>)
  SysTick->LOAD  = (uint32_t)(ticks - 1UL);                         /* set reload register */
 8000f30:	6158      	str	r0, [r3, #20]
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8000f32:	f04f 0cf0 	mov.w	ip, #240	@ 0xf0
 8000f36:	f884 c023 	strb.w	ip, [r4, #35]	@ 0x23
  NVIC_SetPriority (SysTick_IRQn, (1UL << __NVIC_PRIO_BITS) - 1UL); /* set Priority for Systick Interrupt */
  SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Value */
 8000f3a:	2200      	movs	r2, #0
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
 8000f3c:	2107      	movs	r1, #7
                   SysTick_CTRL_TICKINT_Msk   |
                   SysTick_CTRL_ENABLE_Msk;                         /* Enable SysTick IRQ and SysTick Timer */
  return (0UL);                                                     /* Function successful */
 8000f3e:	4610      	mov	r0, r2
  SysTick->VAL   = 0UL;                                             /* Load the SysTick Counter Value */
 8000f40:	619a      	str	r2, [r3, #24]
}
 8000f42:	f85d 4b04 	ldr.w	r4, [sp], #4
  SysTick->CTRL  = SysTick_CTRL_CLKSOURCE_Msk |
 8000f46:	6119      	str	r1, [r3, #16]
 8000f48:	4770      	bx	lr
 8000f4a:	bf00      	nop
 8000f4c:	e000ed00 	.word	0xe000ed00

08000f50 <HAL_DMA_Init>:
  * @param  hdma Pointer to a DMA_HandleTypeDef structure that contains
  *               the configuration information for the specified DMA Stream.  
  * @retval HAL status
  */
HAL_StatusTypeDef HAL_DMA_Init(DMA_HandleTypeDef *hdma)
{
 8000f50:	b538      	push	{r3, r4, r5, lr}
 8000f52:	4604      	mov	r4, r0
  uint32_t tmp = 0U;
  uint32_t tickstart = HAL_GetTick();
 8000f54:	f7ff fe2c 	bl	8000bb0 <HAL_GetTick>
  DMA_Base_Registers *regs;

  /* Check the DMA peripheral state */
  if(hdma == NULL)
 8000f58:	2c00      	cmp	r4, #0
 8000f5a:	d06b      	beq.n	8001034 <HAL_DMA_Init+0xe4>

  /* Allocate lock resource */
  __HAL_UNLOCK(hdma);
  
  /* Disable the peripheral */
  __HAL_DMA_DISABLE(hdma);
 8000f5c:	6823      	ldr	r3, [r4, #0]
  __HAL_UNLOCK(hdma);
 8000f5e:	2200      	movs	r2, #0
  hdma->State = HAL_DMA_STATE_BUSY;
 8000f60:	2102      	movs	r1, #2
 8000f62:	f884 1035 	strb.w	r1, [r4, #53]	@ 0x35
  __HAL_UNLOCK(hdma);
 8000f66:	f884 2034 	strb.w	r2, [r4, #52]	@ 0x34
  __HAL_DMA_DISABLE(hdma);
 8000f6a:	681a      	ldr	r2, [r3, #0]
 8000f6c:	f022 0201 	bic.w	r2, r2, #1
 8000f70:	4605      	mov	r5, r0
 8000f72:	601a      	str	r2, [r3, #0]
  
  /* Check if the DMA Stream is effectively disabled */
  while((hdma->Instance->CR & DMA_SxCR_EN) != RESET)
 8000f74:	e005      	b.n	8000f82 <HAL_DMA_Init+0x32>
  {
    /* Check for the Timeout */
    if((HAL_GetTick() - tickstart ) > HAL_TIMEOUT_DMA_ABORT)
 8000f76:	f7ff fe1b 	bl	8000bb0 <HAL_GetTick>
 8000f7a:	1b43      	subs	r3, r0, r5
 8000f7c:	2b05      	cmp	r3, #5
 8000f7e:	d837      	bhi.n	8000ff0 <HAL_DMA_Init+0xa0>
  while((hdma->Instance->CR & DMA_SxCR_EN) != RESET)
 8000f80:	6823      	ldr	r3, [r4, #0]
 8000f82:	681a      	ldr	r2, [r3, #0]
 8000f84:	07d1      	lsls	r1, r2, #31
 8000f86:	d4f6      	bmi.n	8000f76 <HAL_DMA_Init+0x26>
                      DMA_SxCR_PL    | DMA_SxCR_MSIZE  | DMA_SxCR_PSIZE  | \
                      DMA_SxCR_MINC  | DMA_SxCR_PINC   | DMA_SxCR_CIRC   | \
                      DMA_SxCR_DIR   | DMA_SxCR_CT     | DMA_SxCR_DBM));

  /* Prepare the DMA Stream configuration */
  tmp |=  hdma->Init.Channel             | hdma->Init.Direction        |
 8000f88:	e9d4 2001 	ldrd	r2, r0, [r4, #4]
 8000f8c:	68e1      	ldr	r1, [r4, #12]
 8000f8e:	4302      	orrs	r2, r0
 8000f90:	430a      	orrs	r2, r1
          hdma->Init.PeriphInc           | hdma->Init.MemInc           |
 8000f92:	e9d4 5104 	ldrd	r5, r1, [r4, #16]
          hdma->Init.PeriphDataAlignment | hdma->Init.MemDataAlignment |
 8000f96:	69a0      	ldr	r0, [r4, #24]
          hdma->Init.PeriphInc           | hdma->Init.MemInc           |
 8000f98:	432a      	orrs	r2, r5
 8000f9a:	430a      	orrs	r2, r1
          hdma->Init.PeriphDataAlignment | hdma->Init.MemDataAlignment |
 8000f9c:	69e1      	ldr	r1, [r4, #28]
  tmp = hdma->Instance->CR;
 8000f9e:	681d      	ldr	r5, [r3, #0]
          hdma->Init.PeriphDataAlignment | hdma->Init.MemDataAlignment |
 8000fa0:	4302      	orrs	r2, r0
 8000fa2:	430a      	orrs	r2, r1
  tmp &= ((uint32_t)~(DMA_SxCR_CHSEL | DMA_SxCR_MBURST | DMA_SxCR_PBURST | \
 8000fa4:	4931      	ldr	r1, [pc, #196]	@ (800106c <HAL_DMA_Init+0x11c>)
 8000fa6:	4029      	ands	r1, r5
          hdma->Init.Mode                | hdma->Init.Priority;
 8000fa8:	6a25      	ldr	r5, [r4, #32]
 8000faa:	432a      	orrs	r2, r5
  tmp |=  hdma->Init.Channel             | hdma->Init.Direction        |
 8000fac:	430a      	orrs	r2, r1

  /* the Memory burst and peripheral burst are not used when the FIFO is disabled */
  if(hdma->Init.FIFOMode == DMA_FIFOMODE_ENABLE)
 8000fae:	6a61      	ldr	r1, [r4, #36]	@ 0x24
 8000fb0:	2904      	cmp	r1, #4
 8000fb2:	d024      	beq.n	8000ffe <HAL_DMA_Init+0xae>
    /* Get memory burst and peripheral burst */
    tmp |=  hdma->Init.MemBurst | hdma->Init.PeriphBurst;
  }
  
  /* Write to DMA Stream CR register */
  hdma->Instance->CR = tmp;  
 8000fb4:	601a      	str	r2, [r3, #0]

  /* Get the FCR register value */
  tmp = hdma->Instance->FCR;
 8000fb6:	695a      	ldr	r2, [r3, #20]

  /* Clear Direct mode and FIFO threshold bits */
  tmp &= (uint32_t)~(DMA_SxFCR_DMDIS | DMA_SxFCR_FTH);
 8000fb8:	f022 0207 	bic.w	r2, r2, #7

  /* Prepare the DMA Stream FIFO configuration */
  tmp |= hdma->Init.FIFOMode;
 8000fbc:	4311      	orrs	r1, r2
  *                     the configuration information for the specified DMA Stream. 
  * @retval Stream base address
  */
static uint32_t DMA_CalcBaseAndBitshift(DMA_HandleTypeDef *hdma)
{
  uint32_t stream_number = (((uint32_t)hdma->Instance & 0xFFU) - 16U) / 24U;
 8000fbe:	b2d8      	uxtb	r0, r3
 8000fc0:	4a2b      	ldr	r2, [pc, #172]	@ (8001070 <HAL_DMA_Init+0x120>)
  hdma->Instance->FCR = tmp;
 8000fc2:	6159      	str	r1, [r3, #20]
  uint32_t stream_number = (((uint32_t)hdma->Instance & 0xFFU) - 16U) / 24U;
 8000fc4:	3810      	subs	r0, #16
 8000fc6:	fba2 5200 	umull	r5, r2, r2, r0
 8000fca:	0912      	lsrs	r2, r2, #4
  
  /* lookup table for necessary bitshift of flags within status registers */
  static const uint8_t flagBitshiftOffset[8U] = {0U, 6U, 16U, 22U, 0U, 6U, 16U, 22U};
  hdma->StreamIndex = flagBitshiftOffset[stream_number];
 8000fcc:	4929      	ldr	r1, [pc, #164]	@ (8001074 <HAL_DMA_Init+0x124>)
  
  if (stream_number > 3U)
  {
    /* return pointer to HISR and HIFCR */
    hdma->StreamBaseAddress = (((uint32_t)hdma->Instance & (uint32_t)(~0x3FFU)) + 4U);
 8000fce:	f36f 0309 	bfc	r3, #0, #10
  hdma->StreamIndex = flagBitshiftOffset[stream_number];
 8000fd2:	5c89      	ldrb	r1, [r1, r2]
 8000fd4:	65e1      	str	r1, [r4, #92]	@ 0x5c
  if (stream_number > 3U)
 8000fd6:	285f      	cmp	r0, #95	@ 0x5f
    hdma->StreamBaseAddress = (((uint32_t)hdma->Instance & (uint32_t)(~0x3FFU)) + 4U);
 8000fd8:	bf88      	it	hi
 8000fda:	3304      	addhi	r3, #4
  regs->IFCR = 0x3FU << hdma->StreamIndex;
 8000fdc:	223f      	movs	r2, #63	@ 0x3f
 8000fde:	408a      	lsls	r2, r1
 8000fe0:	65a3      	str	r3, [r4, #88]	@ 0x58
  hdma->ErrorCode = HAL_DMA_ERROR_NONE;
 8000fe2:	2000      	movs	r0, #0
  regs->IFCR = 0x3FU << hdma->StreamIndex;
 8000fe4:	609a      	str	r2, [r3, #8]
  hdma->State = HAL_DMA_STATE_READY;
 8000fe6:	2301      	movs	r3, #1
  hdma->ErrorCode = HAL_DMA_ERROR_NONE;
 8000fe8:	6560      	str	r0, [r4, #84]	@ 0x54
  hdma->State = HAL_DMA_STATE_READY;
 8000fea:	f884 3035 	strb.w	r3, [r4, #53]	@ 0x35
}
 8000fee:	bd38      	pop	{r3, r4, r5, pc}
      hdma->State = HAL_DMA_STATE_TIMEOUT;
 8000ff0:	2303      	movs	r3, #3
      hdma->ErrorCode = HAL_DMA_ERROR_TIMEOUT;
 8000ff2:	2220      	movs	r2, #32
 8000ff4:	6562      	str	r2, [r4, #84]	@ 0x54
      hdma->State = HAL_DMA_STATE_TIMEOUT;
 8000ff6:	f884 3035 	strb.w	r3, [r4, #53]	@ 0x35
      return HAL_TIMEOUT;
 8000ffa:	4618      	mov	r0, r3
}
 8000ffc:	bd38      	pop	{r3, r4, r5, pc}
    tmp |=  hdma->Init.MemBurst | hdma->Init.PeriphBurst;
 8000ffe:	e9d4 510b 	ldrd	r5, r1, [r4, #44]	@ 0x2c
 8001002:	4329      	orrs	r1, r5
 8001004:	4311      	orrs	r1, r2
  hdma->Instance->CR = tmp;  
 8001006:	6019      	str	r1, [r3, #0]
  tmp = hdma->Instance->FCR;
 8001008:	6959      	ldr	r1, [r3, #20]
    tmp |= hdma->Init.FIFOThreshold;
 800100a:	6aa2      	ldr	r2, [r4, #40]	@ 0x28
  tmp &= (uint32_t)~(DMA_SxFCR_DMDIS | DMA_SxFCR_FTH);
 800100c:	f021 0107 	bic.w	r1, r1, #7
 8001010:	4311      	orrs	r1, r2
    tmp |= hdma->Init.FIFOThreshold;
 8001012:	f041 0104 	orr.w	r1, r1, #4
    if (hdma->Init.MemBurst != DMA_MBURST_SINGLE)
 8001016:	2d00      	cmp	r5, #0
 8001018:	d0d1      	beq.n	8000fbe <HAL_DMA_Init+0x6e>
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t tmp = hdma->Init.FIFOThreshold;
  
  /* Memory Data size equal to Byte */
  if(hdma->Init.MemDataAlignment == DMA_MDATAALIGN_BYTE)
 800101a:	b968      	cbnz	r0, 8001038 <HAL_DMA_Init+0xe8>
  {
    switch (tmp)
 800101c:	2a01      	cmp	r2, #1
 800101e:	d021      	beq.n	8001064 <HAL_DMA_Init+0x114>
 8001020:	f032 0202 	bics.w	r2, r2, #2
 8001024:	d1cb      	bne.n	8000fbe <HAL_DMA_Init+0x6e>
    case DMA_FIFO_THRESHOLD_HALFFULL:
    case DMA_FIFO_THRESHOLD_3QUARTERSFULL:
      status = HAL_ERROR;
      break;
    case DMA_FIFO_THRESHOLD_FULL:
      if ((hdma->Init.MemBurst & DMA_SxCR_MBURST_1) == DMA_SxCR_MBURST_1)
 8001026:	01ea      	lsls	r2, r5, #7
 8001028:	d5c9      	bpl.n	8000fbe <HAL_DMA_Init+0x6e>
        hdma->ErrorCode = HAL_DMA_ERROR_PARAM;
 800102a:	2240      	movs	r2, #64	@ 0x40
        hdma->State = HAL_DMA_STATE_READY;
 800102c:	2301      	movs	r3, #1
        hdma->ErrorCode = HAL_DMA_ERROR_PARAM;
 800102e:	6562      	str	r2, [r4, #84]	@ 0x54
        hdma->State = HAL_DMA_STATE_READY;
 8001030:	f884 3035 	strb.w	r3, [r4, #53]	@ 0x35
    return HAL_ERROR;
 8001034:	2001      	movs	r0, #1
}
 8001036:	bd38      	pop	{r3, r4, r5, pc}
  else if (hdma->Init.MemDataAlignment == DMA_MDATAALIGN_HALFWORD)
 8001038:	f5b0 5f00 	cmp.w	r0, #8192	@ 0x2000
 800103c:	d004      	beq.n	8001048 <HAL_DMA_Init+0xf8>
    switch (tmp)
 800103e:	2a02      	cmp	r2, #2
 8001040:	d9f3      	bls.n	800102a <HAL_DMA_Init+0xda>
 8001042:	2a03      	cmp	r2, #3
 8001044:	d0ef      	beq.n	8001026 <HAL_DMA_Init+0xd6>
 8001046:	e7ba      	b.n	8000fbe <HAL_DMA_Init+0x6e>
    switch (tmp)
 8001048:	2a03      	cmp	r2, #3
 800104a:	d8b8      	bhi.n	8000fbe <HAL_DMA_Init+0x6e>
 800104c:	a001      	add	r0, pc, #4	@ (adr r0, 8001054 <HAL_DMA_Init+0x104>)
 800104e:	f850 f022 	ldr.w	pc, [r0, r2, lsl #2]
 8001052:	bf00      	nop
 8001054:	0800102b 	.word	0x0800102b
 8001058:	08001027 	.word	0x08001027
 800105c:	0800102b 	.word	0x0800102b
 8001060:	08001065 	.word	0x08001065
      if (hdma->Init.MemBurst == DMA_MBURST_INC16)
 8001064:	f1b5 7fc0 	cmp.w	r5, #25165824	@ 0x1800000
 8001068:	d1a9      	bne.n	8000fbe <HAL_DMA_Init+0x6e>
 800106a:	e7de      	b.n	800102a <HAL_DMA_Init+0xda>
 800106c:	f010803f 	.word	0xf010803f
 8001070:	aaaaaaab 	.word	0xaaaaaaab
 8001074:	08003994 	.word	0x08003994

08001078 <HAL_DMA_IRQHandler>:
{
 8001078:	b5f0      	push	{r4, r5, r6, r7, lr}
 800107a:	b083      	sub	sp, #12
  uint32_t timeout = SystemCoreClock / 9600U;
 800107c:	4a67      	ldr	r2, [pc, #412]	@ (800121c <HAL_DMA_IRQHandler+0x1a4>)
  DMA_Base_Registers *regs = (DMA_Base_Registers *)hdma->StreamBaseAddress;
 800107e:	6d87      	ldr	r7, [r0, #88]	@ 0x58
  uint32_t timeout = SystemCoreClock / 9600U;
 8001080:	6816      	ldr	r6, [r2, #0]
  __IO uint32_t count = 0U;
 8001082:	2300      	movs	r3, #0
 8001084:	9301      	str	r3, [sp, #4]
  if ((tmpisr & (DMA_FLAG_TEIF0_4 << hdma->StreamIndex)) != RESET)
 8001086:	6dc3      	ldr	r3, [r0, #92]	@ 0x5c
  tmpisr = regs->ISR;
 8001088:	683d      	ldr	r5, [r7, #0]
  if ((tmpisr & (DMA_FLAG_TEIF0_4 << hdma->StreamIndex)) != RESET)
 800108a:	2208      	movs	r2, #8
 800108c:	409a      	lsls	r2, r3
 800108e:	422a      	tst	r2, r5
{
 8001090:	4604      	mov	r4, r0
  if ((tmpisr & (DMA_FLAG_TEIF0_4 << hdma->StreamIndex)) != RESET)
 8001092:	d003      	beq.n	800109c <HAL_DMA_IRQHandler+0x24>
    if(__HAL_DMA_GET_IT_SOURCE(hdma, DMA_IT_TE) != RESET)
 8001094:	6801      	ldr	r1, [r0, #0]
 8001096:	6808      	ldr	r0, [r1, #0]
 8001098:	0740      	lsls	r0, r0, #29
 800109a:	d478      	bmi.n	800118e <HAL_DMA_IRQHandler+0x116>
  if ((tmpisr & (DMA_FLAG_FEIF0_4 << hdma->StreamIndex)) != RESET)
 800109c:	2201      	movs	r2, #1
 800109e:	409a      	lsls	r2, r3
 80010a0:	422a      	tst	r2, r5
 80010a2:	d003      	beq.n	80010ac <HAL_DMA_IRQHandler+0x34>
    if(__HAL_DMA_GET_IT_SOURCE(hdma, DMA_IT_FE) != RESET)
 80010a4:	6821      	ldr	r1, [r4, #0]
 80010a6:	6949      	ldr	r1, [r1, #20]
 80010a8:	0608      	lsls	r0, r1, #24
 80010aa:	d46a      	bmi.n	8001182 <HAL_DMA_IRQHandler+0x10a>
  if ((tmpisr & (DMA_FLAG_DMEIF0_4 << hdma->StreamIndex)) != RESET)
 80010ac:	2204      	movs	r2, #4
 80010ae:	409a      	lsls	r2, r3
 80010b0:	422a      	tst	r2, r5
 80010b2:	d003      	beq.n	80010bc <HAL_DMA_IRQHandler+0x44>
    if(__HAL_DMA_GET_IT_SOURCE(hdma, DMA_IT_DME) != RESET)
 80010b4:	6821      	ldr	r1, [r4, #0]
 80010b6:	6809      	ldr	r1, [r1, #0]
 80010b8:	0789      	lsls	r1, r1, #30
 80010ba:	d45c      	bmi.n	8001176 <HAL_DMA_IRQHandler+0xfe>
  if ((tmpisr & (DMA_FLAG_HTIF0_4 << hdma->StreamIndex)) != RESET)
 80010bc:	2210      	movs	r2, #16
 80010be:	409a      	lsls	r2, r3
 80010c0:	422a      	tst	r2, r5
 80010c2:	d003      	beq.n	80010cc <HAL_DMA_IRQHandler+0x54>
    if(__HAL_DMA_GET_IT_SOURCE(hdma, DMA_IT_HT) != RESET)
 80010c4:	6821      	ldr	r1, [r4, #0]
 80010c6:	6808      	ldr	r0, [r1, #0]
 80010c8:	0700      	lsls	r0, r0, #28
 80010ca:	d441      	bmi.n	8001150 <HAL_DMA_IRQHandler+0xd8>
  if ((tmpisr & (DMA_FLAG_TCIF0_4 << hdma->StreamIndex)) != RESET)
 80010cc:	2220      	movs	r2, #32
 80010ce:	409a      	lsls	r2, r3
 80010d0:	422a      	tst	r2, r5
 80010d2:	d014      	beq.n	80010fe <HAL_DMA_IRQHandler+0x86>
    if(__HAL_DMA_GET_IT_SOURCE(hdma, DMA_IT_TC) != RESET)
 80010d4:	6821      	ldr	r1, [r4, #0]
 80010d6:	6808      	ldr	r0, [r1, #0]
 80010d8:	06c0      	lsls	r0, r0, #27
 80010da:	d510      	bpl.n	80010fe <HAL_DMA_IRQHandler+0x86>
      regs->IFCR = DMA_FLAG_TCIF0_4 << hdma->StreamIndex;
 80010dc:	60ba      	str	r2, [r7, #8]
      if(HAL_DMA_STATE_ABORT == hdma->State)
 80010de:	f894 2035 	ldrb.w	r2, [r4, #53]	@ 0x35
 80010e2:	2a05      	cmp	r2, #5
 80010e4:	d063      	beq.n	80011ae <HAL_DMA_IRQHandler+0x136>
      if(((hdma->Instance->CR) & (uint32_t)(DMA_SxCR_DBM)) != RESET)
 80010e6:	680b      	ldr	r3, [r1, #0]
 80010e8:	f413 2f80 	tst.w	r3, #262144	@ 0x40000
        if((hdma->Instance->CR & DMA_SxCR_CT) == RESET)
 80010ec:	680b      	ldr	r3, [r1, #0]
      if(((hdma->Instance->CR) & (uint32_t)(DMA_SxCR_DBM)) != RESET)
 80010ee:	d07e      	beq.n	80011ee <HAL_DMA_IRQHandler+0x176>
        if((hdma->Instance->CR & DMA_SxCR_CT) == RESET)
 80010f0:	0319      	lsls	r1, r3, #12
 80010f2:	f140 8089 	bpl.w	8001208 <HAL_DMA_IRQHandler+0x190>
        if(hdma->XferCpltCallback != NULL)
 80010f6:	6be3      	ldr	r3, [r4, #60]	@ 0x3c
 80010f8:	b10b      	cbz	r3, 80010fe <HAL_DMA_IRQHandler+0x86>
          hdma->XferCpltCallback(hdma);
 80010fa:	4620      	mov	r0, r4
 80010fc:	4798      	blx	r3
  if(hdma->ErrorCode != HAL_DMA_ERROR_NONE)
 80010fe:	6d63      	ldr	r3, [r4, #84]	@ 0x54
 8001100:	b323      	cbz	r3, 800114c <HAL_DMA_IRQHandler+0xd4>
    if((hdma->ErrorCode & HAL_DMA_ERROR_TE) != RESET)
 8001102:	6d63      	ldr	r3, [r4, #84]	@ 0x54
 8001104:	07da      	lsls	r2, r3, #31
 8001106:	d51a      	bpl.n	800113e <HAL_DMA_IRQHandler+0xc6>
      __HAL_DMA_DISABLE(hdma);
 8001108:	6822      	ldr	r2, [r4, #0]
  uint32_t timeout = SystemCoreClock / 9600U;
 800110a:	4945      	ldr	r1, [pc, #276]	@ (8001220 <HAL_DMA_IRQHandler+0x1a8>)
      hdma->State = HAL_DMA_STATE_ABORT;
 800110c:	2305      	movs	r3, #5
 800110e:	f884 3035 	strb.w	r3, [r4, #53]	@ 0x35
      __HAL_DMA_DISABLE(hdma);
 8001112:	6813      	ldr	r3, [r2, #0]
  uint32_t timeout = SystemCoreClock / 9600U;
 8001114:	fba1 1606 	umull	r1, r6, r1, r6
      __HAL_DMA_DISABLE(hdma);
 8001118:	f023 0301 	bic.w	r3, r3, #1
  uint32_t timeout = SystemCoreClock / 9600U;
 800111c:	0ab6      	lsrs	r6, r6, #10
      __HAL_DMA_DISABLE(hdma);
 800111e:	6013      	str	r3, [r2, #0]
 8001120:	e002      	b.n	8001128 <HAL_DMA_IRQHandler+0xb0>
      while((hdma->Instance->CR & DMA_SxCR_EN) != RESET);
 8001122:	6813      	ldr	r3, [r2, #0]
 8001124:	07db      	lsls	r3, r3, #31
 8001126:	d504      	bpl.n	8001132 <HAL_DMA_IRQHandler+0xba>
        if (++count > timeout)
 8001128:	9b01      	ldr	r3, [sp, #4]
 800112a:	3301      	adds	r3, #1
 800112c:	42b3      	cmp	r3, r6
 800112e:	9301      	str	r3, [sp, #4]
 8001130:	d9f7      	bls.n	8001122 <HAL_DMA_IRQHandler+0xaa>
      hdma->State = HAL_DMA_STATE_READY;
 8001132:	2201      	movs	r2, #1
      __HAL_UNLOCK(hdma);
 8001134:	2300      	movs	r3, #0
      hdma->State = HAL_DMA_STATE_READY;
 8001136:	f884 2035 	strb.w	r2, [r4, #53]	@ 0x35
      __HAL_UNLOCK(hdma);
 800113a:	f884 3034 	strb.w	r3, [r4, #52]	@ 0x34
    if(hdma->XferErrorCallback != NULL)
 800113e:	6ce3      	ldr	r3, [r4, #76]	@ 0x4c
 8001140:	b123      	cbz	r3, 800114c <HAL_DMA_IRQHandler+0xd4>
      hdma->XferErrorCallback(hdma);
 8001142:	4620      	mov	r0, r4
}
 8001144:	b003      	add	sp, #12
 8001146:	e8bd 40f0 	ldmia.w	sp!, {r4, r5, r6, r7, lr}
      hdma->XferErrorCallback(hdma);
 800114a:	4718      	bx	r3
}
 800114c:	b003      	add	sp, #12
 800114e:	bdf0      	pop	{r4, r5, r6, r7, pc}
      regs->IFCR = DMA_FLAG_HTIF0_4 << hdma->StreamIndex;
 8001150:	60ba      	str	r2, [r7, #8]
      if(((hdma->Instance->CR) & (uint32_t)(DMA_SxCR_DBM)) != RESET)
 8001152:	680a      	ldr	r2, [r1, #0]
 8001154:	f412 2f80 	tst.w	r2, #262144	@ 0x40000
        if((hdma->Instance->CR & DMA_SxCR_CT) == RESET)
 8001158:	680a      	ldr	r2, [r1, #0]
      if(((hdma->Instance->CR) & (uint32_t)(DMA_SxCR_DBM)) != RESET)
 800115a:	d122      	bne.n	80011a2 <HAL_DMA_IRQHandler+0x12a>
        if((hdma->Instance->CR & DMA_SxCR_CIRC) == RESET)
 800115c:	05d2      	lsls	r2, r2, #23
 800115e:	d403      	bmi.n	8001168 <HAL_DMA_IRQHandler+0xf0>
          hdma->Instance->CR  &= ~(DMA_IT_HT);
 8001160:	680a      	ldr	r2, [r1, #0]
 8001162:	f022 0208 	bic.w	r2, r2, #8
 8001166:	600a      	str	r2, [r1, #0]
        if(hdma->XferHalfCpltCallback != NULL)
 8001168:	6c22      	ldr	r2, [r4, #64]	@ 0x40
 800116a:	2a00      	cmp	r2, #0
 800116c:	d0ae      	beq.n	80010cc <HAL_DMA_IRQHandler+0x54>
          hdma->XferHalfCpltCallback(hdma);
 800116e:	4620      	mov	r0, r4
 8001170:	4790      	blx	r2
  if ((tmpisr & (DMA_FLAG_TCIF0_4 << hdma->StreamIndex)) != RESET)
 8001172:	6de3      	ldr	r3, [r4, #92]	@ 0x5c
 8001174:	e7aa      	b.n	80010cc <HAL_DMA_IRQHandler+0x54>
      regs->IFCR = DMA_FLAG_DMEIF0_4 << hdma->StreamIndex;
 8001176:	60ba      	str	r2, [r7, #8]
      hdma->ErrorCode |= HAL_DMA_ERROR_DME;
 8001178:	6d62      	ldr	r2, [r4, #84]	@ 0x54
 800117a:	f042 0204 	orr.w	r2, r2, #4
 800117e:	6562      	str	r2, [r4, #84]	@ 0x54
 8001180:	e79c      	b.n	80010bc <HAL_DMA_IRQHandler+0x44>
      regs->IFCR = DMA_FLAG_FEIF0_4 << hdma->StreamIndex;
 8001182:	60ba      	str	r2, [r7, #8]
      hdma->ErrorCode |= HAL_DMA_ERROR_FE;
 8001184:	6d62      	ldr	r2, [r4, #84]	@ 0x54
 8001186:	f042 0202 	orr.w	r2, r2, #2
 800118a:	6562      	str	r2, [r4, #84]	@ 0x54
 800118c:	e78e      	b.n	80010ac <HAL_DMA_IRQHandler+0x34>
      hdma->Instance->CR  &= ~(DMA_IT_TE);
 800118e:	6808      	ldr	r0, [r1, #0]
 8001190:	f020 0004 	bic.w	r0, r0, #4
 8001194:	6008      	str	r0, [r1, #0]
      regs->IFCR = DMA_FLAG_TEIF0_4 << hdma->StreamIndex;
 8001196:	60ba      	str	r2, [r7, #8]
      hdma->ErrorCode |= HAL_DMA_ERROR_TE;
 8001198:	6d62      	ldr	r2, [r4, #84]	@ 0x54
 800119a:	f042 0201 	orr.w	r2, r2, #1
 800119e:	6562      	str	r2, [r4, #84]	@ 0x54
 80011a0:	e77c      	b.n	800109c <HAL_DMA_IRQHandler+0x24>
        if((hdma->Instance->CR & DMA_SxCR_CT) == RESET)
 80011a2:	0311      	lsls	r1, r2, #12
 80011a4:	d5e0      	bpl.n	8001168 <HAL_DMA_IRQHandler+0xf0>
          if(hdma->XferM1HalfCpltCallback != NULL)
 80011a6:	6ca2      	ldr	r2, [r4, #72]	@ 0x48
 80011a8:	2a00      	cmp	r2, #0
 80011aa:	d1e0      	bne.n	800116e <HAL_DMA_IRQHandler+0xf6>
 80011ac:	e78e      	b.n	80010cc <HAL_DMA_IRQHandler+0x54>
        hdma->Instance->CR  &= ~(DMA_IT_TC | DMA_IT_TE | DMA_IT_DME);
 80011ae:	680a      	ldr	r2, [r1, #0]
 80011b0:	f022 0216 	bic.w	r2, r2, #22
 80011b4:	600a      	str	r2, [r1, #0]
        hdma->Instance->FCR &= ~(DMA_IT_FE);
 80011b6:	694a      	ldr	r2, [r1, #20]
 80011b8:	f022 0280 	bic.w	r2, r2, #128	@ 0x80
 80011bc:	614a      	str	r2, [r1, #20]
        if((hdma->XferHalfCpltCallback != NULL) || (hdma->XferM1HalfCpltCallback != NULL))
 80011be:	6c22      	ldr	r2, [r4, #64]	@ 0x40
 80011c0:	b33a      	cbz	r2, 8001212 <HAL_DMA_IRQHandler+0x19a>
          hdma->Instance->CR  &= ~(DMA_IT_HT);
 80011c2:	680a      	ldr	r2, [r1, #0]
 80011c4:	f022 0208 	bic.w	r2, r2, #8
 80011c8:	600a      	str	r2, [r1, #0]
        regs->IFCR = 0x3FU << hdma->StreamIndex;
 80011ca:	223f      	movs	r2, #63	@ 0x3f
 80011cc:	fa02 f303 	lsl.w	r3, r2, r3
        if(hdma->XferAbortCallback != NULL)
 80011d0:	6d21      	ldr	r1, [r4, #80]	@ 0x50
        regs->IFCR = 0x3FU << hdma->StreamIndex;
 80011d2:	60bb      	str	r3, [r7, #8]
        hdma->State = HAL_DMA_STATE_READY;
 80011d4:	2201      	movs	r2, #1
        __HAL_UNLOCK(hdma);
 80011d6:	2300      	movs	r3, #0
        hdma->State = HAL_DMA_STATE_READY;
 80011d8:	f884 2035 	strb.w	r2, [r4, #53]	@ 0x35
        __HAL_UNLOCK(hdma);
 80011dc:	f884 3034 	strb.w	r3, [r4, #52]	@ 0x34
        if(hdma->XferAbortCallback != NULL)
 80011e0:	2900      	cmp	r1, #0
 80011e2:	d0b3      	beq.n	800114c <HAL_DMA_IRQHandler+0xd4>
          hdma->XferAbortCallback(hdma);
 80011e4:	4620      	mov	r0, r4
}
 80011e6:	b003      	add	sp, #12
 80011e8:	e8bd 40f0 	ldmia.w	sp!, {r4, r5, r6, r7, lr}
          hdma->XferAbortCallback(hdma);
 80011ec:	4708      	bx	r1
        if((hdma->Instance->CR & DMA_SxCR_CIRC) == RESET)
 80011ee:	f413 7380 	ands.w	r3, r3, #256	@ 0x100
 80011f2:	d180      	bne.n	80010f6 <HAL_DMA_IRQHandler+0x7e>
          hdma->Instance->CR  &= ~(DMA_IT_TC);
 80011f4:	680a      	ldr	r2, [r1, #0]
 80011f6:	f022 0210 	bic.w	r2, r2, #16
 80011fa:	600a      	str	r2, [r1, #0]
          hdma->State = HAL_DMA_STATE_READY;
 80011fc:	2201      	movs	r2, #1
 80011fe:	f884 2035 	strb.w	r2, [r4, #53]	@ 0x35
          __HAL_UNLOCK(hdma);
 8001202:	f884 3034 	strb.w	r3, [r4, #52]	@ 0x34
 8001206:	e776      	b.n	80010f6 <HAL_DMA_IRQHandler+0x7e>
          if(hdma->XferM1CpltCallback != NULL)
 8001208:	6c63      	ldr	r3, [r4, #68]	@ 0x44
 800120a:	2b00      	cmp	r3, #0
 800120c:	f47f af75 	bne.w	80010fa <HAL_DMA_IRQHandler+0x82>
 8001210:	e775      	b.n	80010fe <HAL_DMA_IRQHandler+0x86>
        if((hdma->XferHalfCpltCallback != NULL) || (hdma->XferM1HalfCpltCallback != NULL))
 8001212:	6ca2      	ldr	r2, [r4, #72]	@ 0x48
 8001214:	2a00      	cmp	r2, #0
 8001216:	d1d4      	bne.n	80011c2 <HAL_DMA_IRQHandler+0x14a>
 8001218:	e7d7      	b.n	80011ca <HAL_DMA_IRQHandler+0x152>
 800121a:	bf00      	nop
 800121c:	20000000 	.word	0x20000000
 8001220:	1b4e81b5 	.word	0x1b4e81b5

08001224 <HAL_GPIO_Init>:
  * @param  GPIO_Init pointer to a GPIO_InitTypeDef structure that contains
  *         the configuration information for the specified GPIO peripheral.
  * @retval None
  */
void HAL_GPIO_Init(GPIO_TypeDef  *GPIOx, GPIO_InitTypeDef *GPIO_Init)
{
 8001224:	e92d 4ff0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
  assert_param(IS_GPIO_ALL_INSTANCE(GPIOx));
  assert_param(IS_GPIO_PIN(GPIO_Init->Pin));
  assert_param(IS_GPIO_MODE(GPIO_Init->Mode));

  /* Configure the port pins */
  for(position = 0U; position < GPIO_NUMBER; position++)
 8001228:	2300      	movs	r3, #0
  {
    /* Get the IO position */
    ioposition = 0x01U << position;
    /* Get the current IO position */
    iocurrent = (uint32_t)(GPIO_Init->Pin) & ioposition;
 800122a:	680c      	ldr	r4, [r1, #0]
      /*--------------------- EXTI Mode Configuration ------------------------*/
      /* Configure the External Interrupt or event for the current IO */
      if((GPIO_Init->Mode & EXTI_MODE) != 0x00U)
      {
        /* Enable SYSCFG Clock */
        __HAL_RCC_SYSCFG_CLK_ENABLE();
 800122c:	f8df a214 	ldr.w	sl, [pc, #532]	@ 8001444 <HAL_GPIO_Init+0x220>
{
 8001230:	b085      	sub	sp, #20
    ioposition = 0x01U << position;
 8001232:	f04f 0b01 	mov.w	fp, #1

        temp = SYSCFG->EXTICR[position >> 2U];
        temp &= ~(0x0FU << (4U * (position & 0x03U)));
 8001236:	4689      	mov	r9, r1
 8001238:	e003      	b.n	8001242 <HAL_GPIO_Init+0x1e>
  for(position = 0U; position < GPIO_NUMBER; position++)
 800123a:	3301      	adds	r3, #1
 800123c:	2b10      	cmp	r3, #16
 800123e:	f000 8092 	beq.w	8001366 <HAL_GPIO_Init+0x142>
    ioposition = 0x01U << position;
 8001242:	fa0b f203 	lsl.w	r2, fp, r3
    iocurrent = (uint32_t)(GPIO_Init->Pin) & ioposition;
 8001246:	ea02 0804 	and.w	r8, r2, r4
    if(iocurrent == ioposition)
 800124a:	43a2      	bics	r2, r4
 800124c:	d1f5      	bne.n	800123a <HAL_GPIO_Init+0x16>
      if(((GPIO_Init->Mode & GPIO_MODE) == MODE_OUTPUT) || \
 800124e:	f8d9 1004 	ldr.w	r1, [r9, #4]
 8001252:	f001 0c03 	and.w	ip, r1, #3
 8001256:	005a      	lsls	r2, r3, #1
        temp &= ~(GPIO_OSPEEDER_OSPEEDR0 << (position * 2U));
 8001258:	2503      	movs	r5, #3
      if(((GPIO_Init->Mode & GPIO_MODE) == MODE_OUTPUT) || \
 800125a:	f10c 36ff 	add.w	r6, ip, #**********
        temp &= ~(GPIO_OSPEEDER_OSPEEDR0 << (position * 2U));
 800125e:	4095      	lsls	r5, r2
      if(((GPIO_Init->Mode & GPIO_MODE) == MODE_OUTPUT) || \
 8001260:	2e01      	cmp	r6, #1
        temp &= ~(GPIO_OSPEEDER_OSPEEDR0 << (position * 2U));
 8001262:	ea6f 0505 	mvn.w	r5, r5
      if(((GPIO_Init->Mode & GPIO_MODE) == MODE_OUTPUT) || \
 8001266:	f240 8081 	bls.w	800136c <HAL_GPIO_Init+0x148>
      if((GPIO_Init->Mode & GPIO_MODE) != MODE_ANALOG)
 800126a:	f1bc 0f03 	cmp.w	ip, #3
 800126e:	f040 80d9 	bne.w	8001424 <HAL_GPIO_Init+0x200>
      temp = GPIOx->MODER;
 8001272:	6806      	ldr	r6, [r0, #0]
      temp |= ((GPIO_Init->Mode & GPIO_MODE) << (position * 2U));
 8001274:	fa0c f202 	lsl.w	r2, ip, r2
      temp &= ~(GPIO_MODER_MODER0 << (position * 2U));
 8001278:	4035      	ands	r5, r6
      temp |= ((GPIO_Init->Mode & GPIO_MODE) << (position * 2U));
 800127a:	432a      	orrs	r2, r5
      if((GPIO_Init->Mode & EXTI_MODE) != 0x00U)
 800127c:	f411 3f40 	tst.w	r1, #196608	@ 0x30000
      GPIOx->MODER = temp;
 8001280:	6002      	str	r2, [r0, #0]
      if((GPIO_Init->Mode & EXTI_MODE) != 0x00U)
 8001282:	d0da      	beq.n	800123a <HAL_GPIO_Init+0x16>
        __HAL_RCC_SYSCFG_CLK_ENABLE();
 8001284:	2200      	movs	r2, #0
 8001286:	9203      	str	r2, [sp, #12]
 8001288:	f8da 2044 	ldr.w	r2, [sl, #68]	@ 0x44
 800128c:	f442 4280 	orr.w	r2, r2, #16384	@ 0x4000
 8001290:	f8ca 2044 	str.w	r2, [sl, #68]	@ 0x44
 8001294:	f8da 2044 	ldr.w	r2, [sl, #68]	@ 0x44
 8001298:	f402 4280 	and.w	r2, r2, #16384	@ 0x4000
 800129c:	9203      	str	r2, [sp, #12]
 800129e:	9a03      	ldr	r2, [sp, #12]
        temp = SYSCFG->EXTICR[position >> 2U];
 80012a0:	f023 0203 	bic.w	r2, r3, #3
 80012a4:	f102 4280 	add.w	r2, r2, #1073741824	@ 0x40000000
        temp &= ~(0x0FU << (4U * (position & 0x03U)));
 80012a8:	f003 0603 	and.w	r6, r3, #3
 80012ac:	270f      	movs	r7, #15
 80012ae:	f502 329c 	add.w	r2, r2, #79872	@ 0x13800
 80012b2:	00b6      	lsls	r6, r6, #2
 80012b4:	fa07 fc06 	lsl.w	ip, r7, r6
        temp |= ((uint32_t)(GPIO_GET_INDEX(GPIOx)) << (4U * (position & 0x03U)));
 80012b8:	4f5f      	ldr	r7, [pc, #380]	@ (8001438 <HAL_GPIO_Init+0x214>)
        temp = SYSCFG->EXTICR[position >> 2U];
 80012ba:	6895      	ldr	r5, [r2, #8]
        temp |= ((uint32_t)(GPIO_GET_INDEX(GPIOx)) << (4U * (position & 0x03U)));
 80012bc:	42b8      	cmp	r0, r7
        temp &= ~(0x0FU << (4U * (position & 0x03U)));
 80012be:	ea25 050c 	bic.w	r5, r5, ip
        temp |= ((uint32_t)(GPIO_GET_INDEX(GPIOx)) << (4U * (position & 0x03U)));
 80012c2:	d027      	beq.n	8001314 <HAL_GPIO_Init+0xf0>
 80012c4:	f507 6780 	add.w	r7, r7, #1024	@ 0x400
 80012c8:	42b8      	cmp	r0, r7
 80012ca:	f000 8087 	beq.w	80013dc <HAL_GPIO_Init+0x1b8>
 80012ce:	4f5b      	ldr	r7, [pc, #364]	@ (800143c <HAL_GPIO_Init+0x218>)
 80012d0:	42b8      	cmp	r0, r7
 80012d2:	f000 8089 	beq.w	80013e8 <HAL_GPIO_Init+0x1c4>
 80012d6:	f8df c170 	ldr.w	ip, [pc, #368]	@ 8001448 <HAL_GPIO_Init+0x224>
 80012da:	4560      	cmp	r0, ip
 80012dc:	f000 808a 	beq.w	80013f4 <HAL_GPIO_Init+0x1d0>
 80012e0:	f8df c168 	ldr.w	ip, [pc, #360]	@ 800144c <HAL_GPIO_Init+0x228>
 80012e4:	4560      	cmp	r0, ip
 80012e6:	f000 808b 	beq.w	8001400 <HAL_GPIO_Init+0x1dc>
 80012ea:	f8df c164 	ldr.w	ip, [pc, #356]	@ 8001450 <HAL_GPIO_Init+0x22c>
 80012ee:	4560      	cmp	r0, ip
 80012f0:	f000 808c 	beq.w	800140c <HAL_GPIO_Init+0x1e8>
 80012f4:	f8df c15c 	ldr.w	ip, [pc, #348]	@ 8001454 <HAL_GPIO_Init+0x230>
 80012f8:	4560      	cmp	r0, ip
 80012fa:	f000 808d 	beq.w	8001418 <HAL_GPIO_Init+0x1f4>
 80012fe:	f8df c158 	ldr.w	ip, [pc, #344]	@ 8001458 <HAL_GPIO_Init+0x234>
 8001302:	4560      	cmp	r0, ip
 8001304:	bf0c      	ite	eq
 8001306:	f04f 0c07 	moveq.w	ip, #7
 800130a:	f04f 0c08 	movne.w	ip, #8
 800130e:	fa0c f606 	lsl.w	r6, ip, r6
 8001312:	4335      	orrs	r5, r6
        SYSCFG->EXTICR[position >> 2U] = temp;
 8001314:	6095      	str	r5, [r2, #8]

        /* Clear Rising Falling edge configuration */
        temp = EXTI->RTSR;
 8001316:	4a4a      	ldr	r2, [pc, #296]	@ (8001440 <HAL_GPIO_Init+0x21c>)
 8001318:	6892      	ldr	r2, [r2, #8]
        temp &= ~((uint32_t)iocurrent);
        if((GPIO_Init->Mode & TRIGGER_RISING) != 0x00U)
 800131a:	02ce      	lsls	r6, r1, #11
        temp &= ~((uint32_t)iocurrent);
 800131c:	ea6f 0508 	mvn.w	r5, r8
        {
          temp |= iocurrent;
        }
        EXTI->RTSR = temp;
 8001320:	4e47      	ldr	r6, [pc, #284]	@ (8001440 <HAL_GPIO_Init+0x21c>)
        temp &= ~((uint32_t)iocurrent);
 8001322:	bf54      	ite	pl
 8001324:	402a      	andpl	r2, r5
          temp |= iocurrent;
 8001326:	ea48 0202 	orrmi.w	r2, r8, r2
        EXTI->RTSR = temp;
 800132a:	60b2      	str	r2, [r6, #8]

        temp = EXTI->FTSR;
 800132c:	68f2      	ldr	r2, [r6, #12]
        temp &= ~((uint32_t)iocurrent);
        if((GPIO_Init->Mode & TRIGGER_FALLING) != 0x00U)
        {
          temp |= iocurrent;
        }
        EXTI->FTSR = temp;
 800132e:	4e44      	ldr	r6, [pc, #272]	@ (8001440 <HAL_GPIO_Init+0x21c>)
        if((GPIO_Init->Mode & TRIGGER_FALLING) != 0x00U)
 8001330:	028f      	lsls	r7, r1, #10
        temp &= ~((uint32_t)iocurrent);
 8001332:	bf54      	ite	pl
 8001334:	402a      	andpl	r2, r5
          temp |= iocurrent;
 8001336:	ea48 0202 	orrmi.w	r2, r8, r2
        EXTI->FTSR = temp;
 800133a:	60f2      	str	r2, [r6, #12]

        temp = EXTI->EMR;
 800133c:	6872      	ldr	r2, [r6, #4]
        temp &= ~((uint32_t)iocurrent);
        if((GPIO_Init->Mode & EXTI_EVT) != 0x00U)
 800133e:	038e      	lsls	r6, r1, #14
        {
          temp |= iocurrent;
        }
        EXTI->EMR = temp;
 8001340:	4e3f      	ldr	r6, [pc, #252]	@ (8001440 <HAL_GPIO_Init+0x21c>)
        temp &= ~((uint32_t)iocurrent);
 8001342:	bf54      	ite	pl
 8001344:	402a      	andpl	r2, r5
          temp |= iocurrent;
 8001346:	ea48 0202 	orrmi.w	r2, r8, r2
        EXTI->EMR = temp;
 800134a:	6072      	str	r2, [r6, #4]

        /* Clear EXTI line configuration */
        temp = EXTI->IMR;
 800134c:	6832      	ldr	r2, [r6, #0]
        temp &= ~((uint32_t)iocurrent);
        if((GPIO_Init->Mode & EXTI_IT) != 0x00U)
 800134e:	03c9      	lsls	r1, r1, #15
  for(position = 0U; position < GPIO_NUMBER; position++)
 8001350:	f103 0301 	add.w	r3, r3, #1
        {
          temp |= iocurrent;
        }
        EXTI->IMR = temp;
 8001354:	493a      	ldr	r1, [pc, #232]	@ (8001440 <HAL_GPIO_Init+0x21c>)
        temp &= ~((uint32_t)iocurrent);
 8001356:	bf54      	ite	pl
 8001358:	402a      	andpl	r2, r5
          temp |= iocurrent;
 800135a:	ea48 0202 	orrmi.w	r2, r8, r2
  for(position = 0U; position < GPIO_NUMBER; position++)
 800135e:	2b10      	cmp	r3, #16
        EXTI->IMR = temp;
 8001360:	600a      	str	r2, [r1, #0]
  for(position = 0U; position < GPIO_NUMBER; position++)
 8001362:	f47f af6e 	bne.w	8001242 <HAL_GPIO_Init+0x1e>
      }
    }
  }
}
 8001366:	b005      	add	sp, #20
 8001368:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
        temp = GPIOx->OSPEEDR; 
 800136c:	6886      	ldr	r6, [r0, #8]
        temp &= ~(GPIO_OSPEEDER_OSPEEDR0 << (position * 2U));
 800136e:	ea06 0e05 	and.w	lr, r6, r5
        temp |= (GPIO_Init->Speed << (position * 2U));
 8001372:	f8d9 600c 	ldr.w	r6, [r9, #12]
 8001376:	4096      	lsls	r6, r2
 8001378:	ea46 060e 	orr.w	r6, r6, lr
        GPIOx->OSPEEDR = temp;
 800137c:	6086      	str	r6, [r0, #8]
        temp = GPIOx->OTYPER;
 800137e:	6846      	ldr	r6, [r0, #4]
        temp &= ~(GPIO_OTYPER_OT_0 << position) ;
 8001380:	ea26 0e08 	bic.w	lr, r6, r8
        temp |= (((GPIO_Init->Mode & OUTPUT_TYPE) >> OUTPUT_TYPE_Pos) << position);
 8001384:	f3c1 1600 	ubfx	r6, r1, #4, #1
 8001388:	409e      	lsls	r6, r3
 800138a:	ea46 060e 	orr.w	r6, r6, lr
        GPIOx->OTYPER = temp;
 800138e:	6046      	str	r6, [r0, #4]
        temp = GPIOx->PUPDR;
 8001390:	68c6      	ldr	r6, [r0, #12]
        temp &= ~(GPIO_PUPDR_PUPDR0 << (position * 2U));
 8001392:	ea06 0e05 	and.w	lr, r6, r5
        temp |= ((GPIO_Init->Pull) << (position * 2U));
 8001396:	f8d9 6008 	ldr.w	r6, [r9, #8]
 800139a:	4096      	lsls	r6, r2
 800139c:	ea46 060e 	orr.w	r6, r6, lr
      if((GPIO_Init->Mode & GPIO_MODE) == MODE_AF)
 80013a0:	f1bc 0f02 	cmp.w	ip, #2
        GPIOx->PUPDR = temp;
 80013a4:	60c6      	str	r6, [r0, #12]
      if((GPIO_Init->Mode & GPIO_MODE) == MODE_AF)
 80013a6:	f47f af64 	bne.w	8001272 <HAL_GPIO_Init+0x4e>
        temp = GPIOx->AFR[position >> 3U];
 80013aa:	08de      	lsrs	r6, r3, #3
 80013ac:	eb00 0686 	add.w	r6, r0, r6, lsl #2
 80013b0:	9601      	str	r6, [sp, #4]
 80013b2:	6a37      	ldr	r7, [r6, #32]
        temp |= ((uint32_t)(GPIO_Init->Alternate) << (((uint32_t)position & 0x07U) * 4U));
 80013b4:	f8d9 6010 	ldr.w	r6, [r9, #16]
        temp = GPIOx->AFR[position >> 3U];
 80013b8:	9700      	str	r7, [sp, #0]
        temp &= ~(0xFU << ((uint32_t)(position & 0x07U) * 4U)) ;
 80013ba:	f003 0e07 	and.w	lr, r3, #7
 80013be:	ea4f 0e8e 	mov.w	lr, lr, lsl #2
 80013c2:	270f      	movs	r7, #15
        temp |= ((uint32_t)(GPIO_Init->Alternate) << (((uint32_t)position & 0x07U) * 4U));
 80013c4:	fa06 f60e 	lsl.w	r6, r6, lr
        temp &= ~(0xFU << ((uint32_t)(position & 0x07U) * 4U)) ;
 80013c8:	fa07 fe0e 	lsl.w	lr, r7, lr
 80013cc:	9f00      	ldr	r7, [sp, #0]
 80013ce:	ea27 0e0e 	bic.w	lr, r7, lr
        GPIOx->AFR[position >> 3U] = temp;
 80013d2:	9f01      	ldr	r7, [sp, #4]
        temp |= ((uint32_t)(GPIO_Init->Alternate) << (((uint32_t)position & 0x07U) * 4U));
 80013d4:	ea46 060e 	orr.w	r6, r6, lr
        GPIOx->AFR[position >> 3U] = temp;
 80013d8:	623e      	str	r6, [r7, #32]
 80013da:	e74a      	b.n	8001272 <HAL_GPIO_Init+0x4e>
        temp |= ((uint32_t)(GPIO_GET_INDEX(GPIOx)) << (4U * (position & 0x03U)));
 80013dc:	f04f 0c01 	mov.w	ip, #1
 80013e0:	fa0c f606 	lsl.w	r6, ip, r6
 80013e4:	4335      	orrs	r5, r6
 80013e6:	e795      	b.n	8001314 <HAL_GPIO_Init+0xf0>
 80013e8:	f04f 0c02 	mov.w	ip, #2
 80013ec:	fa0c f606 	lsl.w	r6, ip, r6
 80013f0:	4335      	orrs	r5, r6
 80013f2:	e78f      	b.n	8001314 <HAL_GPIO_Init+0xf0>
 80013f4:	f04f 0c03 	mov.w	ip, #3
 80013f8:	fa0c f606 	lsl.w	r6, ip, r6
 80013fc:	4335      	orrs	r5, r6
 80013fe:	e789      	b.n	8001314 <HAL_GPIO_Init+0xf0>
 8001400:	f04f 0c04 	mov.w	ip, #4
 8001404:	fa0c f606 	lsl.w	r6, ip, r6
 8001408:	4335      	orrs	r5, r6
 800140a:	e783      	b.n	8001314 <HAL_GPIO_Init+0xf0>
 800140c:	f04f 0c05 	mov.w	ip, #5
 8001410:	fa0c f606 	lsl.w	r6, ip, r6
 8001414:	4335      	orrs	r5, r6
 8001416:	e77d      	b.n	8001314 <HAL_GPIO_Init+0xf0>
 8001418:	f04f 0c06 	mov.w	ip, #6
 800141c:	fa0c f606 	lsl.w	r6, ip, r6
 8001420:	4335      	orrs	r5, r6
 8001422:	e777      	b.n	8001314 <HAL_GPIO_Init+0xf0>
        temp = GPIOx->PUPDR;
 8001424:	68c6      	ldr	r6, [r0, #12]
        temp &= ~(GPIO_PUPDR_PUPDR0 << (position * 2U));
 8001426:	ea06 0e05 	and.w	lr, r6, r5
        temp |= ((GPIO_Init->Pull) << (position * 2U));
 800142a:	f8d9 6008 	ldr.w	r6, [r9, #8]
 800142e:	4096      	lsls	r6, r2
 8001430:	ea46 060e 	orr.w	r6, r6, lr
        GPIOx->PUPDR = temp;
 8001434:	60c6      	str	r6, [r0, #12]
      if((GPIO_Init->Mode & GPIO_MODE) == MODE_AF)
 8001436:	e71c      	b.n	8001272 <HAL_GPIO_Init+0x4e>
 8001438:	40020000 	.word	0x40020000
 800143c:	40020800 	.word	0x40020800
 8001440:	40013c00 	.word	0x40013c00
 8001444:	40023800 	.word	0x40023800
 8001448:	40020c00 	.word	0x40020c00
 800144c:	40021000 	.word	0x40021000
 8001450:	40021400 	.word	0x40021400
 8001454:	40021800 	.word	0x40021800
 8001458:	40021c00 	.word	0x40021c00

0800145c <HAL_GPIO_WritePin>:
{
  /* Check the parameters */
  assert_param(IS_GPIO_PIN(GPIO_Pin));
  assert_param(IS_GPIO_PIN_ACTION(PinState));

  if(PinState != GPIO_PIN_RESET)
 800145c:	b902      	cbnz	r2, 8001460 <HAL_GPIO_WritePin+0x4>
  {
    GPIOx->BSRR = GPIO_Pin;
  }
  else
  {
    GPIOx->BSRR = (uint32_t)GPIO_Pin << 16U;
 800145e:	0409      	lsls	r1, r1, #16
 8001460:	6181      	str	r1, [r0, #24]
  }
}
 8001462:	4770      	bx	lr

08001464 <HAL_RCC_OscConfig>:
__weak HAL_StatusTypeDef HAL_RCC_OscConfig(const RCC_OscInitTypeDef  *RCC_OscInitStruct)
{
  uint32_t tickstart;
  uint32_t pll_config;
  /* Check Null pointer */
  if (RCC_OscInitStruct == NULL)
 8001464:	2800      	cmp	r0, #0
 8001466:	f000 81d8 	beq.w	800181a <HAL_RCC_OscConfig+0x3b6>
{
 800146a:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
  }

  /* Check the parameters */
  assert_param(IS_RCC_OSCILLATORTYPE(RCC_OscInitStruct->OscillatorType));
  /*------------------------------- HSE Configuration ------------------------*/
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSE) == RCC_OSCILLATORTYPE_HSE)
 800146e:	6803      	ldr	r3, [r0, #0]
 8001470:	07dd      	lsls	r5, r3, #31
{
 8001472:	b082      	sub	sp, #8
 8001474:	4604      	mov	r4, r0
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSE) == RCC_OSCILLATORTYPE_HSE)
 8001476:	d52f      	bpl.n	80014d8 <HAL_RCC_OscConfig+0x74>
  {
    /* Check the parameters */
    assert_param(IS_RCC_HSE(RCC_OscInitStruct->HSEState));
    /* When the HSE is used as system clock or clock source for PLL in these cases HSE will not disabled */
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_CFGR_SWS_HSE) || \
 8001478:	499d      	ldr	r1, [pc, #628]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 800147a:	688a      	ldr	r2, [r1, #8]
 800147c:	f002 020c 	and.w	r2, r2, #12
 8001480:	2a04      	cmp	r2, #4
 8001482:	f000 80ec 	beq.w	800165e <HAL_RCC_OscConfig+0x1fa>
        ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_CFGR_SWS_PLL) && ((RCC->PLLCFGR & RCC_PLLCFGR_PLLSRC) == RCC_PLLCFGR_PLLSRC_HSE)))
 8001486:	688a      	ldr	r2, [r1, #8]
 8001488:	f002 020c 	and.w	r2, r2, #12
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_CFGR_SWS_HSE) || \
 800148c:	2a08      	cmp	r2, #8
 800148e:	f000 80e2 	beq.w	8001656 <HAL_RCC_OscConfig+0x1f2>
      }
    }
    else
    {
      /* Set the new HSE configuration ---------------------------------------*/
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 8001492:	6863      	ldr	r3, [r4, #4]
 8001494:	f5b3 3f80 	cmp.w	r3, #65536	@ 0x10000
 8001498:	f000 80eb 	beq.w	8001672 <HAL_RCC_OscConfig+0x20e>
 800149c:	f5b3 2fa0 	cmp.w	r3, #327680	@ 0x50000
 80014a0:	f000 8173 	beq.w	800178a <HAL_RCC_OscConfig+0x326>
 80014a4:	4d92      	ldr	r5, [pc, #584]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 80014a6:	682a      	ldr	r2, [r5, #0]
 80014a8:	f422 3280 	bic.w	r2, r2, #65536	@ 0x10000
 80014ac:	602a      	str	r2, [r5, #0]
 80014ae:	682a      	ldr	r2, [r5, #0]
 80014b0:	f422 2280 	bic.w	r2, r2, #262144	@ 0x40000
 80014b4:	602a      	str	r2, [r5, #0]

      /* Check the HSE State */
      if ((RCC_OscInitStruct->HSEState) != RCC_HSE_OFF)
 80014b6:	2b00      	cmp	r3, #0
 80014b8:	f040 80e0 	bne.w	800167c <HAL_RCC_OscConfig+0x218>
        }
      }
      else
      {
        /* Get Start Tick */
        tickstart = HAL_GetTick();
 80014bc:	f7ff fb78 	bl	8000bb0 <HAL_GetTick>
 80014c0:	4606      	mov	r6, r0

        /* Wait till HSE is bypassed or disabled */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET)
 80014c2:	e005      	b.n	80014d0 <HAL_RCC_OscConfig+0x6c>
        {
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 80014c4:	f7ff fb74 	bl	8000bb0 <HAL_GetTick>
 80014c8:	1b80      	subs	r0, r0, r6
 80014ca:	2864      	cmp	r0, #100	@ 0x64
 80014cc:	f200 8100 	bhi.w	80016d0 <HAL_RCC_OscConfig+0x26c>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET)
 80014d0:	682b      	ldr	r3, [r5, #0]
 80014d2:	039f      	lsls	r7, r3, #14
 80014d4:	d4f6      	bmi.n	80014c4 <HAL_RCC_OscConfig+0x60>
        }
      }
    }
  }
  /*----------------------------- HSI Configuration --------------------------*/
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_HSI) == RCC_OSCILLATORTYPE_HSI)
 80014d6:	6823      	ldr	r3, [r4, #0]
 80014d8:	079d      	lsls	r5, r3, #30
 80014da:	d528      	bpl.n	800152e <HAL_RCC_OscConfig+0xca>
    /* Check the parameters */
    assert_param(IS_RCC_HSI(RCC_OscInitStruct->HSIState));
    assert_param(IS_RCC_CALIBRATION_VALUE(RCC_OscInitStruct->HSICalibrationValue));

    /* Check if HSI is used as system clock or as PLL source when PLL is selected as system clock */
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_CFGR_SWS_HSI) || \
 80014dc:	4a84      	ldr	r2, [pc, #528]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 80014de:	6891      	ldr	r1, [r2, #8]
 80014e0:	f011 0f0c 	tst.w	r1, #12
 80014e4:	f000 809b 	beq.w	800161e <HAL_RCC_OscConfig+0x1ba>
        ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_CFGR_SWS_PLL) && ((RCC->PLLCFGR & RCC_PLLCFGR_PLLSRC) == RCC_PLLCFGR_PLLSRC_HSI)))
 80014e8:	6891      	ldr	r1, [r2, #8]
 80014ea:	f001 010c 	and.w	r1, r1, #12
    if ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_CFGR_SWS_HSI) || \
 80014ee:	2908      	cmp	r1, #8
 80014f0:	f000 8091 	beq.w	8001616 <HAL_RCC_OscConfig+0x1b2>
      }
    }
    else
    {
      /* Check the HSI State */
      if ((RCC_OscInitStruct->HSIState) != RCC_HSI_OFF)
 80014f4:	68e3      	ldr	r3, [r4, #12]
 80014f6:	2b00      	cmp	r3, #0
 80014f8:	f000 810c 	beq.w	8001714 <HAL_RCC_OscConfig+0x2b0>
      {
        /* Enable the Internal High Speed oscillator (HSI). */
        __HAL_RCC_HSI_ENABLE();
 80014fc:	4b7d      	ldr	r3, [pc, #500]	@ (80016f4 <HAL_RCC_OscConfig+0x290>)

        /* Get Start Tick*/
        tickstart = HAL_GetTick();

        /* Wait till HSI is ready */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 80014fe:	4e7c      	ldr	r6, [pc, #496]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
        __HAL_RCC_HSI_ENABLE();
 8001500:	2201      	movs	r2, #1
 8001502:	601a      	str	r2, [r3, #0]
        tickstart = HAL_GetTick();
 8001504:	f7ff fb54 	bl	8000bb0 <HAL_GetTick>
 8001508:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 800150a:	e005      	b.n	8001518 <HAL_RCC_OscConfig+0xb4>
        {
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 800150c:	f7ff fb50 	bl	8000bb0 <HAL_GetTick>
 8001510:	1b40      	subs	r0, r0, r5
 8001512:	2802      	cmp	r0, #2
 8001514:	f200 80dc 	bhi.w	80016d0 <HAL_RCC_OscConfig+0x26c>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 8001518:	6833      	ldr	r3, [r6, #0]
 800151a:	079f      	lsls	r7, r3, #30
 800151c:	d5f6      	bpl.n	800150c <HAL_RCC_OscConfig+0xa8>
            return HAL_TIMEOUT;
          }
        }

        /* Adjusts the Internal High Speed oscillator (HSI) calibration value. */
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 800151e:	6833      	ldr	r3, [r6, #0]
 8001520:	6922      	ldr	r2, [r4, #16]
 8001522:	f023 03f8 	bic.w	r3, r3, #248	@ 0xf8
 8001526:	ea43 03c2 	orr.w	r3, r3, r2, lsl #3
 800152a:	6033      	str	r3, [r6, #0]
        }
      }
    }
  }
  /*------------------------------ LSI Configuration -------------------------*/
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSI) == RCC_OSCILLATORTYPE_LSI)
 800152c:	6823      	ldr	r3, [r4, #0]
 800152e:	071a      	lsls	r2, r3, #28
 8001530:	d45c      	bmi.n	80015ec <HAL_RCC_OscConfig+0x188>
        }
      }
    }
  }
  /*------------------------------ LSE Configuration -------------------------*/
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSE) == RCC_OSCILLATORTYPE_LSE)
 8001532:	075d      	lsls	r5, r3, #29
 8001534:	d53a      	bpl.n	80015ac <HAL_RCC_OscConfig+0x148>
    /* Check the parameters */
    assert_param(IS_RCC_LSE(RCC_OscInitStruct->LSEState));

    /* Update LSE configuration in Backup Domain control register    */
    /* Requires to enable write access to Backup Domain of necessary */
    if (__HAL_RCC_PWR_IS_CLK_DISABLED())
 8001536:	4a6e      	ldr	r2, [pc, #440]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 8001538:	6c13      	ldr	r3, [r2, #64]	@ 0x40
 800153a:	f013 5380 	ands.w	r3, r3, #268435456	@ 0x10000000
 800153e:	f040 8088 	bne.w	8001652 <HAL_RCC_OscConfig+0x1ee>
    {
      __HAL_RCC_PWR_CLK_ENABLE();
 8001542:	9301      	str	r3, [sp, #4]
 8001544:	6c13      	ldr	r3, [r2, #64]	@ 0x40
 8001546:	f043 5380 	orr.w	r3, r3, #268435456	@ 0x10000000
 800154a:	6413      	str	r3, [r2, #64]	@ 0x40
 800154c:	6c13      	ldr	r3, [r2, #64]	@ 0x40
 800154e:	f003 5380 	and.w	r3, r3, #268435456	@ 0x10000000
 8001552:	9301      	str	r3, [sp, #4]
 8001554:	9b01      	ldr	r3, [sp, #4]
      pwrclkchanged = SET;
 8001556:	2501      	movs	r5, #1
    }

    if (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 8001558:	4e67      	ldr	r6, [pc, #412]	@ (80016f8 <HAL_RCC_OscConfig+0x294>)
 800155a:	6833      	ldr	r3, [r6, #0]
 800155c:	05d8      	lsls	r0, r3, #23
 800155e:	f140 80a7 	bpl.w	80016b0 <HAL_RCC_OscConfig+0x24c>
        }
      }
    }

    /* Set the new LSE configuration -----------------------------------------*/
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 8001562:	68a3      	ldr	r3, [r4, #8]
 8001564:	2b01      	cmp	r3, #1
 8001566:	f000 80b7 	beq.w	80016d8 <HAL_RCC_OscConfig+0x274>
 800156a:	2b05      	cmp	r3, #5
 800156c:	f000 811d 	beq.w	80017aa <HAL_RCC_OscConfig+0x346>
 8001570:	4e5f      	ldr	r6, [pc, #380]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 8001572:	6f32      	ldr	r2, [r6, #112]	@ 0x70
 8001574:	f022 0201 	bic.w	r2, r2, #1
 8001578:	6732      	str	r2, [r6, #112]	@ 0x70
 800157a:	6f32      	ldr	r2, [r6, #112]	@ 0x70
 800157c:	f022 0204 	bic.w	r2, r2, #4
 8001580:	6732      	str	r2, [r6, #112]	@ 0x70
    /* Check the LSE State */
    if ((RCC_OscInitStruct->LSEState) != RCC_LSE_OFF)
 8001582:	2b00      	cmp	r3, #0
 8001584:	f040 80ad 	bne.w	80016e2 <HAL_RCC_OscConfig+0x27e>
      }
    }
    else
    {
      /* Get Start Tick */
      tickstart = HAL_GetTick();
 8001588:	f7ff fb12 	bl	8000bb0 <HAL_GetTick>

      /* Wait till LSE is ready */
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
      {
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 800158c:	f241 3888 	movw	r8, #5000	@ 0x1388
      tickstart = HAL_GetTick();
 8001590:	4607      	mov	r7, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
 8001592:	e005      	b.n	80015a0 <HAL_RCC_OscConfig+0x13c>
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 8001594:	f7ff fb0c 	bl	8000bb0 <HAL_GetTick>
 8001598:	1bc0      	subs	r0, r0, r7
 800159a:	4540      	cmp	r0, r8
 800159c:	f200 8098 	bhi.w	80016d0 <HAL_RCC_OscConfig+0x26c>
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) != RESET)
 80015a0:	6f33      	ldr	r3, [r6, #112]	@ 0x70
 80015a2:	079b      	lsls	r3, r3, #30
 80015a4:	d4f6      	bmi.n	8001594 <HAL_RCC_OscConfig+0x130>
        }
      }
    }

    /* Restore clock configuration if changed */
    if (pwrclkchanged == SET)
 80015a6:	2d00      	cmp	r5, #0
 80015a8:	f040 80f9 	bne.w	800179e <HAL_RCC_OscConfig+0x33a>
    }
  }
  /*-------------------------------- PLL Configuration -----------------------*/
  /* Check the parameters */
  assert_param(IS_RCC_PLL(RCC_OscInitStruct->PLL.PLLState));
  if ((RCC_OscInitStruct->PLL.PLLState) != RCC_PLL_NONE)
 80015ac:	69a3      	ldr	r3, [r4, #24]
 80015ae:	b1cb      	cbz	r3, 80015e4 <HAL_RCC_OscConfig+0x180>
  {
    /* Check if the PLL is used as system clock or not */
    if (__HAL_RCC_GET_SYSCLK_SOURCE() != RCC_CFGR_SWS_PLL)
 80015b0:	4d4f      	ldr	r5, [pc, #316]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 80015b2:	68aa      	ldr	r2, [r5, #8]
 80015b4:	f002 020c 	and.w	r2, r2, #12
 80015b8:	2a08      	cmp	r2, #8
 80015ba:	f000 80bc 	beq.w	8001736 <HAL_RCC_OscConfig+0x2d2>
    {
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_ON)
 80015be:	2b02      	cmp	r3, #2
        assert_param(IS_RCC_PLLN_VALUE(RCC_OscInitStruct->PLL.PLLN));
        assert_param(IS_RCC_PLLP_VALUE(RCC_OscInitStruct->PLL.PLLP));
        assert_param(IS_RCC_PLLQ_VALUE(RCC_OscInitStruct->PLL.PLLQ));

        /* Disable the main PLL. */
        __HAL_RCC_PLL_DISABLE();
 80015c0:	4b4c      	ldr	r3, [pc, #304]	@ (80016f4 <HAL_RCC_OscConfig+0x290>)
 80015c2:	f04f 0200 	mov.w	r2, #0
 80015c6:	661a      	str	r2, [r3, #96]	@ 0x60
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_ON)
 80015c8:	f000 80f9 	beq.w	80017be <HAL_RCC_OscConfig+0x35a>
      {
        /* Disable the main PLL. */
        __HAL_RCC_PLL_DISABLE();

        /* Get Start Tick */
        tickstart = HAL_GetTick();
 80015cc:	f7ff faf0 	bl	8000bb0 <HAL_GetTick>
 80015d0:	4604      	mov	r4, r0

        /* Wait till PLL is disabled */
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) != RESET)
 80015d2:	e004      	b.n	80015de <HAL_RCC_OscConfig+0x17a>
        {
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 80015d4:	f7ff faec 	bl	8000bb0 <HAL_GetTick>
 80015d8:	1b00      	subs	r0, r0, r4
 80015da:	2802      	cmp	r0, #2
 80015dc:	d878      	bhi.n	80016d0 <HAL_RCC_OscConfig+0x26c>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) != RESET)
 80015de:	682b      	ldr	r3, [r5, #0]
 80015e0:	019b      	lsls	r3, r3, #6
 80015e2:	d4f7      	bmi.n	80015d4 <HAL_RCC_OscConfig+0x170>
          return HAL_ERROR;
        }
      }
    }
  }
  return HAL_OK;
 80015e4:	2000      	movs	r0, #0
}
 80015e6:	b002      	add	sp, #8
 80015e8:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
    if ((RCC_OscInitStruct->LSIState) != RCC_LSI_OFF)
 80015ec:	6963      	ldr	r3, [r4, #20]
 80015ee:	b1fb      	cbz	r3, 8001630 <HAL_RCC_OscConfig+0x1cc>
      __HAL_RCC_LSI_ENABLE();
 80015f0:	4b40      	ldr	r3, [pc, #256]	@ (80016f4 <HAL_RCC_OscConfig+0x290>)
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 80015f2:	4e3f      	ldr	r6, [pc, #252]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
      __HAL_RCC_LSI_ENABLE();
 80015f4:	2201      	movs	r2, #1
 80015f6:	f8c3 2e80 	str.w	r2, [r3, #3712]	@ 0xe80
      tickstart = HAL_GetTick();
 80015fa:	f7ff fad9 	bl	8000bb0 <HAL_GetTick>
 80015fe:	4605      	mov	r5, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 8001600:	e004      	b.n	800160c <HAL_RCC_OscConfig+0x1a8>
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 8001602:	f7ff fad5 	bl	8000bb0 <HAL_GetTick>
 8001606:	1b40      	subs	r0, r0, r5
 8001608:	2802      	cmp	r0, #2
 800160a:	d861      	bhi.n	80016d0 <HAL_RCC_OscConfig+0x26c>
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) == RESET)
 800160c:	6f73      	ldr	r3, [r6, #116]	@ 0x74
 800160e:	079b      	lsls	r3, r3, #30
 8001610:	d5f7      	bpl.n	8001602 <HAL_RCC_OscConfig+0x19e>
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSE) == RCC_OSCILLATORTYPE_LSE)
 8001612:	6823      	ldr	r3, [r4, #0]
 8001614:	e78d      	b.n	8001532 <HAL_RCC_OscConfig+0xce>
        ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_CFGR_SWS_PLL) && ((RCC->PLLCFGR & RCC_PLLCFGR_PLLSRC) == RCC_PLLCFGR_PLLSRC_HSI)))
 8001616:	6852      	ldr	r2, [r2, #4]
 8001618:	0251      	lsls	r1, r2, #9
 800161a:	f53f af6b 	bmi.w	80014f4 <HAL_RCC_OscConfig+0x90>
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET) && (RCC_OscInitStruct->HSIState != RCC_HSI_ON))
 800161e:	4a34      	ldr	r2, [pc, #208]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 8001620:	6812      	ldr	r2, [r2, #0]
 8001622:	0792      	lsls	r2, r2, #30
 8001624:	d538      	bpl.n	8001698 <HAL_RCC_OscConfig+0x234>
 8001626:	68e2      	ldr	r2, [r4, #12]
 8001628:	2a01      	cmp	r2, #1
 800162a:	d035      	beq.n	8001698 <HAL_RCC_OscConfig+0x234>
    return HAL_ERROR;
 800162c:	2001      	movs	r0, #1
 800162e:	e7da      	b.n	80015e6 <HAL_RCC_OscConfig+0x182>
      __HAL_RCC_LSI_DISABLE();
 8001630:	4a30      	ldr	r2, [pc, #192]	@ (80016f4 <HAL_RCC_OscConfig+0x290>)
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 8001632:	4e2f      	ldr	r6, [pc, #188]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
      __HAL_RCC_LSI_DISABLE();
 8001634:	f8c2 3e80 	str.w	r3, [r2, #3712]	@ 0xe80
      tickstart = HAL_GetTick();
 8001638:	f7ff faba 	bl	8000bb0 <HAL_GetTick>
 800163c:	4605      	mov	r5, r0
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 800163e:	e004      	b.n	800164a <HAL_RCC_OscConfig+0x1e6>
        if ((HAL_GetTick() - tickstart) > LSI_TIMEOUT_VALUE)
 8001640:	f7ff fab6 	bl	8000bb0 <HAL_GetTick>
 8001644:	1b40      	subs	r0, r0, r5
 8001646:	2802      	cmp	r0, #2
 8001648:	d842      	bhi.n	80016d0 <HAL_RCC_OscConfig+0x26c>
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSIRDY) != RESET)
 800164a:	6f73      	ldr	r3, [r6, #116]	@ 0x74
 800164c:	079f      	lsls	r7, r3, #30
 800164e:	d4f7      	bmi.n	8001640 <HAL_RCC_OscConfig+0x1dc>
 8001650:	e7df      	b.n	8001612 <HAL_RCC_OscConfig+0x1ae>
    FlagStatus       pwrclkchanged = RESET;
 8001652:	2500      	movs	r5, #0
 8001654:	e780      	b.n	8001558 <HAL_RCC_OscConfig+0xf4>
        ((__HAL_RCC_GET_SYSCLK_SOURCE() == RCC_CFGR_SWS_PLL) && ((RCC->PLLCFGR & RCC_PLLCFGR_PLLSRC) == RCC_PLLCFGR_PLLSRC_HSE)))
 8001656:	684a      	ldr	r2, [r1, #4]
 8001658:	0251      	lsls	r1, r2, #9
 800165a:	f57f af1a 	bpl.w	8001492 <HAL_RCC_OscConfig+0x2e>
      if ((__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) != RESET) && (RCC_OscInitStruct->HSEState == RCC_HSE_OFF))
 800165e:	4a24      	ldr	r2, [pc, #144]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 8001660:	6812      	ldr	r2, [r2, #0]
 8001662:	0392      	lsls	r2, r2, #14
 8001664:	f57f af38 	bpl.w	80014d8 <HAL_RCC_OscConfig+0x74>
 8001668:	6862      	ldr	r2, [r4, #4]
 800166a:	2a00      	cmp	r2, #0
 800166c:	f47f af34 	bne.w	80014d8 <HAL_RCC_OscConfig+0x74>
 8001670:	e7dc      	b.n	800162c <HAL_RCC_OscConfig+0x1c8>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 8001672:	4a1f      	ldr	r2, [pc, #124]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 8001674:	6813      	ldr	r3, [r2, #0]
 8001676:	f443 3380 	orr.w	r3, r3, #65536	@ 0x10000
 800167a:	6013      	str	r3, [r2, #0]
        tickstart = HAL_GetTick();
 800167c:	f7ff fa98 	bl	8000bb0 <HAL_GetTick>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8001680:	4e1b      	ldr	r6, [pc, #108]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
        tickstart = HAL_GetTick();
 8001682:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8001684:	e004      	b.n	8001690 <HAL_RCC_OscConfig+0x22c>
          if ((HAL_GetTick() - tickstart) > HSE_TIMEOUT_VALUE)
 8001686:	f7ff fa93 	bl	8000bb0 <HAL_GetTick>
 800168a:	1b40      	subs	r0, r0, r5
 800168c:	2864      	cmp	r0, #100	@ 0x64
 800168e:	d81f      	bhi.n	80016d0 <HAL_RCC_OscConfig+0x26c>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 8001690:	6833      	ldr	r3, [r6, #0]
 8001692:	039b      	lsls	r3, r3, #14
 8001694:	d5f7      	bpl.n	8001686 <HAL_RCC_OscConfig+0x222>
 8001696:	e71e      	b.n	80014d6 <HAL_RCC_OscConfig+0x72>
        __HAL_RCC_HSI_CALIBRATIONVALUE_ADJUST(RCC_OscInitStruct->HSICalibrationValue);
 8001698:	4915      	ldr	r1, [pc, #84]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 800169a:	6920      	ldr	r0, [r4, #16]
 800169c:	680a      	ldr	r2, [r1, #0]
 800169e:	f022 02f8 	bic.w	r2, r2, #248	@ 0xf8
 80016a2:	ea42 02c0 	orr.w	r2, r2, r0, lsl #3
 80016a6:	600a      	str	r2, [r1, #0]
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSI) == RCC_OSCILLATORTYPE_LSI)
 80016a8:	071a      	lsls	r2, r3, #28
 80016aa:	f57f af42 	bpl.w	8001532 <HAL_RCC_OscConfig+0xce>
 80016ae:	e79d      	b.n	80015ec <HAL_RCC_OscConfig+0x188>
      SET_BIT(PWR->CR, PWR_CR_DBP);
 80016b0:	6833      	ldr	r3, [r6, #0]
 80016b2:	f443 7380 	orr.w	r3, r3, #256	@ 0x100
 80016b6:	6033      	str	r3, [r6, #0]
      tickstart = HAL_GetTick();
 80016b8:	f7ff fa7a 	bl	8000bb0 <HAL_GetTick>
 80016bc:	4607      	mov	r7, r0
      while (HAL_IS_BIT_CLR(PWR->CR, PWR_CR_DBP))
 80016be:	6833      	ldr	r3, [r6, #0]
 80016c0:	05d9      	lsls	r1, r3, #23
 80016c2:	f53f af4e 	bmi.w	8001562 <HAL_RCC_OscConfig+0xfe>
        if ((HAL_GetTick() - tickstart) > RCC_DBP_TIMEOUT_VALUE)
 80016c6:	f7ff fa73 	bl	8000bb0 <HAL_GetTick>
 80016ca:	1bc0      	subs	r0, r0, r7
 80016cc:	2802      	cmp	r0, #2
 80016ce:	d9f6      	bls.n	80016be <HAL_RCC_OscConfig+0x25a>
            return HAL_TIMEOUT;
 80016d0:	2003      	movs	r0, #3
}
 80016d2:	b002      	add	sp, #8
 80016d4:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 80016d8:	4a05      	ldr	r2, [pc, #20]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
 80016da:	6f13      	ldr	r3, [r2, #112]	@ 0x70
 80016dc:	f043 0301 	orr.w	r3, r3, #1
 80016e0:	6713      	str	r3, [r2, #112]	@ 0x70
      tickstart = HAL_GetTick();
 80016e2:	f7ff fa65 	bl	8000bb0 <HAL_GetTick>
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 80016e6:	4f02      	ldr	r7, [pc, #8]	@ (80016f0 <HAL_RCC_OscConfig+0x28c>)
      tickstart = HAL_GetTick();
 80016e8:	4606      	mov	r6, r0
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 80016ea:	f241 3888 	movw	r8, #5000	@ 0x1388
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 80016ee:	e00a      	b.n	8001706 <HAL_RCC_OscConfig+0x2a2>
 80016f0:	40023800 	.word	0x40023800
 80016f4:	42470000 	.word	0x42470000
 80016f8:	40007000 	.word	0x40007000
        if ((HAL_GetTick() - tickstart) > RCC_LSE_TIMEOUT_VALUE)
 80016fc:	f7ff fa58 	bl	8000bb0 <HAL_GetTick>
 8001700:	1b80      	subs	r0, r0, r6
 8001702:	4540      	cmp	r0, r8
 8001704:	d8e4      	bhi.n	80016d0 <HAL_RCC_OscConfig+0x26c>
      while (__HAL_RCC_GET_FLAG(RCC_FLAG_LSERDY) == RESET)
 8001706:	6f3b      	ldr	r3, [r7, #112]	@ 0x70
 8001708:	079a      	lsls	r2, r3, #30
 800170a:	d5f7      	bpl.n	80016fc <HAL_RCC_OscConfig+0x298>
    if (pwrclkchanged == SET)
 800170c:	2d00      	cmp	r5, #0
 800170e:	f43f af4d 	beq.w	80015ac <HAL_RCC_OscConfig+0x148>
 8001712:	e044      	b.n	800179e <HAL_RCC_OscConfig+0x33a>
        __HAL_RCC_HSI_DISABLE();
 8001714:	4a42      	ldr	r2, [pc, #264]	@ (8001820 <HAL_RCC_OscConfig+0x3bc>)
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 8001716:	4e43      	ldr	r6, [pc, #268]	@ (8001824 <HAL_RCC_OscConfig+0x3c0>)
        __HAL_RCC_HSI_DISABLE();
 8001718:	6013      	str	r3, [r2, #0]
        tickstart = HAL_GetTick();
 800171a:	f7ff fa49 	bl	8000bb0 <HAL_GetTick>
 800171e:	4605      	mov	r5, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 8001720:	e004      	b.n	800172c <HAL_RCC_OscConfig+0x2c8>
          if ((HAL_GetTick() - tickstart) > HSI_TIMEOUT_VALUE)
 8001722:	f7ff fa45 	bl	8000bb0 <HAL_GetTick>
 8001726:	1b40      	subs	r0, r0, r5
 8001728:	2802      	cmp	r0, #2
 800172a:	d8d1      	bhi.n	80016d0 <HAL_RCC_OscConfig+0x26c>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) != RESET)
 800172c:	6833      	ldr	r3, [r6, #0]
 800172e:	0799      	lsls	r1, r3, #30
 8001730:	d4f7      	bmi.n	8001722 <HAL_RCC_OscConfig+0x2be>
  if (((RCC_OscInitStruct->OscillatorType) & RCC_OSCILLATORTYPE_LSI) == RCC_OSCILLATORTYPE_LSI)
 8001732:	6823      	ldr	r3, [r4, #0]
 8001734:	e6fb      	b.n	800152e <HAL_RCC_OscConfig+0xca>
      if ((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF)
 8001736:	2b01      	cmp	r3, #1
 8001738:	f43f af78 	beq.w	800162c <HAL_RCC_OscConfig+0x1c8>
        pll_config = RCC->PLLCFGR;
 800173c:	686b      	ldr	r3, [r5, #4]
        if (((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF) ||
 800173e:	69e2      	ldr	r2, [r4, #28]
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 8001740:	f403 0180 	and.w	r1, r3, #4194304	@ 0x400000
        if (((RCC_OscInitStruct->PLL.PLLState) == RCC_PLL_OFF) ||
 8001744:	4291      	cmp	r1, r2
 8001746:	f47f af71 	bne.w	800162c <HAL_RCC_OscConfig+0x1c8>
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 800174a:	6a22      	ldr	r2, [r4, #32]
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLM) != (RCC_OscInitStruct->PLL.PLLM) << RCC_PLLCFGR_PLLM_Pos) ||
 800174c:	f003 013f 	and.w	r1, r3, #63	@ 0x3f
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLSRC) != RCC_OscInitStruct->PLL.PLLSource) ||
 8001750:	4291      	cmp	r1, r2
 8001752:	f47f af6b 	bne.w	800162c <HAL_RCC_OscConfig+0x1c8>
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLN) != (RCC_OscInitStruct->PLL.PLLN) << RCC_PLLCFGR_PLLN_Pos) ||
 8001756:	6a61      	ldr	r1, [r4, #36]	@ 0x24
 8001758:	f647 72c0 	movw	r2, #32704	@ 0x7fc0
 800175c:	401a      	ands	r2, r3
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLM) != (RCC_OscInitStruct->PLL.PLLM) << RCC_PLLCFGR_PLLM_Pos) ||
 800175e:	ebb2 1f81 	cmp.w	r2, r1, lsl #6
 8001762:	f47f af63 	bne.w	800162c <HAL_RCC_OscConfig+0x1c8>
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLP) != (((RCC_OscInitStruct->PLL.PLLP >> 1U) - 1U)) << RCC_PLLCFGR_PLLP_Pos) ||
 8001766:	6aa2      	ldr	r2, [r4, #40]	@ 0x28
 8001768:	0852      	lsrs	r2, r2, #1
 800176a:	f403 3140 	and.w	r1, r3, #196608	@ 0x30000
 800176e:	3a01      	subs	r2, #1
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLN) != (RCC_OscInitStruct->PLL.PLLN) << RCC_PLLCFGR_PLLN_Pos) ||
 8001770:	ebb1 4f02 	cmp.w	r1, r2, lsl #16
 8001774:	f47f af5a 	bne.w	800162c <HAL_RCC_OscConfig+0x1c8>
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLQ) != (RCC_OscInitStruct->PLL.PLLQ << RCC_PLLCFGR_PLLQ_Pos)))
 8001778:	6ae2      	ldr	r2, [r4, #44]	@ 0x2c
 800177a:	f003 6370 	and.w	r3, r3, #251658240	@ 0xf000000
            (READ_BIT(pll_config, RCC_PLLCFGR_PLLP) != (((RCC_OscInitStruct->PLL.PLLP >> 1U) - 1U)) << RCC_PLLCFGR_PLLP_Pos) ||
 800177e:	ebb3 6f02 	cmp.w	r3, r2, lsl #24
 8001782:	bf14      	ite	ne
 8001784:	2001      	movne	r0, #1
 8001786:	2000      	moveq	r0, #0
 8001788:	e72d      	b.n	80015e6 <HAL_RCC_OscConfig+0x182>
      __HAL_RCC_HSE_CONFIG(RCC_OscInitStruct->HSEState);
 800178a:	4b26      	ldr	r3, [pc, #152]	@ (8001824 <HAL_RCC_OscConfig+0x3c0>)
 800178c:	681a      	ldr	r2, [r3, #0]
 800178e:	f442 2280 	orr.w	r2, r2, #262144	@ 0x40000
 8001792:	601a      	str	r2, [r3, #0]
 8001794:	681a      	ldr	r2, [r3, #0]
 8001796:	f442 3280 	orr.w	r2, r2, #65536	@ 0x10000
 800179a:	601a      	str	r2, [r3, #0]
      if ((RCC_OscInitStruct->HSEState) != RCC_HSE_OFF)
 800179c:	e76e      	b.n	800167c <HAL_RCC_OscConfig+0x218>
      __HAL_RCC_PWR_CLK_DISABLE();
 800179e:	4a21      	ldr	r2, [pc, #132]	@ (8001824 <HAL_RCC_OscConfig+0x3c0>)
 80017a0:	6c13      	ldr	r3, [r2, #64]	@ 0x40
 80017a2:	f023 5380 	bic.w	r3, r3, #268435456	@ 0x10000000
 80017a6:	6413      	str	r3, [r2, #64]	@ 0x40
 80017a8:	e700      	b.n	80015ac <HAL_RCC_OscConfig+0x148>
    __HAL_RCC_LSE_CONFIG(RCC_OscInitStruct->LSEState);
 80017aa:	4b1e      	ldr	r3, [pc, #120]	@ (8001824 <HAL_RCC_OscConfig+0x3c0>)
 80017ac:	6f1a      	ldr	r2, [r3, #112]	@ 0x70
 80017ae:	f042 0204 	orr.w	r2, r2, #4
 80017b2:	671a      	str	r2, [r3, #112]	@ 0x70
 80017b4:	6f1a      	ldr	r2, [r3, #112]	@ 0x70
 80017b6:	f042 0201 	orr.w	r2, r2, #1
 80017ba:	671a      	str	r2, [r3, #112]	@ 0x70
    if ((RCC_OscInitStruct->LSEState) != RCC_LSE_OFF)
 80017bc:	e791      	b.n	80016e2 <HAL_RCC_OscConfig+0x27e>
        tickstart = HAL_GetTick();
 80017be:	f7ff f9f7 	bl	8000bb0 <HAL_GetTick>
 80017c2:	4606      	mov	r6, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) != RESET)
 80017c4:	e005      	b.n	80017d2 <HAL_RCC_OscConfig+0x36e>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 80017c6:	f7ff f9f3 	bl	8000bb0 <HAL_GetTick>
 80017ca:	1b80      	subs	r0, r0, r6
 80017cc:	2802      	cmp	r0, #2
 80017ce:	f63f af7f 	bhi.w	80016d0 <HAL_RCC_OscConfig+0x26c>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) != RESET)
 80017d2:	682b      	ldr	r3, [r5, #0]
 80017d4:	0199      	lsls	r1, r3, #6
 80017d6:	d4f6      	bmi.n	80017c6 <HAL_RCC_OscConfig+0x362>
        WRITE_REG(RCC->PLLCFGR, (RCC_OscInitStruct->PLL.PLLSource                                            | \
 80017d8:	e9d4 3107 	ldrd	r3, r1, [r4, #28]
 80017dc:	6a62      	ldr	r2, [r4, #36]	@ 0x24
 80017de:	430b      	orrs	r3, r1
 80017e0:	ea43 1382 	orr.w	r3, r3, r2, lsl #6
 80017e4:	e9d4 200a 	ldrd	r2, r0, [r4, #40]	@ 0x28
 80017e8:	0852      	lsrs	r2, r2, #1
        __HAL_RCC_PLL_ENABLE();
 80017ea:	490d      	ldr	r1, [pc, #52]	@ (8001820 <HAL_RCC_OscConfig+0x3bc>)
        WRITE_REG(RCC->PLLCFGR, (RCC_OscInitStruct->PLL.PLLSource                                            | \
 80017ec:	ea43 6300 	orr.w	r3, r3, r0, lsl #24
 80017f0:	3a01      	subs	r2, #1
 80017f2:	ea43 4302 	orr.w	r3, r3, r2, lsl #16
        __HAL_RCC_PLL_ENABLE();
 80017f6:	2201      	movs	r2, #1
        WRITE_REG(RCC->PLLCFGR, (RCC_OscInitStruct->PLL.PLLSource                                            | \
 80017f8:	606b      	str	r3, [r5, #4]
        __HAL_RCC_PLL_ENABLE();
 80017fa:	660a      	str	r2, [r1, #96]	@ 0x60
        tickstart = HAL_GetTick();
 80017fc:	f7ff f9d8 	bl	8000bb0 <HAL_GetTick>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 8001800:	4d08      	ldr	r5, [pc, #32]	@ (8001824 <HAL_RCC_OscConfig+0x3c0>)
        tickstart = HAL_GetTick();
 8001802:	4604      	mov	r4, r0
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 8001804:	e005      	b.n	8001812 <HAL_RCC_OscConfig+0x3ae>
          if ((HAL_GetTick() - tickstart) > PLL_TIMEOUT_VALUE)
 8001806:	f7ff f9d3 	bl	8000bb0 <HAL_GetTick>
 800180a:	1b00      	subs	r0, r0, r4
 800180c:	2802      	cmp	r0, #2
 800180e:	f63f af5f 	bhi.w	80016d0 <HAL_RCC_OscConfig+0x26c>
        while (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 8001812:	682b      	ldr	r3, [r5, #0]
 8001814:	019a      	lsls	r2, r3, #6
 8001816:	d5f6      	bpl.n	8001806 <HAL_RCC_OscConfig+0x3a2>
 8001818:	e6e4      	b.n	80015e4 <HAL_RCC_OscConfig+0x180>
    return HAL_ERROR;
 800181a:	2001      	movs	r0, #1
}
 800181c:	4770      	bx	lr
 800181e:	bf00      	nop
 8001820:	42470000 	.word	0x42470000
 8001824:	40023800 	.word	0x40023800

08001828 <HAL_RCC_GetSysClockFreq>:
  uint32_t pllvco = 0U;
  uint32_t pllp = 0U;
  uint32_t sysclockfreq = 0U;

  /* Get SYSCLK source -------------------------------------------------------*/
  switch (RCC->CFGR & RCC_CFGR_SWS)
 8001828:	4916      	ldr	r1, [pc, #88]	@ (8001884 <HAL_RCC_GetSysClockFreq+0x5c>)
{
 800182a:	b508      	push	{r3, lr}
  switch (RCC->CFGR & RCC_CFGR_SWS)
 800182c:	688b      	ldr	r3, [r1, #8]
 800182e:	f003 030c 	and.w	r3, r3, #12
 8001832:	2b04      	cmp	r3, #4
 8001834:	d01b      	beq.n	800186e <HAL_RCC_GetSysClockFreq+0x46>
 8001836:	2b08      	cmp	r3, #8
 8001838:	d001      	beq.n	800183e <HAL_RCC_GetSysClockFreq+0x16>
  {
    case RCC_CFGR_SWS_HSI:  /* HSI used as system clock source */
    {
      sysclockfreq = HSI_VALUE;
 800183a:	4813      	ldr	r0, [pc, #76]	@ (8001888 <HAL_RCC_GetSysClockFreq+0x60>)
      sysclockfreq = HSI_VALUE;
      break;
    }
  }
  return sysclockfreq;
}
 800183c:	bd08      	pop	{r3, pc}
      pllm = RCC->PLLCFGR & RCC_PLLCFGR_PLLM;
 800183e:	684a      	ldr	r2, [r1, #4]
      if (__HAL_RCC_GET_PLL_OSCSOURCE() != RCC_PLLSOURCE_HSI)
 8001840:	684b      	ldr	r3, [r1, #4]
        pllvco = (uint32_t)((((uint64_t) HSE_VALUE * ((uint64_t)((RCC->PLLCFGR & RCC_PLLCFGR_PLLN) >> RCC_PLLCFGR_PLLN_Pos)))) / (uint64_t)pllm);
 8001842:	6849      	ldr	r1, [r1, #4]
      if (__HAL_RCC_GET_PLL_OSCSOURCE() != RCC_PLLSOURCE_HSI)
 8001844:	f413 0380 	ands.w	r3, r3, #4194304	@ 0x400000
      pllm = RCC->PLLCFGR & RCC_PLLCFGR_PLLM;
 8001848:	f002 023f 	and.w	r2, r2, #63	@ 0x3f
      if (__HAL_RCC_GET_PLL_OSCSOURCE() != RCC_PLLSOURCE_HSI)
 800184c:	d111      	bne.n	8001872 <HAL_RCC_GetSysClockFreq+0x4a>
        pllvco = (uint32_t)((((uint64_t) HSI_VALUE * ((uint64_t)((RCC->PLLCFGR & RCC_PLLCFGR_PLLN) >> RCC_PLLCFGR_PLLN_Pos)))) / (uint64_t)pllm);
 800184e:	480e      	ldr	r0, [pc, #56]	@ (8001888 <HAL_RCC_GetSysClockFreq+0x60>)
 8001850:	f3c1 1188 	ubfx	r1, r1, #6, #9
 8001854:	fba1 0100 	umull	r0, r1, r1, r0
 8001858:	f7fe fcba 	bl	80001d0 <__aeabi_uldivmod>
      pllp = ((((RCC->PLLCFGR & RCC_PLLCFGR_PLLP) >> RCC_PLLCFGR_PLLP_Pos) + 1U) * 2U);
 800185c:	4b09      	ldr	r3, [pc, #36]	@ (8001884 <HAL_RCC_GetSysClockFreq+0x5c>)
 800185e:	685b      	ldr	r3, [r3, #4]
 8001860:	f3c3 4301 	ubfx	r3, r3, #16, #2
 8001864:	3301      	adds	r3, #1
 8001866:	005b      	lsls	r3, r3, #1
      sysclockfreq = pllvco / pllp;
 8001868:	fbb0 f0f3 	udiv	r0, r0, r3
}
 800186c:	bd08      	pop	{r3, pc}
  switch (RCC->CFGR & RCC_CFGR_SWS)
 800186e:	4807      	ldr	r0, [pc, #28]	@ (800188c <HAL_RCC_GetSysClockFreq+0x64>)
}
 8001870:	bd08      	pop	{r3, pc}
        pllvco = (uint32_t)((((uint64_t) HSE_VALUE * ((uint64_t)((RCC->PLLCFGR & RCC_PLLCFGR_PLLN) >> RCC_PLLCFGR_PLLN_Pos)))) / (uint64_t)pllm);
 8001872:	4806      	ldr	r0, [pc, #24]	@ (800188c <HAL_RCC_GetSysClockFreq+0x64>)
 8001874:	f3c1 1188 	ubfx	r1, r1, #6, #9
 8001878:	2300      	movs	r3, #0
 800187a:	fba1 0100 	umull	r0, r1, r1, r0
 800187e:	f7fe fca7 	bl	80001d0 <__aeabi_uldivmod>
 8001882:	e7eb      	b.n	800185c <HAL_RCC_GetSysClockFreq+0x34>
 8001884:	40023800 	.word	0x40023800
 8001888:	00f42400 	.word	0x00f42400
 800188c:	017d7840 	.word	0x017d7840

08001890 <HAL_RCC_ClockConfig>:
  if (RCC_ClkInitStruct == NULL)
 8001890:	2800      	cmp	r0, #0
 8001892:	f000 8087 	beq.w	80019a4 <HAL_RCC_ClockConfig+0x114>
  if (FLatency > __HAL_FLASH_GET_LATENCY())
 8001896:	4a48      	ldr	r2, [pc, #288]	@ (80019b8 <HAL_RCC_ClockConfig+0x128>)
 8001898:	6813      	ldr	r3, [r2, #0]
 800189a:	f003 0307 	and.w	r3, r3, #7
 800189e:	428b      	cmp	r3, r1
{
 80018a0:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 80018a4:	460d      	mov	r5, r1
 80018a6:	4604      	mov	r4, r0
  if (FLatency > __HAL_FLASH_GET_LATENCY())
 80018a8:	d209      	bcs.n	80018be <HAL_RCC_ClockConfig+0x2e>
    __HAL_FLASH_SET_LATENCY(FLatency);
 80018aa:	b2cb      	uxtb	r3, r1
 80018ac:	7013      	strb	r3, [r2, #0]
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 80018ae:	6813      	ldr	r3, [r2, #0]
 80018b0:	f003 0307 	and.w	r3, r3, #7
 80018b4:	428b      	cmp	r3, r1
 80018b6:	d002      	beq.n	80018be <HAL_RCC_ClockConfig+0x2e>
    return HAL_ERROR;
 80018b8:	2001      	movs	r0, #1
}
 80018ba:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_HCLK) == RCC_CLOCKTYPE_HCLK)
 80018be:	6823      	ldr	r3, [r4, #0]
 80018c0:	0798      	lsls	r0, r3, #30
 80018c2:	d514      	bpl.n	80018ee <HAL_RCC_ClockConfig+0x5e>
    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 80018c4:	0759      	lsls	r1, r3, #29
 80018c6:	d504      	bpl.n	80018d2 <HAL_RCC_ClockConfig+0x42>
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_HCLK_DIV16);
 80018c8:	493c      	ldr	r1, [pc, #240]	@ (80019bc <HAL_RCC_ClockConfig+0x12c>)
 80018ca:	688a      	ldr	r2, [r1, #8]
 80018cc:	f442 52e0 	orr.w	r2, r2, #7168	@ 0x1c00
 80018d0:	608a      	str	r2, [r1, #8]
    if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 80018d2:	071a      	lsls	r2, r3, #28
 80018d4:	d504      	bpl.n	80018e0 <HAL_RCC_ClockConfig+0x50>
      MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, (RCC_HCLK_DIV16 << 3));
 80018d6:	4939      	ldr	r1, [pc, #228]	@ (80019bc <HAL_RCC_ClockConfig+0x12c>)
 80018d8:	688a      	ldr	r2, [r1, #8]
 80018da:	f442 4260 	orr.w	r2, r2, #57344	@ 0xe000
 80018de:	608a      	str	r2, [r1, #8]
    MODIFY_REG(RCC->CFGR, RCC_CFGR_HPRE, RCC_ClkInitStruct->AHBCLKDivider);
 80018e0:	4936      	ldr	r1, [pc, #216]	@ (80019bc <HAL_RCC_ClockConfig+0x12c>)
 80018e2:	68a0      	ldr	r0, [r4, #8]
 80018e4:	688a      	ldr	r2, [r1, #8]
 80018e6:	f022 02f0 	bic.w	r2, r2, #240	@ 0xf0
 80018ea:	4302      	orrs	r2, r0
 80018ec:	608a      	str	r2, [r1, #8]
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_SYSCLK) == RCC_CLOCKTYPE_SYSCLK)
 80018ee:	07df      	lsls	r7, r3, #31
 80018f0:	d521      	bpl.n	8001936 <HAL_RCC_ClockConfig+0xa6>
    if (RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_HSE)
 80018f2:	6862      	ldr	r2, [r4, #4]
 80018f4:	2a01      	cmp	r2, #1
 80018f6:	d057      	beq.n	80019a8 <HAL_RCC_ClockConfig+0x118>
    else if ((RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_PLLCLK)   ||
 80018f8:	1e93      	subs	r3, r2, #2
 80018fa:	2b01      	cmp	r3, #1
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 80018fc:	4b2f      	ldr	r3, [pc, #188]	@ (80019bc <HAL_RCC_ClockConfig+0x12c>)
 80018fe:	681b      	ldr	r3, [r3, #0]
    else if ((RCC_ClkInitStruct->SYSCLKSource == RCC_SYSCLKSOURCE_PLLCLK)   ||
 8001900:	d94d      	bls.n	800199e <HAL_RCC_ClockConfig+0x10e>
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSIRDY) == RESET)
 8001902:	0799      	lsls	r1, r3, #30
 8001904:	d5d8      	bpl.n	80018b8 <HAL_RCC_ClockConfig+0x28>
    __HAL_RCC_SYSCLK_CONFIG(RCC_ClkInitStruct->SYSCLKSource);
 8001906:	4e2d      	ldr	r6, [pc, #180]	@ (80019bc <HAL_RCC_ClockConfig+0x12c>)
 8001908:	68b3      	ldr	r3, [r6, #8]
 800190a:	f023 0303 	bic.w	r3, r3, #3
 800190e:	4313      	orrs	r3, r2
 8001910:	60b3      	str	r3, [r6, #8]
    tickstart = HAL_GetTick();
 8001912:	f7ff f94d 	bl	8000bb0 <HAL_GetTick>
      if ((HAL_GetTick() - tickstart) > CLOCKSWITCH_TIMEOUT_VALUE)
 8001916:	f241 3888 	movw	r8, #5000	@ 0x1388
    tickstart = HAL_GetTick();
 800191a:	4607      	mov	r7, r0
    while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
 800191c:	e004      	b.n	8001928 <HAL_RCC_ClockConfig+0x98>
      if ((HAL_GetTick() - tickstart) > CLOCKSWITCH_TIMEOUT_VALUE)
 800191e:	f7ff f947 	bl	8000bb0 <HAL_GetTick>
 8001922:	1bc0      	subs	r0, r0, r7
 8001924:	4540      	cmp	r0, r8
 8001926:	d844      	bhi.n	80019b2 <HAL_RCC_ClockConfig+0x122>
    while (__HAL_RCC_GET_SYSCLK_SOURCE() != (RCC_ClkInitStruct->SYSCLKSource << RCC_CFGR_SWS_Pos))
 8001928:	68b3      	ldr	r3, [r6, #8]
 800192a:	6862      	ldr	r2, [r4, #4]
 800192c:	f003 030c 	and.w	r3, r3, #12
 8001930:	ebb3 0f82 	cmp.w	r3, r2, lsl #2
 8001934:	d1f3      	bne.n	800191e <HAL_RCC_ClockConfig+0x8e>
  if (FLatency < __HAL_FLASH_GET_LATENCY())
 8001936:	4a20      	ldr	r2, [pc, #128]	@ (80019b8 <HAL_RCC_ClockConfig+0x128>)
 8001938:	6813      	ldr	r3, [r2, #0]
 800193a:	f003 0307 	and.w	r3, r3, #7
 800193e:	42ab      	cmp	r3, r5
 8001940:	d906      	bls.n	8001950 <HAL_RCC_ClockConfig+0xc0>
    __HAL_FLASH_SET_LATENCY(FLatency);
 8001942:	b2eb      	uxtb	r3, r5
 8001944:	7013      	strb	r3, [r2, #0]
    if (__HAL_FLASH_GET_LATENCY() != FLatency)
 8001946:	6813      	ldr	r3, [r2, #0]
 8001948:	f003 0307 	and.w	r3, r3, #7
 800194c:	42ab      	cmp	r3, r5
 800194e:	d1b3      	bne.n	80018b8 <HAL_RCC_ClockConfig+0x28>
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK1) == RCC_CLOCKTYPE_PCLK1)
 8001950:	6823      	ldr	r3, [r4, #0]
 8001952:	075a      	lsls	r2, r3, #29
 8001954:	d506      	bpl.n	8001964 <HAL_RCC_ClockConfig+0xd4>
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE1, RCC_ClkInitStruct->APB1CLKDivider);
 8001956:	4919      	ldr	r1, [pc, #100]	@ (80019bc <HAL_RCC_ClockConfig+0x12c>)
 8001958:	68e0      	ldr	r0, [r4, #12]
 800195a:	688a      	ldr	r2, [r1, #8]
 800195c:	f422 52e0 	bic.w	r2, r2, #7168	@ 0x1c00
 8001960:	4302      	orrs	r2, r0
 8001962:	608a      	str	r2, [r1, #8]
  if (((RCC_ClkInitStruct->ClockType) & RCC_CLOCKTYPE_PCLK2) == RCC_CLOCKTYPE_PCLK2)
 8001964:	071b      	lsls	r3, r3, #28
 8001966:	d507      	bpl.n	8001978 <HAL_RCC_ClockConfig+0xe8>
    MODIFY_REG(RCC->CFGR, RCC_CFGR_PPRE2, ((RCC_ClkInitStruct->APB2CLKDivider) << 3U));
 8001968:	4a14      	ldr	r2, [pc, #80]	@ (80019bc <HAL_RCC_ClockConfig+0x12c>)
 800196a:	6921      	ldr	r1, [r4, #16]
 800196c:	6893      	ldr	r3, [r2, #8]
 800196e:	f423 4360 	bic.w	r3, r3, #57344	@ 0xe000
 8001972:	ea43 03c1 	orr.w	r3, r3, r1, lsl #3
 8001976:	6093      	str	r3, [r2, #8]
  SystemCoreClock = HAL_RCC_GetSysClockFreq() >> AHBPrescTable[(RCC->CFGR & RCC_CFGR_HPRE) >> RCC_CFGR_HPRE_Pos];
 8001978:	f7ff ff56 	bl	8001828 <HAL_RCC_GetSysClockFreq>
 800197c:	4a0f      	ldr	r2, [pc, #60]	@ (80019bc <HAL_RCC_ClockConfig+0x12c>)
 800197e:	4c10      	ldr	r4, [pc, #64]	@ (80019c0 <HAL_RCC_ClockConfig+0x130>)
 8001980:	6892      	ldr	r2, [r2, #8]
 8001982:	4910      	ldr	r1, [pc, #64]	@ (80019c4 <HAL_RCC_ClockConfig+0x134>)
 8001984:	f3c2 1203 	ubfx	r2, r2, #4, #4
 8001988:	4603      	mov	r3, r0
 800198a:	5ca2      	ldrb	r2, [r4, r2]
  HAL_InitTick(uwTickPrio);
 800198c:	480e      	ldr	r0, [pc, #56]	@ (80019c8 <HAL_RCC_ClockConfig+0x138>)
  SystemCoreClock = HAL_RCC_GetSysClockFreq() >> AHBPrescTable[(RCC->CFGR & RCC_CFGR_HPRE) >> RCC_CFGR_HPRE_Pos];
 800198e:	40d3      	lsrs	r3, r2
  HAL_InitTick(uwTickPrio);
 8001990:	6800      	ldr	r0, [r0, #0]
  SystemCoreClock = HAL_RCC_GetSysClockFreq() >> AHBPrescTable[(RCC->CFGR & RCC_CFGR_HPRE) >> RCC_CFGR_HPRE_Pos];
 8001992:	600b      	str	r3, [r1, #0]
  HAL_InitTick(uwTickPrio);
 8001994:	f7ff f8c2 	bl	8000b1c <HAL_InitTick>
  return HAL_OK;
 8001998:	2000      	movs	r0, #0
}
 800199a:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_PLLRDY) == RESET)
 800199e:	0198      	lsls	r0, r3, #6
 80019a0:	d4b1      	bmi.n	8001906 <HAL_RCC_ClockConfig+0x76>
 80019a2:	e789      	b.n	80018b8 <HAL_RCC_ClockConfig+0x28>
    return HAL_ERROR;
 80019a4:	2001      	movs	r0, #1
}
 80019a6:	4770      	bx	lr
      if (__HAL_RCC_GET_FLAG(RCC_FLAG_HSERDY) == RESET)
 80019a8:	4b04      	ldr	r3, [pc, #16]	@ (80019bc <HAL_RCC_ClockConfig+0x12c>)
 80019aa:	681b      	ldr	r3, [r3, #0]
 80019ac:	039e      	lsls	r6, r3, #14
 80019ae:	d4aa      	bmi.n	8001906 <HAL_RCC_ClockConfig+0x76>
 80019b0:	e782      	b.n	80018b8 <HAL_RCC_ClockConfig+0x28>
        return HAL_TIMEOUT;
 80019b2:	2003      	movs	r0, #3
 80019b4:	e781      	b.n	80018ba <HAL_RCC_ClockConfig+0x2a>
 80019b6:	bf00      	nop
 80019b8:	40023c00 	.word	0x40023c00
 80019bc:	40023800 	.word	0x40023800
 80019c0:	08003984 	.word	0x08003984
 80019c4:	20000000 	.word	0x20000000
 80019c8:	20000008 	.word	0x20000008

080019cc <HAL_TIM_ConfigClockSource>:
{
  HAL_StatusTypeDef status = HAL_OK;
  uint32_t tmpsmcr;

  /* Process Locked */
  __HAL_LOCK(htim);
 80019cc:	f890 203c 	ldrb.w	r2, [r0, #60]	@ 0x3c
 80019d0:	2a01      	cmp	r2, #1
 80019d2:	d06e      	beq.n	8001ab2 <HAL_TIM_ConfigClockSource+0xe6>
 80019d4:	4603      	mov	r3, r0

  /* Check the parameters */
  assert_param(IS_TIM_CLOCKSOURCE(sClockSourceConfig->ClockSource));

  /* Reset the SMS, TS, ECE, ETPS and ETRF bits */
  tmpsmcr = htim->Instance->SMCR;
 80019d6:	6802      	ldr	r2, [r0, #0]
{
 80019d8:	b430      	push	{r4, r5}
  __HAL_LOCK(htim);
 80019da:	2001      	movs	r0, #1
  htim->State = HAL_TIM_STATE_BUSY;
 80019dc:	2402      	movs	r4, #2
  __HAL_LOCK(htim);
 80019de:	f883 003c 	strb.w	r0, [r3, #60]	@ 0x3c
  htim->State = HAL_TIM_STATE_BUSY;
 80019e2:	f883 403d 	strb.w	r4, [r3, #61]	@ 0x3d
  tmpsmcr = htim->Instance->SMCR;
 80019e6:	6894      	ldr	r4, [r2, #8]
  tmpsmcr &= ~(TIM_SMCR_SMS | TIM_SMCR_TS);
  tmpsmcr &= ~(TIM_SMCR_ETF | TIM_SMCR_ETPS | TIM_SMCR_ECE | TIM_SMCR_ETP);
 80019e8:	f424 447f 	bic.w	r4, r4, #65280	@ 0xff00
 80019ec:	f024 0477 	bic.w	r4, r4, #119	@ 0x77
  htim->Instance->SMCR = tmpsmcr;
 80019f0:	6094      	str	r4, [r2, #8]

  switch (sClockSourceConfig->ClockSource)
 80019f2:	680c      	ldr	r4, [r1, #0]
 80019f4:	2c60      	cmp	r4, #96	@ 0x60
 80019f6:	d076      	beq.n	8001ae6 <HAL_TIM_ConfigClockSource+0x11a>
 80019f8:	d811      	bhi.n	8001a1e <HAL_TIM_ConfigClockSource+0x52>
 80019fa:	2c40      	cmp	r4, #64	@ 0x40
 80019fc:	d05b      	beq.n	8001ab6 <HAL_TIM_ConfigClockSource+0xea>
 80019fe:	d82e      	bhi.n	8001a5e <HAL_TIM_ConfigClockSource+0x92>
 8001a00:	2c20      	cmp	r4, #32
 8001a02:	d004      	beq.n	8001a0e <HAL_TIM_ConfigClockSource+0x42>
 8001a04:	f200 8088 	bhi.w	8001b18 <HAL_TIM_ConfigClockSource+0x14c>
 8001a08:	f034 0110 	bics.w	r1, r4, #16
 8001a0c:	d11f      	bne.n	8001a4e <HAL_TIM_ConfigClockSource+0x82>
static void TIM_ITRx_SetConfig(TIM_TypeDef *TIMx, uint32_t InputTriggerSource)
{
  uint32_t tmpsmcr;

  /* Get the TIMx SMCR register value */
  tmpsmcr = TIMx->SMCR;
 8001a0e:	6891      	ldr	r1, [r2, #8]
  /* Reset the TS Bits */
  tmpsmcr &= ~TIM_SMCR_TS;
 8001a10:	f021 0170 	bic.w	r1, r1, #112	@ 0x70
  /* Set the Input Trigger source and the slave mode*/
  tmpsmcr |= (InputTriggerSource | TIM_SLAVEMODE_EXTERNAL1);
 8001a14:	4321      	orrs	r1, r4
 8001a16:	f041 0107 	orr.w	r1, r1, #7
  /* Write to TIMx SMCR */
  TIMx->SMCR = tmpsmcr;
 8001a1a:	6091      	str	r1, [r2, #8]
}
 8001a1c:	e016      	b.n	8001a4c <HAL_TIM_ConfigClockSource+0x80>
  switch (sClockSourceConfig->ClockSource)
 8001a1e:	f5b4 5f80 	cmp.w	r4, #4096	@ 0x1000
 8001a22:	d013      	beq.n	8001a4c <HAL_TIM_ConfigClockSource+0x80>
 8001a24:	f5b4 5f00 	cmp.w	r4, #8192	@ 0x2000
 8001a28:	d033      	beq.n	8001a92 <HAL_TIM_ConfigClockSource+0xc6>
 8001a2a:	2c70      	cmp	r4, #112	@ 0x70
 8001a2c:	d10f      	bne.n	8001a4e <HAL_TIM_ConfigClockSource+0x82>

  /* Reset the ETR Bits */
  tmpsmcr &= ~(TIM_SMCR_ETF | TIM_SMCR_ETPS | TIM_SMCR_ECE | TIM_SMCR_ETP);

  /* Set the Prescaler, the Filter value and the Polarity */
  tmpsmcr |= (uint32_t)(TIM_ExtTRGPrescaler | (TIM_ExtTRGPolarity | (ExtTRGFilter << 8U)));
 8001a2e:	e9d1 5001 	ldrd	r5, r0, [r1, #4]
  tmpsmcr = TIMx->SMCR;
 8001a32:	6894      	ldr	r4, [r2, #8]
  tmpsmcr |= (uint32_t)(TIM_ExtTRGPrescaler | (TIM_ExtTRGPolarity | (ExtTRGFilter << 8U)));
 8001a34:	4328      	orrs	r0, r5
 8001a36:	68cd      	ldr	r5, [r1, #12]
  tmpsmcr &= ~(TIM_SMCR_ETF | TIM_SMCR_ETPS | TIM_SMCR_ECE | TIM_SMCR_ETP);
 8001a38:	f424 417f 	bic.w	r1, r4, #65280	@ 0xff00
  tmpsmcr |= (uint32_t)(TIM_ExtTRGPrescaler | (TIM_ExtTRGPolarity | (ExtTRGFilter << 8U)));
 8001a3c:	ea40 2005 	orr.w	r0, r0, r5, lsl #8
 8001a40:	4308      	orrs	r0, r1

  /* Write to TIMx SMCR */
  TIMx->SMCR = tmpsmcr;
 8001a42:	6090      	str	r0, [r2, #8]
      tmpsmcr = htim->Instance->SMCR;
 8001a44:	6891      	ldr	r1, [r2, #8]
      tmpsmcr |= (TIM_SLAVEMODE_EXTERNAL1 | TIM_CLOCKSOURCE_ETRMODE1);
 8001a46:	f041 0177 	orr.w	r1, r1, #119	@ 0x77
      htim->Instance->SMCR = tmpsmcr;
 8001a4a:	6091      	str	r1, [r2, #8]
  HAL_StatusTypeDef status = HAL_OK;
 8001a4c:	2000      	movs	r0, #0
  htim->State = HAL_TIM_STATE_READY;
 8001a4e:	2101      	movs	r1, #1
  __HAL_UNLOCK(htim);
 8001a50:	2200      	movs	r2, #0
  htim->State = HAL_TIM_STATE_READY;
 8001a52:	f883 103d 	strb.w	r1, [r3, #61]	@ 0x3d
  __HAL_UNLOCK(htim);
 8001a56:	f883 203c 	strb.w	r2, [r3, #60]	@ 0x3c
}
 8001a5a:	bc30      	pop	{r4, r5}
 8001a5c:	4770      	bx	lr
  switch (sClockSourceConfig->ClockSource)
 8001a5e:	2c50      	cmp	r4, #80	@ 0x50
 8001a60:	d1f5      	bne.n	8001a4e <HAL_TIM_ConfigClockSource+0x82>
                               sClockSourceConfig->ClockPolarity,
 8001a62:	6848      	ldr	r0, [r1, #4]
                               sClockSourceConfig->ClockFilter);
 8001a64:	68cc      	ldr	r4, [r1, #12]
  tmpccer = TIMx->CCER;
 8001a66:	6a11      	ldr	r1, [r2, #32]
  tmpccer &= ~(TIM_CCER_CC1P | TIM_CCER_CC1NP);
 8001a68:	f021 010a 	bic.w	r1, r1, #10
  tmpccer |= TIM_ICPolarity;
 8001a6c:	4308      	orrs	r0, r1
  TIMx->CCER &= ~TIM_CCER_CC1E;
 8001a6e:	6a11      	ldr	r1, [r2, #32]
 8001a70:	f021 0101 	bic.w	r1, r1, #1
 8001a74:	6211      	str	r1, [r2, #32]
  tmpccmr1 = TIMx->CCMR1;
 8001a76:	6991      	ldr	r1, [r2, #24]
  tmpccmr1 &= ~TIM_CCMR1_IC1F;
 8001a78:	f021 01f0 	bic.w	r1, r1, #240	@ 0xf0
  tmpccmr1 |= (TIM_ICFilter << 4U);
 8001a7c:	ea41 1104 	orr.w	r1, r1, r4, lsl #4
  TIMx->CCMR1 = tmpccmr1;
 8001a80:	6191      	str	r1, [r2, #24]
  TIMx->CCER = tmpccer;
 8001a82:	6210      	str	r0, [r2, #32]
  tmpsmcr = TIMx->SMCR;
 8001a84:	6891      	ldr	r1, [r2, #8]
  tmpsmcr &= ~TIM_SMCR_TS;
 8001a86:	f021 0170 	bic.w	r1, r1, #112	@ 0x70
  tmpsmcr |= (InputTriggerSource | TIM_SLAVEMODE_EXTERNAL1);
 8001a8a:	f041 0157 	orr.w	r1, r1, #87	@ 0x57
  TIMx->SMCR = tmpsmcr;
 8001a8e:	6091      	str	r1, [r2, #8]
}
 8001a90:	e7dc      	b.n	8001a4c <HAL_TIM_ConfigClockSource+0x80>
  tmpsmcr |= (uint32_t)(TIM_ExtTRGPrescaler | (TIM_ExtTRGPolarity | (ExtTRGFilter << 8U)));
 8001a92:	e9d1 5001 	ldrd	r5, r0, [r1, #4]
  tmpsmcr = TIMx->SMCR;
 8001a96:	6894      	ldr	r4, [r2, #8]
  tmpsmcr |= (uint32_t)(TIM_ExtTRGPrescaler | (TIM_ExtTRGPolarity | (ExtTRGFilter << 8U)));
 8001a98:	4328      	orrs	r0, r5
 8001a9a:	68cd      	ldr	r5, [r1, #12]
  tmpsmcr &= ~(TIM_SMCR_ETF | TIM_SMCR_ETPS | TIM_SMCR_ECE | TIM_SMCR_ETP);
 8001a9c:	f424 417f 	bic.w	r1, r4, #65280	@ 0xff00
  tmpsmcr |= (uint32_t)(TIM_ExtTRGPrescaler | (TIM_ExtTRGPolarity | (ExtTRGFilter << 8U)));
 8001aa0:	ea40 2005 	orr.w	r0, r0, r5, lsl #8
 8001aa4:	4308      	orrs	r0, r1
  TIMx->SMCR = tmpsmcr;
 8001aa6:	6090      	str	r0, [r2, #8]
      htim->Instance->SMCR |= TIM_SMCR_ECE;
 8001aa8:	6891      	ldr	r1, [r2, #8]
 8001aaa:	f441 4180 	orr.w	r1, r1, #16384	@ 0x4000
 8001aae:	6091      	str	r1, [r2, #8]
      break;
 8001ab0:	e7cc      	b.n	8001a4c <HAL_TIM_ConfigClockSource+0x80>
  __HAL_LOCK(htim);
 8001ab2:	2002      	movs	r0, #2
}
 8001ab4:	4770      	bx	lr
                               sClockSourceConfig->ClockPolarity,
 8001ab6:	6848      	ldr	r0, [r1, #4]
                               sClockSourceConfig->ClockFilter);
 8001ab8:	68cc      	ldr	r4, [r1, #12]
  tmpccer = TIMx->CCER;
 8001aba:	6a11      	ldr	r1, [r2, #32]
  tmpccer &= ~(TIM_CCER_CC1P | TIM_CCER_CC1NP);
 8001abc:	f021 010a 	bic.w	r1, r1, #10
  tmpccer |= TIM_ICPolarity;
 8001ac0:	4308      	orrs	r0, r1
  TIMx->CCER &= ~TIM_CCER_CC1E;
 8001ac2:	6a11      	ldr	r1, [r2, #32]
 8001ac4:	f021 0101 	bic.w	r1, r1, #1
 8001ac8:	6211      	str	r1, [r2, #32]
  tmpccmr1 = TIMx->CCMR1;
 8001aca:	6991      	ldr	r1, [r2, #24]
  tmpccmr1 &= ~TIM_CCMR1_IC1F;
 8001acc:	f021 01f0 	bic.w	r1, r1, #240	@ 0xf0
  tmpccmr1 |= (TIM_ICFilter << 4U);
 8001ad0:	ea41 1104 	orr.w	r1, r1, r4, lsl #4
  TIMx->CCMR1 = tmpccmr1;
 8001ad4:	6191      	str	r1, [r2, #24]
  TIMx->CCER = tmpccer;
 8001ad6:	6210      	str	r0, [r2, #32]
  tmpsmcr = TIMx->SMCR;
 8001ad8:	6891      	ldr	r1, [r2, #8]
  tmpsmcr &= ~TIM_SMCR_TS;
 8001ada:	f021 0170 	bic.w	r1, r1, #112	@ 0x70
  tmpsmcr |= (InputTriggerSource | TIM_SLAVEMODE_EXTERNAL1);
 8001ade:	f041 0147 	orr.w	r1, r1, #71	@ 0x47
  TIMx->SMCR = tmpsmcr;
 8001ae2:	6091      	str	r1, [r2, #8]
}
 8001ae4:	e7b2      	b.n	8001a4c <HAL_TIM_ConfigClockSource+0x80>
                               sClockSourceConfig->ClockPolarity,
 8001ae6:	6848      	ldr	r0, [r1, #4]
                               sClockSourceConfig->ClockFilter);
 8001ae8:	68cc      	ldr	r4, [r1, #12]
  tmpccer = TIMx->CCER;
 8001aea:	6a11      	ldr	r1, [r2, #32]
  tmpccer &= ~(TIM_CCER_CC2P | TIM_CCER_CC2NP);
 8001aec:	f021 01a0 	bic.w	r1, r1, #160	@ 0xa0
  tmpccer |= (TIM_ICPolarity << 4U);
 8001af0:	ea41 1100 	orr.w	r1, r1, r0, lsl #4
  TIMx->CCER &= ~TIM_CCER_CC2E;
 8001af4:	6a10      	ldr	r0, [r2, #32]
 8001af6:	f020 0010 	bic.w	r0, r0, #16
 8001afa:	6210      	str	r0, [r2, #32]
  tmpccmr1 = TIMx->CCMR1;
 8001afc:	6990      	ldr	r0, [r2, #24]
  tmpccmr1 &= ~TIM_CCMR1_IC2F;
 8001afe:	f420 4070 	bic.w	r0, r0, #61440	@ 0xf000
  tmpccmr1 |= (TIM_ICFilter << 12U);
 8001b02:	ea40 3004 	orr.w	r0, r0, r4, lsl #12
  TIMx->CCMR1 = tmpccmr1 ;
 8001b06:	6190      	str	r0, [r2, #24]
  TIMx->CCER = tmpccer;
 8001b08:	6211      	str	r1, [r2, #32]
  tmpsmcr = TIMx->SMCR;
 8001b0a:	6891      	ldr	r1, [r2, #8]
  tmpsmcr &= ~TIM_SMCR_TS;
 8001b0c:	f021 0170 	bic.w	r1, r1, #112	@ 0x70
  tmpsmcr |= (InputTriggerSource | TIM_SLAVEMODE_EXTERNAL1);
 8001b10:	f041 0167 	orr.w	r1, r1, #103	@ 0x67
  TIMx->SMCR = tmpsmcr;
 8001b14:	6091      	str	r1, [r2, #8]
}
 8001b16:	e799      	b.n	8001a4c <HAL_TIM_ConfigClockSource+0x80>
  switch (sClockSourceConfig->ClockSource)
 8001b18:	2c30      	cmp	r4, #48	@ 0x30
 8001b1a:	f43f af78 	beq.w	8001a0e <HAL_TIM_ConfigClockSource+0x42>
 8001b1e:	e796      	b.n	8001a4e <HAL_TIM_ConfigClockSource+0x82>

08001b20 <TIM_Base_SetConfig>:
{
 8001b20:	b470      	push	{r4, r5, r6}
  if (IS_TIM_COUNTER_MODE_SELECT_INSTANCE(TIMx))
 8001b22:	4e34      	ldr	r6, [pc, #208]	@ (8001bf4 <TIM_Base_SetConfig+0xd4>)
  MODIFY_REG(tmpcr1, TIM_CR1_ARPE, Structure->AutoReloadPreload);
 8001b24:	694a      	ldr	r2, [r1, #20]
  tmpcr1 = TIMx->CR1;
 8001b26:	6803      	ldr	r3, [r0, #0]
  TIMx->ARR = (uint32_t)Structure->Period ;
 8001b28:	688d      	ldr	r5, [r1, #8]
  TIMx->PSC = Structure->Prescaler;
 8001b2a:	680c      	ldr	r4, [r1, #0]
  if (IS_TIM_COUNTER_MODE_SELECT_INSTANCE(TIMx))
 8001b2c:	42b0      	cmp	r0, r6
 8001b2e:	d045      	beq.n	8001bbc <TIM_Base_SetConfig+0x9c>
 8001b30:	f1b0 4f80 	cmp.w	r0, #1073741824	@ 0x40000000
 8001b34:	d034      	beq.n	8001ba0 <TIM_Base_SetConfig+0x80>
 8001b36:	f5a6 467c 	sub.w	r6, r6, #64512	@ 0xfc00
 8001b3a:	42b0      	cmp	r0, r6
 8001b3c:	d030      	beq.n	8001ba0 <TIM_Base_SetConfig+0x80>
 8001b3e:	f506 6680 	add.w	r6, r6, #1024	@ 0x400
 8001b42:	42b0      	cmp	r0, r6
 8001b44:	d02c      	beq.n	8001ba0 <TIM_Base_SetConfig+0x80>
 8001b46:	f506 6680 	add.w	r6, r6, #1024	@ 0x400
 8001b4a:	42b0      	cmp	r0, r6
 8001b4c:	d028      	beq.n	8001ba0 <TIM_Base_SetConfig+0x80>
 8001b4e:	f506 4678 	add.w	r6, r6, #63488	@ 0xf800
 8001b52:	42b0      	cmp	r0, r6
 8001b54:	d032      	beq.n	8001bbc <TIM_Base_SetConfig+0x9c>
  if (IS_TIM_CLOCK_DIVISION_INSTANCE(TIMx))
 8001b56:	4e28      	ldr	r6, [pc, #160]	@ (8001bf8 <TIM_Base_SetConfig+0xd8>)
 8001b58:	42b0      	cmp	r0, r6
 8001b5a:	d025      	beq.n	8001ba8 <TIM_Base_SetConfig+0x88>
 8001b5c:	f506 6680 	add.w	r6, r6, #1024	@ 0x400
 8001b60:	42b0      	cmp	r0, r6
 8001b62:	d021      	beq.n	8001ba8 <TIM_Base_SetConfig+0x88>
 8001b64:	f506 6680 	add.w	r6, r6, #1024	@ 0x400
 8001b68:	42b0      	cmp	r0, r6
 8001b6a:	d01d      	beq.n	8001ba8 <TIM_Base_SetConfig+0x88>
 8001b6c:	f5a6 3698 	sub.w	r6, r6, #77824	@ 0x13000
 8001b70:	42b0      	cmp	r0, r6
 8001b72:	d033      	beq.n	8001bdc <TIM_Base_SetConfig+0xbc>
 8001b74:	f506 6680 	add.w	r6, r6, #1024	@ 0x400
 8001b78:	42b0      	cmp	r0, r6
 8001b7a:	d015      	beq.n	8001ba8 <TIM_Base_SetConfig+0x88>
 8001b7c:	f506 6680 	add.w	r6, r6, #1024	@ 0x400
 8001b80:	42b0      	cmp	r0, r6
 8001b82:	d011      	beq.n	8001ba8 <TIM_Base_SetConfig+0x88>
  MODIFY_REG(tmpcr1, TIM_CR1_ARPE, Structure->AutoReloadPreload);
 8001b84:	f023 0380 	bic.w	r3, r3, #128	@ 0x80
  TIMx->ARR = (uint32_t)Structure->Period ;
 8001b88:	62c5      	str	r5, [r0, #44]	@ 0x2c
  MODIFY_REG(tmpcr1, TIM_CR1_ARPE, Structure->AutoReloadPreload);
 8001b8a:	431a      	orrs	r2, r3
  TIMx->PSC = Structure->Prescaler;
 8001b8c:	6284      	str	r4, [r0, #40]	@ 0x28
  SET_BIT(TIMx->CR1, TIM_CR1_URS);
 8001b8e:	6803      	ldr	r3, [r0, #0]
  TIMx->EGR = TIM_EGR_UG;
 8001b90:	2101      	movs	r1, #1
  SET_BIT(TIMx->CR1, TIM_CR1_URS);
 8001b92:	f043 0304 	orr.w	r3, r3, #4
 8001b96:	6003      	str	r3, [r0, #0]
}
 8001b98:	bc70      	pop	{r4, r5, r6}
  TIMx->EGR = TIM_EGR_UG;
 8001b9a:	6141      	str	r1, [r0, #20]
  TIMx->CR1 = tmpcr1;
 8001b9c:	6002      	str	r2, [r0, #0]
}
 8001b9e:	4770      	bx	lr
    tmpcr1 |= Structure->CounterMode;
 8001ba0:	684e      	ldr	r6, [r1, #4]
    tmpcr1 &= ~(TIM_CR1_DIR | TIM_CR1_CMS);
 8001ba2:	f023 0370 	bic.w	r3, r3, #112	@ 0x70
    tmpcr1 |= Structure->CounterMode;
 8001ba6:	4333      	orrs	r3, r6
    tmpcr1 |= (uint32_t)Structure->ClockDivision;
 8001ba8:	68c9      	ldr	r1, [r1, #12]
  TIMx->ARR = (uint32_t)Structure->Period ;
 8001baa:	62c5      	str	r5, [r0, #44]	@ 0x2c
    tmpcr1 &= ~TIM_CR1_CKD;
 8001bac:	f423 7340 	bic.w	r3, r3, #768	@ 0x300
    tmpcr1 |= (uint32_t)Structure->ClockDivision;
 8001bb0:	430b      	orrs	r3, r1
  MODIFY_REG(tmpcr1, TIM_CR1_ARPE, Structure->AutoReloadPreload);
 8001bb2:	f023 0380 	bic.w	r3, r3, #128	@ 0x80
 8001bb6:	431a      	orrs	r2, r3
  TIMx->PSC = Structure->Prescaler;
 8001bb8:	6284      	str	r4, [r0, #40]	@ 0x28
  if (IS_TIM_REPETITION_COUNTER_INSTANCE(TIMx))
 8001bba:	e7e8      	b.n	8001b8e <TIM_Base_SetConfig+0x6e>
    tmpcr1 |= Structure->CounterMode;
 8001bbc:	684e      	ldr	r6, [r1, #4]
    tmpcr1 &= ~(TIM_CR1_DIR | TIM_CR1_CMS);
 8001bbe:	f023 0370 	bic.w	r3, r3, #112	@ 0x70
    tmpcr1 |= Structure->CounterMode;
 8001bc2:	4333      	orrs	r3, r6
    tmpcr1 |= (uint32_t)Structure->ClockDivision;
 8001bc4:	68ce      	ldr	r6, [r1, #12]
  TIMx->ARR = (uint32_t)Structure->Period ;
 8001bc6:	62c5      	str	r5, [r0, #44]	@ 0x2c
    tmpcr1 &= ~TIM_CR1_CKD;
 8001bc8:	f423 7340 	bic.w	r3, r3, #768	@ 0x300
    tmpcr1 |= (uint32_t)Structure->ClockDivision;
 8001bcc:	4333      	orrs	r3, r6
  MODIFY_REG(tmpcr1, TIM_CR1_ARPE, Structure->AutoReloadPreload);
 8001bce:	f023 0380 	bic.w	r3, r3, #128	@ 0x80
  TIMx->PSC = Structure->Prescaler;
 8001bd2:	6284      	str	r4, [r0, #40]	@ 0x28
  MODIFY_REG(tmpcr1, TIM_CR1_ARPE, Structure->AutoReloadPreload);
 8001bd4:	431a      	orrs	r2, r3
    TIMx->RCR = Structure->RepetitionCounter;
 8001bd6:	690b      	ldr	r3, [r1, #16]
 8001bd8:	6303      	str	r3, [r0, #48]	@ 0x30
 8001bda:	e7d8      	b.n	8001b8e <TIM_Base_SetConfig+0x6e>
    tmpcr1 |= (uint32_t)Structure->ClockDivision;
 8001bdc:	68c9      	ldr	r1, [r1, #12]
    tmpcr1 &= ~TIM_CR1_CKD;
 8001bde:	f423 7340 	bic.w	r3, r3, #768	@ 0x300
    tmpcr1 |= (uint32_t)Structure->ClockDivision;
 8001be2:	430b      	orrs	r3, r1
  MODIFY_REG(tmpcr1, TIM_CR1_ARPE, Structure->AutoReloadPreload);
 8001be4:	f023 0380 	bic.w	r3, r3, #128	@ 0x80
 8001be8:	431a      	orrs	r2, r3
  TIMx->ARR = (uint32_t)Structure->Period ;
 8001bea:	4b04      	ldr	r3, [pc, #16]	@ (8001bfc <TIM_Base_SetConfig+0xdc>)
 8001bec:	62dd      	str	r5, [r3, #44]	@ 0x2c
  TIMx->PSC = Structure->Prescaler;
 8001bee:	629c      	str	r4, [r3, #40]	@ 0x28
  if (IS_TIM_REPETITION_COUNTER_INSTANCE(TIMx))
 8001bf0:	e7cd      	b.n	8001b8e <TIM_Base_SetConfig+0x6e>
 8001bf2:	bf00      	nop
 8001bf4:	40010000 	.word	0x40010000
 8001bf8:	40014000 	.word	0x40014000
 8001bfc:	40001800 	.word	0x40001800

08001c00 <HAL_TIM_Base_Init>:
  if (htim == NULL)
 8001c00:	b350      	cbz	r0, 8001c58 <HAL_TIM_Base_Init+0x58>
{
 8001c02:	b510      	push	{r4, lr}
  if (htim->State == HAL_TIM_STATE_RESET)
 8001c04:	f890 303d 	ldrb.w	r3, [r0, #61]	@ 0x3d
 8001c08:	4604      	mov	r4, r0
 8001c0a:	f003 02ff 	and.w	r2, r3, #255	@ 0xff
 8001c0e:	b1f3      	cbz	r3, 8001c4e <HAL_TIM_Base_Init+0x4e>
  TIM_Base_SetConfig(htim->Instance, &htim->Init);
 8001c10:	4621      	mov	r1, r4
  htim->State = HAL_TIM_STATE_BUSY;
 8001c12:	2302      	movs	r3, #2
 8001c14:	f884 303d 	strb.w	r3, [r4, #61]	@ 0x3d
  TIM_Base_SetConfig(htim->Instance, &htim->Init);
 8001c18:	f851 0b04 	ldr.w	r0, [r1], #4
 8001c1c:	f7ff ff80 	bl	8001b20 <TIM_Base_SetConfig>
  htim->DMABurstState = HAL_DMA_BURST_STATE_READY;
 8001c20:	2301      	movs	r3, #1
 8001c22:	f884 3046 	strb.w	r3, [r4, #70]	@ 0x46
  TIM_CHANNEL_STATE_SET_ALL(htim, HAL_TIM_CHANNEL_STATE_READY);
 8001c26:	f884 303e 	strb.w	r3, [r4, #62]	@ 0x3e
 8001c2a:	f884 303f 	strb.w	r3, [r4, #63]	@ 0x3f
 8001c2e:	f884 3040 	strb.w	r3, [r4, #64]	@ 0x40
 8001c32:	f884 3041 	strb.w	r3, [r4, #65]	@ 0x41
  TIM_CHANNEL_N_STATE_SET_ALL(htim, HAL_TIM_CHANNEL_STATE_READY);
 8001c36:	f884 3042 	strb.w	r3, [r4, #66]	@ 0x42
 8001c3a:	f884 3043 	strb.w	r3, [r4, #67]	@ 0x43
 8001c3e:	f884 3044 	strb.w	r3, [r4, #68]	@ 0x44
 8001c42:	f884 3045 	strb.w	r3, [r4, #69]	@ 0x45
  htim->State = HAL_TIM_STATE_READY;
 8001c46:	f884 303d 	strb.w	r3, [r4, #61]	@ 0x3d
  return HAL_OK;
 8001c4a:	2000      	movs	r0, #0
}
 8001c4c:	bd10      	pop	{r4, pc}
    htim->Lock = HAL_UNLOCKED;
 8001c4e:	f880 203c 	strb.w	r2, [r0, #60]	@ 0x3c
    HAL_TIM_Base_MspInit(htim);
 8001c52:	f7fe ff21 	bl	8000a98 <HAL_TIM_Base_MspInit>
 8001c56:	e7db      	b.n	8001c10 <HAL_TIM_Base_Init+0x10>
    return HAL_ERROR;
 8001c58:	2001      	movs	r0, #1
}
 8001c5a:	4770      	bx	lr

08001c5c <HAL_TIMEx_MasterConfigSynchronization>:
  assert_param(IS_TIM_MASTER_INSTANCE(htim->Instance));
  assert_param(IS_TIM_TRGO_SOURCE(sMasterConfig->MasterOutputTrigger));
  assert_param(IS_TIM_MSM_STATE(sMasterConfig->MasterSlaveMode));

  /* Check input state */
  __HAL_LOCK(htim);
 8001c5c:	f890 203c 	ldrb.w	r2, [r0, #60]	@ 0x3c
 8001c60:	2a01      	cmp	r2, #1
 8001c62:	d037      	beq.n	8001cd4 <HAL_TIMEx_MasterConfigSynchronization+0x78>
 8001c64:	4603      	mov	r3, r0

  /* Change the handler state */
  htim->State = HAL_TIM_STATE_BUSY;

  /* Get the TIMx CR2 register value */
  tmpcr2 = htim->Instance->CR2;
 8001c66:	6802      	ldr	r2, [r0, #0]
  htim->State = HAL_TIM_STATE_BUSY;
 8001c68:	2002      	movs	r0, #2
{
 8001c6a:	b430      	push	{r4, r5}
  htim->State = HAL_TIM_STATE_BUSY;
 8001c6c:	f883 003d 	strb.w	r0, [r3, #61]	@ 0x3d
  tmpcr2 = htim->Instance->CR2;
 8001c70:	6850      	ldr	r0, [r2, #4]
  tmpsmcr = htim->Instance->SMCR;

  /* Reset the MMS Bits */
  tmpcr2 &= ~TIM_CR2_MMS;
  /* Select the TRGO source */
  tmpcr2 |=  sMasterConfig->MasterOutputTrigger;
 8001c72:	680d      	ldr	r5, [r1, #0]
  tmpsmcr = htim->Instance->SMCR;
 8001c74:	6894      	ldr	r4, [r2, #8]
  tmpcr2 &= ~TIM_CR2_MMS;
 8001c76:	f020 0070 	bic.w	r0, r0, #112	@ 0x70
  tmpcr2 |=  sMasterConfig->MasterOutputTrigger;
 8001c7a:	4328      	orrs	r0, r5

  /* Update TIMx CR2 */
  htim->Instance->CR2 = tmpcr2;
 8001c7c:	6050      	str	r0, [r2, #4]

  if (IS_TIM_SLAVE_INSTANCE(htim->Instance))
 8001c7e:	4816      	ldr	r0, [pc, #88]	@ (8001cd8 <HAL_TIMEx_MasterConfigSynchronization+0x7c>)
 8001c80:	4282      	cmp	r2, r0
 8001c82:	d01a      	beq.n	8001cba <HAL_TIMEx_MasterConfigSynchronization+0x5e>
 8001c84:	f1b2 4f80 	cmp.w	r2, #1073741824	@ 0x40000000
 8001c88:	d017      	beq.n	8001cba <HAL_TIMEx_MasterConfigSynchronization+0x5e>
 8001c8a:	f5a0 407c 	sub.w	r0, r0, #64512	@ 0xfc00
 8001c8e:	4282      	cmp	r2, r0
 8001c90:	d013      	beq.n	8001cba <HAL_TIMEx_MasterConfigSynchronization+0x5e>
 8001c92:	f500 6080 	add.w	r0, r0, #1024	@ 0x400
 8001c96:	4282      	cmp	r2, r0
 8001c98:	d00f      	beq.n	8001cba <HAL_TIMEx_MasterConfigSynchronization+0x5e>
 8001c9a:	f500 6080 	add.w	r0, r0, #1024	@ 0x400
 8001c9e:	4282      	cmp	r2, r0
 8001ca0:	d00b      	beq.n	8001cba <HAL_TIMEx_MasterConfigSynchronization+0x5e>
 8001ca2:	f500 4078 	add.w	r0, r0, #63488	@ 0xf800
 8001ca6:	4282      	cmp	r2, r0
 8001ca8:	d007      	beq.n	8001cba <HAL_TIMEx_MasterConfigSynchronization+0x5e>
 8001caa:	f500 5070 	add.w	r0, r0, #15360	@ 0x3c00
 8001cae:	4282      	cmp	r2, r0
 8001cb0:	d003      	beq.n	8001cba <HAL_TIMEx_MasterConfigSynchronization+0x5e>
 8001cb2:	f5a0 3094 	sub.w	r0, r0, #75776	@ 0x12800
 8001cb6:	4282      	cmp	r2, r0
 8001cb8:	d104      	bne.n	8001cc4 <HAL_TIMEx_MasterConfigSynchronization+0x68>
  {
    /* Reset the MSM Bit */
    tmpsmcr &= ~TIM_SMCR_MSM;
    /* Set master mode */
    tmpsmcr |= sMasterConfig->MasterSlaveMode;
 8001cba:	6849      	ldr	r1, [r1, #4]
    tmpsmcr &= ~TIM_SMCR_MSM;
 8001cbc:	f024 0480 	bic.w	r4, r4, #128	@ 0x80
    tmpsmcr |= sMasterConfig->MasterSlaveMode;
 8001cc0:	430c      	orrs	r4, r1

    /* Update TIMx SMCR */
    htim->Instance->SMCR = tmpsmcr;
 8001cc2:	6094      	str	r4, [r2, #8]
  }

  /* Change the htim state */
  htim->State = HAL_TIM_STATE_READY;

  __HAL_UNLOCK(htim);
 8001cc4:	2000      	movs	r0, #0
  htim->State = HAL_TIM_STATE_READY;
 8001cc6:	2201      	movs	r2, #1
 8001cc8:	f883 203d 	strb.w	r2, [r3, #61]	@ 0x3d
  __HAL_UNLOCK(htim);
 8001ccc:	f883 003c 	strb.w	r0, [r3, #60]	@ 0x3c

  return HAL_OK;
}
 8001cd0:	bc30      	pop	{r4, r5}
 8001cd2:	4770      	bx	lr
  __HAL_LOCK(htim);
 8001cd4:	2002      	movs	r0, #2
}
 8001cd6:	4770      	bx	lr
 8001cd8:	40010000 	.word	0x40010000

08001cdc <osKernelInitialize>:
 */
__STATIC_FORCEINLINE uint32_t __get_IPSR(void)
{
  uint32_t result;

  __ASM volatile ("MRS %0, ipsr" : "=r" (result) );
 8001cdc:	f3ef 8305 	mrs	r3, IPSR
/*---------------------------------------------------------------------------*/

osStatus_t osKernelInitialize (void) {
  osStatus_t stat;

  if (IS_IRQ()) {
 8001ce0:	b92b      	cbnz	r3, 8001cee <osKernelInitialize+0x12>
    stat = osErrorISR;
  }
  else {
    if (KernelState == osKernelInactive) {
 8001ce2:	4b06      	ldr	r3, [pc, #24]	@ (8001cfc <osKernelInitialize+0x20>)
 8001ce4:	6818      	ldr	r0, [r3, #0]
 8001ce6:	b928      	cbnz	r0, 8001cf4 <osKernelInitialize+0x18>
        EvrFreeRTOSSetup(0U);
      #endif
      #if defined(USE_FreeRTOS_HEAP_5) && (HEAP_5_REGION_SETUP == 1)
        vPortDefineHeapRegions (configHEAP_5_REGIONS);
      #endif
      KernelState = osKernelReady;
 8001ce8:	2201      	movs	r2, #1
 8001cea:	601a      	str	r2, [r3, #0]
      stat = osOK;
 8001cec:	4770      	bx	lr
    stat = osErrorISR;
 8001cee:	f06f 0005 	mvn.w	r0, #5
 8001cf2:	4770      	bx	lr
    } else {
      stat = osError;
 8001cf4:	f04f 30ff 	mov.w	r0, #**********
    }
  }

  return (stat);
}
 8001cf8:	4770      	bx	lr
 8001cfa:	bf00      	nop
 8001cfc:	200007e0 	.word	0x200007e0

08001d00 <osKernelStart>:
  }

  return (state);
}

osStatus_t osKernelStart (void) {
 8001d00:	b510      	push	{r4, lr}
 8001d02:	f3ef 8405 	mrs	r4, IPSR
  osStatus_t stat;

  if (IS_IRQ()) {
 8001d06:	b974      	cbnz	r4, 8001d26 <osKernelStart+0x26>
    stat = osErrorISR;
  }
  else {
    if (KernelState == osKernelReady) {
 8001d08:	4b08      	ldr	r3, [pc, #32]	@ (8001d2c <osKernelStart+0x2c>)
 8001d0a:	681a      	ldr	r2, [r3, #0]
 8001d0c:	2a01      	cmp	r2, #1
 8001d0e:	d107      	bne.n	8001d20 <osKernelStart+0x20>
    SCB->SHP[(((uint32_t)IRQn) & 0xFUL)-4UL] = (uint8_t)((priority << (8U - __NVIC_PRIO_BITS)) & (uint32_t)0xFFUL);
 8001d10:	4907      	ldr	r1, [pc, #28]	@ (8001d30 <osKernelStart+0x30>)
      /* Ensure SVC priority is at the reset value */
      SVC_Setup();
      /* Change state to enable IRQ masking check */
      KernelState = osKernelRunning;
 8001d12:	2202      	movs	r2, #2
 8001d14:	77cc      	strb	r4, [r1, #31]
 8001d16:	601a      	str	r2, [r3, #0]
      /* Start the kernel scheduler */
      vTaskStartScheduler();
 8001d18:	f000 fdd6 	bl	80028c8 <vTaskStartScheduler>
      stat = osOK;
 8001d1c:	4620      	mov	r0, r4
      stat = osError;
    }
  }

  return (stat);
}
 8001d1e:	bd10      	pop	{r4, pc}
      stat = osError;
 8001d20:	f04f 30ff 	mov.w	r0, #**********
}
 8001d24:	bd10      	pop	{r4, pc}
    stat = osErrorISR;
 8001d26:	f06f 0005 	mvn.w	r0, #5
}
 8001d2a:	bd10      	pop	{r4, pc}
 8001d2c:	200007e0 	.word	0x200007e0
 8001d30:	e000ed00 	.word	0xe000ed00

08001d34 <osThreadNew>:
  return (configCPU_CLOCK_HZ);
}

/*---------------------------------------------------------------------------*/

osThreadId_t osThreadNew (osThreadFunc_t func, void *argument, const osThreadAttr_t *attr) {
 8001d34:	b5f0      	push	{r4, r5, r6, r7, lr}
 8001d36:	b087      	sub	sp, #28
  uint32_t stack;
  TaskHandle_t hTask;
  UBaseType_t prio;
  int32_t mem;

  hTask = NULL;
 8001d38:	2500      	movs	r5, #0
osThreadId_t osThreadNew (osThreadFunc_t func, void *argument, const osThreadAttr_t *attr) {
 8001d3a:	4614      	mov	r4, r2
  hTask = NULL;
 8001d3c:	9505      	str	r5, [sp, #20]
 8001d3e:	f3ef 8205 	mrs	r2, IPSR

  if (!IS_IRQ() && (func != NULL)) {
 8001d42:	b112      	cbz	r2, 8001d4a <osThreadNew+0x16>
      if (attr->priority != osPriorityNone) {
        prio = (UBaseType_t)attr->priority;
      }

      if ((prio < osPriorityIdle) || (prio > osPriorityISR) || ((attr->attr_bits & osThreadJoinable) == osThreadJoinable)) {
        return (NULL);
 8001d44:	2000      	movs	r0, #0
      }
    }
  }

  return ((osThreadId_t)hTask);
}
 8001d46:	b007      	add	sp, #28
 8001d48:	bdf0      	pop	{r4, r5, r6, r7, pc}
  if (!IS_IRQ() && (func != NULL)) {
 8001d4a:	2800      	cmp	r0, #0
 8001d4c:	d0fa      	beq.n	8001d44 <osThreadNew+0x10>
    if (attr != NULL) {
 8001d4e:	b304      	cbz	r4, 8001d92 <osThreadNew+0x5e>
      if (attr->priority != osPriorityNone) {
 8001d50:	69a3      	ldr	r3, [r4, #24]
 8001d52:	b9d3      	cbnz	r3, 8001d8a <osThreadNew+0x56>
    prio  = (UBaseType_t)osPriorityNormal;
 8001d54:	2318      	movs	r3, #24
      if ((prio < osPriorityIdle) || (prio > osPriorityISR) || ((attr->attr_bits & osThreadJoinable) == osThreadJoinable)) {
 8001d56:	6862      	ldr	r2, [r4, #4]
 8001d58:	07d2      	lsls	r2, r2, #31
 8001d5a:	d4f3      	bmi.n	8001d44 <osThreadNew+0x10>
      if (attr->stack_size > 0U) {
 8001d5c:	6965      	ldr	r5, [r4, #20]
 8001d5e:	b32d      	cbz	r5, 8001dac <osThreadNew+0x78>
        stack = attr->stack_size / sizeof(StackType_t);
 8001d60:	08aa      	lsrs	r2, r5, #2
      if ((attr->cb_mem    != NULL) && (attr->cb_size    >= sizeof(StaticTask_t)) &&
 8001d62:	68a6      	ldr	r6, [r4, #8]
      if (attr->name != NULL) {
 8001d64:	f8d4 c000 	ldr.w	ip, [r4]
      if ((attr->cb_mem    != NULL) && (attr->cb_size    >= sizeof(StaticTask_t)) &&
 8001d68:	68e7      	ldr	r7, [r4, #12]
 8001d6a:	b30e      	cbz	r6, 8001db0 <osThreadNew+0x7c>
 8001d6c:	2f5b      	cmp	r7, #91	@ 0x5b
 8001d6e:	d9e9      	bls.n	8001d44 <osThreadNew+0x10>
          (attr->stack_mem != NULL) && (attr->stack_size >  0U)) {
 8001d70:	6924      	ldr	r4, [r4, #16]
      if ((attr->cb_mem    != NULL) && (attr->cb_size    >= sizeof(StaticTask_t)) &&
 8001d72:	2c00      	cmp	r4, #0
 8001d74:	d0e6      	beq.n	8001d44 <osThreadNew+0x10>
          (attr->stack_mem != NULL) && (attr->stack_size >  0U)) {
 8001d76:	2d00      	cmp	r5, #0
 8001d78:	d0e4      	beq.n	8001d44 <osThreadNew+0x10>
        hTask = xTaskCreateStatic ((TaskFunction_t)func, name, stack, argument, prio, (StackType_t  *)attr->stack_mem,
 8001d7a:	9300      	str	r3, [sp, #0]
 8001d7c:	e9cd 4601 	strd	r4, r6, [sp, #4]
 8001d80:	460b      	mov	r3, r1
 8001d82:	4661      	mov	r1, ip
 8001d84:	f000 fd36 	bl	80027f4 <xTaskCreateStatic>
 8001d88:	e7dd      	b.n	8001d46 <osThreadNew+0x12>
      if ((prio < osPriorityIdle) || (prio > osPriorityISR) || ((attr->attr_bits & osThreadJoinable) == osThreadJoinable)) {
 8001d8a:	1e5d      	subs	r5, r3, #1
 8001d8c:	2d37      	cmp	r5, #55	@ 0x37
 8001d8e:	d9e2      	bls.n	8001d56 <osThreadNew+0x22>
 8001d90:	e7d8      	b.n	8001d44 <osThreadNew+0x10>
 8001d92:	2280      	movs	r2, #128	@ 0x80
    prio  = (UBaseType_t)osPriorityNormal;
 8001d94:	2318      	movs	r3, #24
          if (xTaskCreate ((TaskFunction_t)func, name, (uint16_t)stack, argument, prio, &hTask) != pdPASS) {
 8001d96:	9300      	str	r3, [sp, #0]
 8001d98:	460b      	mov	r3, r1
 8001d9a:	4621      	mov	r1, r4
 8001d9c:	ac05      	add	r4, sp, #20
 8001d9e:	9401      	str	r4, [sp, #4]
 8001da0:	f000 fd62 	bl	8002868 <xTaskCreate>
 8001da4:	2801      	cmp	r0, #1
 8001da6:	d1cd      	bne.n	8001d44 <osThreadNew+0x10>
  return ((osThreadId_t)hTask);
 8001da8:	9805      	ldr	r0, [sp, #20]
 8001daa:	e7cc      	b.n	8001d46 <osThreadNew+0x12>
    stack = configMINIMAL_STACK_SIZE;
 8001dac:	2280      	movs	r2, #128	@ 0x80
 8001dae:	e7d8      	b.n	8001d62 <osThreadNew+0x2e>
        if ((attr->cb_mem == NULL) && (attr->cb_size == 0U) && (attr->stack_mem == NULL)) {
 8001db0:	2f00      	cmp	r7, #0
 8001db2:	d1c7      	bne.n	8001d44 <osThreadNew+0x10>
 8001db4:	6924      	ldr	r4, [r4, #16]
 8001db6:	2c00      	cmp	r4, #0
 8001db8:	d1c4      	bne.n	8001d44 <osThreadNew+0x10>
          if (xTaskCreate ((TaskFunction_t)func, name, (uint16_t)stack, argument, prio, &hTask) != pdPASS) {
 8001dba:	b292      	uxth	r2, r2
 8001dbc:	4664      	mov	r4, ip
 8001dbe:	e7ea      	b.n	8001d96 <osThreadNew+0x62>

08001dc0 <osDelay>:
 8001dc0:	f3ef 8205 	mrs	r2, IPSR
#endif /* (configUSE_OS2_THREAD_FLAGS == 1) */

osStatus_t osDelay (uint32_t ticks) {
  osStatus_t stat;

  if (IS_IRQ()) {
 8001dc4:	b93a      	cbnz	r2, 8001dd6 <osDelay+0x16>
osStatus_t osDelay (uint32_t ticks) {
 8001dc6:	b508      	push	{r3, lr}
    stat = osErrorISR;
  }
  else {
    stat = osOK;

    if (ticks != 0U) {
 8001dc8:	b908      	cbnz	r0, 8001dce <osDelay+0xe>
    stat = osOK;
 8001dca:	2000      	movs	r0, #0
      vTaskDelay(ticks);
    }
  }

  return (stat);
}
 8001dcc:	bd08      	pop	{r3, pc}
      vTaskDelay(ticks);
 8001dce:	f000 ff19 	bl	8002c04 <vTaskDelay>
    stat = osOK;
 8001dd2:	2000      	movs	r0, #0
}
 8001dd4:	bd08      	pop	{r3, pc}
    stat = osErrorISR;
 8001dd6:	f06f 0005 	mvn.w	r0, #5
}
 8001dda:	4770      	bx	lr

08001ddc <vApplicationGetIdleTaskMemory>:
__WEAK void vApplicationGetIdleTaskMemory (StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize) {
  /* Idle task control block and stack */
  static StaticTask_t Idle_TCB;
  static StackType_t  Idle_Stack[configMINIMAL_STACK_SIZE];

  *ppxIdleTaskTCBBuffer   = &Idle_TCB;
 8001ddc:	4b04      	ldr	r3, [pc, #16]	@ (8001df0 <vApplicationGetIdleTaskMemory+0x14>)
__WEAK void vApplicationGetIdleTaskMemory (StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize) {
 8001dde:	b410      	push	{r4}
  *ppxIdleTaskTCBBuffer   = &Idle_TCB;
 8001de0:	6003      	str	r3, [r0, #0]
  *ppxIdleTaskStackBuffer = &Idle_Stack[0];
 8001de2:	4c04      	ldr	r4, [pc, #16]	@ (8001df4 <vApplicationGetIdleTaskMemory+0x18>)
 8001de4:	600c      	str	r4, [r1, #0]
  *pulIdleTaskStackSize   = (uint32_t)configMINIMAL_STACK_SIZE;
 8001de6:	2380      	movs	r3, #128	@ 0x80
}
 8001de8:	f85d 4b04 	ldr.w	r4, [sp], #4
  *pulIdleTaskStackSize   = (uint32_t)configMINIMAL_STACK_SIZE;
 8001dec:	6013      	str	r3, [r2, #0]
}
 8001dee:	4770      	bx	lr
 8001df0:	20000784 	.word	0x20000784
 8001df4:	20000584 	.word	0x20000584

08001df8 <vApplicationGetTimerTaskMemory>:
__WEAK void vApplicationGetTimerTaskMemory (StaticTask_t **ppxTimerTaskTCBBuffer, StackType_t **ppxTimerTaskStackBuffer, uint32_t *pulTimerTaskStackSize) {
  /* Timer task control block and stack */
  static StaticTask_t Timer_TCB;
  static StackType_t  Timer_Stack[configTIMER_TASK_STACK_DEPTH];

  *ppxTimerTaskTCBBuffer   = &Timer_TCB;
 8001df8:	4b05      	ldr	r3, [pc, #20]	@ (8001e10 <vApplicationGetTimerTaskMemory+0x18>)
__WEAK void vApplicationGetTimerTaskMemory (StaticTask_t **ppxTimerTaskTCBBuffer, StackType_t **ppxTimerTaskStackBuffer, uint32_t *pulTimerTaskStackSize) {
 8001dfa:	b410      	push	{r4}
  *ppxTimerTaskTCBBuffer   = &Timer_TCB;
 8001dfc:	6003      	str	r3, [r0, #0]
  *ppxTimerTaskStackBuffer = &Timer_Stack[0];
 8001dfe:	4c05      	ldr	r4, [pc, #20]	@ (8001e14 <vApplicationGetTimerTaskMemory+0x1c>)
 8001e00:	600c      	str	r4, [r1, #0]
  *pulTimerTaskStackSize   = (uint32_t)configTIMER_TASK_STACK_DEPTH;
 8001e02:	f44f 7380 	mov.w	r3, #256	@ 0x100
}
 8001e06:	f85d 4b04 	ldr.w	r4, [sp], #4
  *pulTimerTaskStackSize   = (uint32_t)configTIMER_TASK_STACK_DEPTH;
 8001e0a:	6013      	str	r3, [r2, #0]
}
 8001e0c:	4770      	bx	lr
 8001e0e:	bf00      	nop
 8001e10:	20000528 	.word	0x20000528
 8001e14:	20000128 	.word	0x20000128

08001e18 <vListInitialise>:
void vListInitialise( List_t * const pxList )
{
	/* The list structure contains a list item which is used to mark the
	end of the list.  To initialise the list the list end is inserted
	as the only list entry. */
	pxList->pxIndex = ( ListItem_t * ) &( pxList->xListEnd );			/*lint !e826 !e740 !e9087 The mini list structure is used as the list end to save RAM.  This is checked and valid. */
 8001e18:	f100 0308 	add.w	r3, r0, #8

	/* The list end value is the highest possible value in the list to
	ensure it remains at the end of the list. */
	pxList->xListEnd.xItemValue = portMAX_DELAY;
 8001e1c:	f04f 31ff 	mov.w	r1, #**********
	/* The list end next and previous pointers point to itself so we know
	when the list is empty. */
	pxList->xListEnd.pxNext = ( ListItem_t * ) &( pxList->xListEnd );	/*lint !e826 !e740 !e9087 The mini list structure is used as the list end to save RAM.  This is checked and valid. */
	pxList->xListEnd.pxPrevious = ( ListItem_t * ) &( pxList->xListEnd );/*lint !e826 !e740 !e9087 The mini list structure is used as the list end to save RAM.  This is checked and valid. */

	pxList->uxNumberOfItems = ( UBaseType_t ) 0U;
 8001e20:	2200      	movs	r2, #0
	pxList->xListEnd.xItemValue = portMAX_DELAY;
 8001e22:	e9c0 3101 	strd	r3, r1, [r0, #4]
	pxList->xListEnd.pxNext = ( ListItem_t * ) &( pxList->xListEnd );	/*lint !e826 !e740 !e9087 The mini list structure is used as the list end to save RAM.  This is checked and valid. */
 8001e26:	e9c0 3303 	strd	r3, r3, [r0, #12]
	pxList->uxNumberOfItems = ( UBaseType_t ) 0U;
 8001e2a:	6002      	str	r2, [r0, #0]

	/* Write known values into the list if
	configUSE_LIST_DATA_INTEGRITY_CHECK_BYTES is set to 1. */
	listSET_LIST_INTEGRITY_CHECK_1_VALUE( pxList );
	listSET_LIST_INTEGRITY_CHECK_2_VALUE( pxList );
}
 8001e2c:	4770      	bx	lr
 8001e2e:	bf00      	nop

08001e30 <vListInitialiseItem>:
/*-----------------------------------------------------------*/

void vListInitialiseItem( ListItem_t * const pxItem )
{
	/* Make sure the list item is not recorded as being on a list. */
	pxItem->pxContainer = NULL;
 8001e30:	2300      	movs	r3, #0
 8001e32:	6103      	str	r3, [r0, #16]

	/* Write known values into the list item if
	configUSE_LIST_DATA_INTEGRITY_CHECK_BYTES is set to 1. */
	listSET_FIRST_LIST_ITEM_INTEGRITY_CHECK_VALUE( pxItem );
	listSET_SECOND_LIST_ITEM_INTEGRITY_CHECK_VALUE( pxItem );
}
 8001e34:	4770      	bx	lr
 8001e36:	bf00      	nop

08001e38 <vListInsertEnd>:
/*-----------------------------------------------------------*/

void vListInsertEnd( List_t * const pxList, ListItem_t * const pxNewListItem )
{
ListItem_t * const pxIndex = pxList->pxIndex;
 8001e38:	6842      	ldr	r2, [r0, #4]
	pxIndex->pxPrevious = pxNewListItem;

	/* Remember which list the item is in. */
	pxNewListItem->pxContainer = pxList;

	( pxList->uxNumberOfItems )++;
 8001e3a:	6803      	ldr	r3, [r0, #0]
{
 8001e3c:	b410      	push	{r4}
	pxNewListItem->pxPrevious = pxIndex->pxPrevious;
 8001e3e:	6894      	ldr	r4, [r2, #8]
	( pxList->uxNumberOfItems )++;
 8001e40:	3301      	adds	r3, #1
	pxNewListItem->pxPrevious = pxIndex->pxPrevious;
 8001e42:	e9c1 2401 	strd	r2, r4, [r1, #4]
	pxIndex->pxPrevious->pxNext = pxNewListItem;
 8001e46:	6061      	str	r1, [r4, #4]
	pxIndex->pxPrevious = pxNewListItem;
 8001e48:	6091      	str	r1, [r2, #8]
}
 8001e4a:	f85d 4b04 	ldr.w	r4, [sp], #4
	pxNewListItem->pxContainer = pxList;
 8001e4e:	6108      	str	r0, [r1, #16]
	( pxList->uxNumberOfItems )++;
 8001e50:	6003      	str	r3, [r0, #0]
}
 8001e52:	4770      	bx	lr

08001e54 <vListInsert>:
/*-----------------------------------------------------------*/

void vListInsert( List_t * const pxList, ListItem_t * const pxNewListItem )
{
 8001e54:	b430      	push	{r4, r5}
ListItem_t *pxIterator;
const TickType_t xValueOfInsertion = pxNewListItem->xItemValue;
 8001e56:	680d      	ldr	r5, [r1, #0]
	new list item should be placed after it.  This ensures that TCBs which are
	stored in ready lists (all of which have the same xItemValue value) get a
	share of the CPU.  However, if the xItemValue is the same as the back marker
	the iteration loop below will not end.  Therefore the value is checked
	first, and the algorithm slightly modified if necessary. */
	if( xValueOfInsertion == portMAX_DELAY )
 8001e58:	1c6b      	adds	r3, r5, #1
 8001e5a:	d010      	beq.n	8001e7e <vListInsert+0x2a>
			4) Using a queue or semaphore before it has been initialised or
			   before the scheduler has been started (are interrupts firing
			   before vTaskStartScheduler() has been called?).
		**********************************************************************/

		for( pxIterator = ( ListItem_t * ) &( pxList->xListEnd ); pxIterator->pxNext->xItemValue <= xValueOfInsertion; pxIterator = pxIterator->pxNext ) /*lint !e826 !e740 !e9087 The mini list structure is used as the list end to save RAM.  This is checked and valid. *//*lint !e440 The iterator moves to a different value, not xValueOfInsertion. */
 8001e5c:	f100 0308 	add.w	r3, r0, #8
 8001e60:	461c      	mov	r4, r3
 8001e62:	685b      	ldr	r3, [r3, #4]
 8001e64:	681a      	ldr	r2, [r3, #0]
 8001e66:	42aa      	cmp	r2, r5
 8001e68:	d9fa      	bls.n	8001e60 <vListInsert+0xc>

	/* Remember which list the item is in.  This allows fast removal of the
	item later. */
	pxNewListItem->pxContainer = pxList;

	( pxList->uxNumberOfItems )++;
 8001e6a:	6802      	ldr	r2, [r0, #0]
	pxNewListItem->pxNext = pxIterator->pxNext;
 8001e6c:	604b      	str	r3, [r1, #4]
	( pxList->uxNumberOfItems )++;
 8001e6e:	3201      	adds	r2, #1
	pxNewListItem->pxNext->pxPrevious = pxNewListItem;
 8001e70:	6099      	str	r1, [r3, #8]
	pxNewListItem->pxPrevious = pxIterator;
 8001e72:	608c      	str	r4, [r1, #8]
	pxIterator->pxNext = pxNewListItem;
 8001e74:	6061      	str	r1, [r4, #4]
	pxNewListItem->pxContainer = pxList;
 8001e76:	6108      	str	r0, [r1, #16]
}
 8001e78:	bc30      	pop	{r4, r5}
	( pxList->uxNumberOfItems )++;
 8001e7a:	6002      	str	r2, [r0, #0]
}
 8001e7c:	4770      	bx	lr
		pxIterator = pxList->xListEnd.pxPrevious;
 8001e7e:	6904      	ldr	r4, [r0, #16]
	pxNewListItem->pxNext = pxIterator->pxNext;
 8001e80:	6863      	ldr	r3, [r4, #4]
 8001e82:	e7f2      	b.n	8001e6a <vListInsert+0x16>

08001e84 <uxListRemove>:

UBaseType_t uxListRemove( ListItem_t * const pxItemToRemove )
{
/* The list item knows which list it is in.  Obtain the list from the list
item. */
List_t * const pxList = pxItemToRemove->pxContainer;
 8001e84:	6903      	ldr	r3, [r0, #16]
{
 8001e86:	b410      	push	{r4}

	pxItemToRemove->pxNext->pxPrevious = pxItemToRemove->pxPrevious;
 8001e88:	e9d0 1201 	ldrd	r1, r2, [r0, #4]

	/* Only used during decision coverage testing. */
	mtCOVERAGE_TEST_DELAY();

	/* Make sure the index is left pointing to a valid item. */
	if( pxList->pxIndex == pxItemToRemove )
 8001e8c:	685c      	ldr	r4, [r3, #4]
	pxItemToRemove->pxNext->pxPrevious = pxItemToRemove->pxPrevious;
 8001e8e:	608a      	str	r2, [r1, #8]
	if( pxList->pxIndex == pxItemToRemove )
 8001e90:	4284      	cmp	r4, r0
	pxItemToRemove->pxPrevious->pxNext = pxItemToRemove->pxNext;
 8001e92:	6051      	str	r1, [r2, #4]
	{
		pxList->pxIndex = pxItemToRemove->pxPrevious;
 8001e94:	bf08      	it	eq
 8001e96:	605a      	streq	r2, [r3, #4]
	{
		mtCOVERAGE_TEST_MARKER();
	}

	pxItemToRemove->pxContainer = NULL;
	( pxList->uxNumberOfItems )--;
 8001e98:	681a      	ldr	r2, [r3, #0]

	return pxList->uxNumberOfItems;
}
 8001e9a:	f85d 4b04 	ldr.w	r4, [sp], #4
	pxItemToRemove->pxContainer = NULL;
 8001e9e:	2100      	movs	r1, #0
	( pxList->uxNumberOfItems )--;
 8001ea0:	3a01      	subs	r2, #1
	pxItemToRemove->pxContainer = NULL;
 8001ea2:	6101      	str	r1, [r0, #16]
	( pxList->uxNumberOfItems )--;
 8001ea4:	601a      	str	r2, [r3, #0]
	return pxList->uxNumberOfItems;
 8001ea6:	6818      	ldr	r0, [r3, #0]
}
 8001ea8:	4770      	bx	lr
 8001eaa:	bf00      	nop

08001eac <prvCopyDataToQueue>:

	/* This function is called from a critical section. */

	uxMessagesWaiting = pxQueue->uxMessagesWaiting;

	if( pxQueue->uxItemSize == ( UBaseType_t ) 0 )
 8001eac:	6c03      	ldr	r3, [r0, #64]	@ 0x40
{
 8001eae:	b570      	push	{r4, r5, r6, lr}
	uxMessagesWaiting = pxQueue->uxMessagesWaiting;
 8001eb0:	6b85      	ldr	r5, [r0, #56]	@ 0x38
{
 8001eb2:	4604      	mov	r4, r0
	if( pxQueue->uxItemSize == ( UBaseType_t ) 0 )
 8001eb4:	b92b      	cbnz	r3, 8001ec2 <prvCopyDataToQueue+0x16>
	{
		#if ( configUSE_MUTEXES == 1 )
		{
			if( pxQueue->uxQueueType == queueQUEUE_IS_MUTEX )
 8001eb6:	6806      	ldr	r6, [r0, #0]
		{
			mtCOVERAGE_TEST_MARKER();
		}
	}

	pxQueue->uxMessagesWaiting = uxMessagesWaiting + ( UBaseType_t ) 1;
 8001eb8:	3501      	adds	r5, #1
			if( pxQueue->uxQueueType == queueQUEUE_IS_MUTEX )
 8001eba:	b346      	cbz	r6, 8001f0e <prvCopyDataToQueue+0x62>
BaseType_t xReturn = pdFALSE;
 8001ebc:	4618      	mov	r0, r3
	pxQueue->uxMessagesWaiting = uxMessagesWaiting + ( UBaseType_t ) 1;
 8001ebe:	63a5      	str	r5, [r4, #56]	@ 0x38

	return xReturn;
}
 8001ec0:	bd70      	pop	{r4, r5, r6, pc}
	else if( xPosition == queueSEND_TO_BACK )
 8001ec2:	4616      	mov	r6, r2
 8001ec4:	b982      	cbnz	r2, 8001ee8 <prvCopyDataToQueue+0x3c>
		( void ) memcpy( ( void * ) pxQueue->pcWriteTo, pvItemToQueue, ( size_t ) pxQueue->uxItemSize ); /*lint !e961 !e418 !e9087 MISRA exception as the casts are only redundant for some ports, plus previous logic ensures a null pointer can only be passed to memcpy() if the copy size is 0.  Cast to void required by function signature and safe as no alignment requirement and copy length specified in bytes. */
 8001ec6:	461a      	mov	r2, r3
 8001ec8:	6840      	ldr	r0, [r0, #4]
 8001eca:	f001 fd1d 	bl	8003908 <memcpy>
		pxQueue->pcWriteTo += pxQueue->uxItemSize; /*lint !e9016 Pointer arithmetic on char types ok, especially in this use case where it is the clearest way of conveying intent. */
 8001ece:	6863      	ldr	r3, [r4, #4]
 8001ed0:	6c21      	ldr	r1, [r4, #64]	@ 0x40
		if( pxQueue->pcWriteTo >= pxQueue->u.xQueue.pcTail ) /*lint !e946 MISRA exception justified as comparison of pointers is the cleanest solution. */
 8001ed2:	68a2      	ldr	r2, [r4, #8]
		pxQueue->pcWriteTo += pxQueue->uxItemSize; /*lint !e9016 Pointer arithmetic on char types ok, especially in this use case where it is the clearest way of conveying intent. */
 8001ed4:	440b      	add	r3, r1
		if( pxQueue->pcWriteTo >= pxQueue->u.xQueue.pcTail ) /*lint !e946 MISRA exception justified as comparison of pointers is the cleanest solution. */
 8001ed6:	4293      	cmp	r3, r2
		pxQueue->pcWriteTo += pxQueue->uxItemSize; /*lint !e9016 Pointer arithmetic on char types ok, especially in this use case where it is the clearest way of conveying intent. */
 8001ed8:	6063      	str	r3, [r4, #4]
			pxQueue->pcWriteTo = pxQueue->pcHead;
 8001eda:	bf24      	itt	cs
 8001edc:	6823      	ldrcs	r3, [r4, #0]
 8001ede:	6063      	strcs	r3, [r4, #4]
	pxQueue->uxMessagesWaiting = uxMessagesWaiting + ( UBaseType_t ) 1;
 8001ee0:	3501      	adds	r5, #1
BaseType_t xReturn = pdFALSE;
 8001ee2:	2000      	movs	r0, #0
	pxQueue->uxMessagesWaiting = uxMessagesWaiting + ( UBaseType_t ) 1;
 8001ee4:	63a5      	str	r5, [r4, #56]	@ 0x38
}
 8001ee6:	bd70      	pop	{r4, r5, r6, pc}
		( void ) memcpy( ( void * ) pxQueue->u.xQueue.pcReadFrom, pvItemToQueue, ( size_t ) pxQueue->uxItemSize ); /*lint !e961 !e9087 !e418 MISRA exception as the casts are only redundant for some ports.  Cast to void required by function signature and safe as no alignment requirement and copy length specified in bytes.  Assert checks null pointer only used when length is 0. */
 8001ee8:	461a      	mov	r2, r3
 8001eea:	68c0      	ldr	r0, [r0, #12]
 8001eec:	f001 fd0c 	bl	8003908 <memcpy>
		pxQueue->u.xQueue.pcReadFrom -= pxQueue->uxItemSize;
 8001ef0:	6c22      	ldr	r2, [r4, #64]	@ 0x40
 8001ef2:	68e3      	ldr	r3, [r4, #12]
 8001ef4:	4251      	negs	r1, r2
 8001ef6:	1a9b      	subs	r3, r3, r2
		if( pxQueue->u.xQueue.pcReadFrom < pxQueue->pcHead ) /*lint !e946 MISRA exception justified as comparison of pointers is the cleanest solution. */
 8001ef8:	6822      	ldr	r2, [r4, #0]
		pxQueue->u.xQueue.pcReadFrom -= pxQueue->uxItemSize;
 8001efa:	60e3      	str	r3, [r4, #12]
		if( pxQueue->u.xQueue.pcReadFrom < pxQueue->pcHead ) /*lint !e946 MISRA exception justified as comparison of pointers is the cleanest solution. */
 8001efc:	4293      	cmp	r3, r2
 8001efe:	d202      	bcs.n	8001f06 <prvCopyDataToQueue+0x5a>
			pxQueue->u.xQueue.pcReadFrom = ( pxQueue->u.xQueue.pcTail - pxQueue->uxItemSize );
 8001f00:	68a3      	ldr	r3, [r4, #8]
 8001f02:	440b      	add	r3, r1
 8001f04:	60e3      	str	r3, [r4, #12]
		if( xPosition == queueOVERWRITE )
 8001f06:	2e02      	cmp	r6, #2
 8001f08:	d006      	beq.n	8001f18 <prvCopyDataToQueue+0x6c>
	pxQueue->uxMessagesWaiting = uxMessagesWaiting + ( UBaseType_t ) 1;
 8001f0a:	3501      	adds	r5, #1
 8001f0c:	e7e9      	b.n	8001ee2 <prvCopyDataToQueue+0x36>
				xReturn = xTaskPriorityDisinherit( pxQueue->u.xSemaphore.xMutexHolder );
 8001f0e:	6880      	ldr	r0, [r0, #8]
 8001f10:	f000 ffce 	bl	8002eb0 <xTaskPriorityDisinherit>
				pxQueue->u.xSemaphore.xMutexHolder = NULL;
 8001f14:	60a6      	str	r6, [r4, #8]
 8001f16:	e7d2      	b.n	8001ebe <prvCopyDataToQueue+0x12>
			if( uxMessagesWaiting > ( UBaseType_t ) 0 )
 8001f18:	2d01      	cmp	r5, #1
 8001f1a:	bf38      	it	cc
 8001f1c:	2501      	movcc	r5, #1
BaseType_t xReturn = pdFALSE;
 8001f1e:	2000      	movs	r0, #0
 8001f20:	e7cd      	b.n	8001ebe <prvCopyDataToQueue+0x12>
 8001f22:	bf00      	nop

08001f24 <prvUnlockQueue>:
	}
}
/*-----------------------------------------------------------*/

static void prvUnlockQueue( Queue_t * const pxQueue )
{
 8001f24:	b570      	push	{r4, r5, r6, lr}
 8001f26:	4605      	mov	r5, r0

	/* The lock counts contains the number of extra data items placed or
	removed from the queue while the queue was locked.  When a queue is
	locked items can be added or removed, but the event lists cannot be
	updated. */
	taskENTER_CRITICAL();
 8001f28:	f001 fa74 	bl	8003414 <vPortEnterCritical>
	{
		int8_t cTxLock = pxQueue->cTxLock;
 8001f2c:	f895 3045 	ldrb.w	r3, [r5, #69]	@ 0x45
 8001f30:	b25c      	sxtb	r4, r3

		/* See if data was added to the queue while it was locked. */
		while( cTxLock > queueLOCKED_UNMODIFIED )
 8001f32:	2c00      	cmp	r4, #0
 8001f34:	dd14      	ble.n	8001f60 <prvUnlockQueue+0x3c>
			{
				/* Tasks that are removed from the event list will get added to
				the pending ready list as the scheduler is still suspended. */
				if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToReceive ) ) == pdFALSE )
				{
					if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToReceive ) ) != pdFALSE )
 8001f36:	f105 0624 	add.w	r6, r5, #36	@ 0x24
 8001f3a:	e003      	b.n	8001f44 <prvUnlockQueue+0x20>
					break;
				}
			}
			#endif /* configUSE_QUEUE_SETS */

			--cTxLock;
 8001f3c:	1e63      	subs	r3, r4, #1
 8001f3e:	b2da      	uxtb	r2, r3
 8001f40:	b25c      	sxtb	r4, r3
		while( cTxLock > queueLOCKED_UNMODIFIED )
 8001f42:	b16a      	cbz	r2, 8001f60 <prvUnlockQueue+0x3c>
				if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToReceive ) ) == pdFALSE )
 8001f44:	6a6b      	ldr	r3, [r5, #36]	@ 0x24
					if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToReceive ) ) != pdFALSE )
 8001f46:	4630      	mov	r0, r6
				if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToReceive ) ) == pdFALSE )
 8001f48:	b153      	cbz	r3, 8001f60 <prvUnlockQueue+0x3c>
					if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToReceive ) ) != pdFALSE )
 8001f4a:	f000 ff09 	bl	8002d60 <xTaskRemoveFromEventList>
 8001f4e:	2800      	cmp	r0, #0
 8001f50:	d0f4      	beq.n	8001f3c <prvUnlockQueue+0x18>
						vTaskMissedYield();
 8001f52:	f000 ff97 	bl	8002e84 <vTaskMissedYield>
			--cTxLock;
 8001f56:	1e63      	subs	r3, r4, #1
 8001f58:	b2da      	uxtb	r2, r3
 8001f5a:	b25c      	sxtb	r4, r3
		while( cTxLock > queueLOCKED_UNMODIFIED )
 8001f5c:	2a00      	cmp	r2, #0
 8001f5e:	d1f1      	bne.n	8001f44 <prvUnlockQueue+0x20>
		}

		pxQueue->cTxLock = queueUNLOCKED;
 8001f60:	23ff      	movs	r3, #255	@ 0xff
 8001f62:	f885 3045 	strb.w	r3, [r5, #69]	@ 0x45
	}
	taskEXIT_CRITICAL();
 8001f66:	f001 fa77 	bl	8003458 <vPortExitCritical>

	/* Do the same for the Rx lock. */
	taskENTER_CRITICAL();
 8001f6a:	f001 fa53 	bl	8003414 <vPortEnterCritical>
	{
		int8_t cRxLock = pxQueue->cRxLock;
 8001f6e:	f895 3044 	ldrb.w	r3, [r5, #68]	@ 0x44
 8001f72:	b25c      	sxtb	r4, r3

		while( cRxLock > queueLOCKED_UNMODIFIED )
 8001f74:	2c00      	cmp	r4, #0
 8001f76:	dd14      	ble.n	8001fa2 <prvUnlockQueue+0x7e>
		{
			if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToSend ) ) == pdFALSE )
			{
				if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToSend ) ) != pdFALSE )
 8001f78:	f105 0610 	add.w	r6, r5, #16
 8001f7c:	e003      	b.n	8001f86 <prvUnlockQueue+0x62>
				else
				{
					mtCOVERAGE_TEST_MARKER();
				}

				--cRxLock;
 8001f7e:	1e63      	subs	r3, r4, #1
 8001f80:	b2da      	uxtb	r2, r3
 8001f82:	b25c      	sxtb	r4, r3
		while( cRxLock > queueLOCKED_UNMODIFIED )
 8001f84:	b16a      	cbz	r2, 8001fa2 <prvUnlockQueue+0x7e>
			if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToSend ) ) == pdFALSE )
 8001f86:	692b      	ldr	r3, [r5, #16]
				if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToSend ) ) != pdFALSE )
 8001f88:	4630      	mov	r0, r6
			if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToSend ) ) == pdFALSE )
 8001f8a:	b153      	cbz	r3, 8001fa2 <prvUnlockQueue+0x7e>
				if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToSend ) ) != pdFALSE )
 8001f8c:	f000 fee8 	bl	8002d60 <xTaskRemoveFromEventList>
 8001f90:	2800      	cmp	r0, #0
 8001f92:	d0f4      	beq.n	8001f7e <prvUnlockQueue+0x5a>
					vTaskMissedYield();
 8001f94:	f000 ff76 	bl	8002e84 <vTaskMissedYield>
				--cRxLock;
 8001f98:	1e63      	subs	r3, r4, #1
 8001f9a:	b2da      	uxtb	r2, r3
 8001f9c:	b25c      	sxtb	r4, r3
		while( cRxLock > queueLOCKED_UNMODIFIED )
 8001f9e:	2a00      	cmp	r2, #0
 8001fa0:	d1f1      	bne.n	8001f86 <prvUnlockQueue+0x62>
			{
				break;
			}
		}

		pxQueue->cRxLock = queueUNLOCKED;
 8001fa2:	23ff      	movs	r3, #255	@ 0xff
 8001fa4:	f885 3044 	strb.w	r3, [r5, #68]	@ 0x44
	}
	taskEXIT_CRITICAL();
}
 8001fa8:	e8bd 4070 	ldmia.w	sp!, {r4, r5, r6, lr}
	taskEXIT_CRITICAL();
 8001fac:	f001 ba54 	b.w	8003458 <vPortExitCritical>

08001fb0 <xQueueGenericReset>:
{
 8001fb0:	b538      	push	{r3, r4, r5, lr}
	configASSERT( pxQueue );
 8001fb2:	b1e0      	cbz	r0, 8001fee <xQueueGenericReset+0x3e>
 8001fb4:	4604      	mov	r4, r0
 8001fb6:	460d      	mov	r5, r1
	taskENTER_CRITICAL();
 8001fb8:	f001 fa2c 	bl	8003414 <vPortEnterCritical>
		pxQueue->u.xQueue.pcTail = pxQueue->pcHead + ( pxQueue->uxLength * pxQueue->uxItemSize ); /*lint !e9016 Pointer arithmetic allowed on char types, especially when it assists conveying intent. */
 8001fbc:	e9d4 230f 	ldrd	r2, r3, [r4, #60]	@ 0x3c
 8001fc0:	6821      	ldr	r1, [r4, #0]
		pxQueue->pcWriteTo = pxQueue->pcHead;
 8001fc2:	6061      	str	r1, [r4, #4]
		pxQueue->u.xQueue.pcTail = pxQueue->pcHead + ( pxQueue->uxLength * pxQueue->uxItemSize ); /*lint !e9016 Pointer arithmetic allowed on char types, especially when it assists conveying intent. */
 8001fc4:	fb03 f202 	mul.w	r2, r3, r2
		pxQueue->u.xQueue.pcReadFrom = pxQueue->pcHead + ( ( pxQueue->uxLength - 1U ) * pxQueue->uxItemSize ); /*lint !e9016 Pointer arithmetic allowed on char types, especially when it assists conveying intent. */
 8001fc8:	1ad3      	subs	r3, r2, r3
		pxQueue->u.xQueue.pcTail = pxQueue->pcHead + ( pxQueue->uxLength * pxQueue->uxItemSize ); /*lint !e9016 Pointer arithmetic allowed on char types, especially when it assists conveying intent. */
 8001fca:	440a      	add	r2, r1
 8001fcc:	60a2      	str	r2, [r4, #8]
		pxQueue->uxMessagesWaiting = ( UBaseType_t ) 0U;
 8001fce:	2200      	movs	r2, #0
 8001fd0:	63a2      	str	r2, [r4, #56]	@ 0x38
		pxQueue->u.xQueue.pcReadFrom = pxQueue->pcHead + ( ( pxQueue->uxLength - 1U ) * pxQueue->uxItemSize ); /*lint !e9016 Pointer arithmetic allowed on char types, especially when it assists conveying intent. */
 8001fd2:	440b      	add	r3, r1
		pxQueue->cRxLock = queueUNLOCKED;
 8001fd4:	22ff      	movs	r2, #255	@ 0xff
 8001fd6:	f884 2044 	strb.w	r2, [r4, #68]	@ 0x44
		pxQueue->u.xQueue.pcReadFrom = pxQueue->pcHead + ( ( pxQueue->uxLength - 1U ) * pxQueue->uxItemSize ); /*lint !e9016 Pointer arithmetic allowed on char types, especially when it assists conveying intent. */
 8001fda:	60e3      	str	r3, [r4, #12]
		pxQueue->cTxLock = queueUNLOCKED;
 8001fdc:	f884 2045 	strb.w	r2, [r4, #69]	@ 0x45
		if( xNewQueue == pdFALSE )
 8001fe0:	b975      	cbnz	r5, 8002000 <xQueueGenericReset+0x50>
			if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToSend ) ) == pdFALSE )
 8001fe2:	6923      	ldr	r3, [r4, #16]
 8001fe4:	b9c3      	cbnz	r3, 8002018 <xQueueGenericReset+0x68>
	taskEXIT_CRITICAL();
 8001fe6:	f001 fa37 	bl	8003458 <vPortExitCritical>
}
 8001fea:	2001      	movs	r0, #1
 8001fec:	bd38      	pop	{r3, r4, r5, pc}

portFORCE_INLINE static void vPortRaiseBASEPRI( void )
{
uint32_t ulNewBASEPRI;

	__asm volatile
 8001fee:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8001ff2:	f383 8811 	msr	BASEPRI, r3
 8001ff6:	f3bf 8f6f 	isb	sy
 8001ffa:	f3bf 8f4f 	dsb	sy
	configASSERT( pxQueue );
 8001ffe:	e7fe      	b.n	8001ffe <xQueueGenericReset+0x4e>
			vListInitialise( &( pxQueue->xTasksWaitingToSend ) );
 8002000:	f104 0010 	add.w	r0, r4, #16
 8002004:	f7ff ff08 	bl	8001e18 <vListInitialise>
			vListInitialise( &( pxQueue->xTasksWaitingToReceive ) );
 8002008:	f104 0024 	add.w	r0, r4, #36	@ 0x24
 800200c:	f7ff ff04 	bl	8001e18 <vListInitialise>
	taskEXIT_CRITICAL();
 8002010:	f001 fa22 	bl	8003458 <vPortExitCritical>
}
 8002014:	2001      	movs	r0, #1
 8002016:	bd38      	pop	{r3, r4, r5, pc}
				if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToSend ) ) != pdFALSE )
 8002018:	f104 0010 	add.w	r0, r4, #16
 800201c:	f000 fea0 	bl	8002d60 <xTaskRemoveFromEventList>
 8002020:	2800      	cmp	r0, #0
 8002022:	d0e0      	beq.n	8001fe6 <xQueueGenericReset+0x36>
					queueYIELD_IF_USING_PREEMPTION();
 8002024:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 8002028:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 800202c:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
 8002030:	f3bf 8f4f 	dsb	sy
 8002034:	f3bf 8f6f 	isb	sy
 8002038:	e7d5      	b.n	8001fe6 <xQueueGenericReset+0x36>
 800203a:	bf00      	nop

0800203c <xQueueGenericCreateStatic>:
	{
 800203c:	b530      	push	{r4, r5, lr}
 800203e:	b085      	sub	sp, #20
 8002040:	f89d 4020 	ldrb.w	r4, [sp, #32]
		configASSERT( uxQueueLength > ( UBaseType_t ) 0 );
 8002044:	b940      	cbnz	r0, 8002058 <xQueueGenericCreateStatic+0x1c>
 8002046:	f04f 0350 	mov.w	r3, #80	@ 0x50
 800204a:	f383 8811 	msr	BASEPRI, r3
 800204e:	f3bf 8f6f 	isb	sy
 8002052:	f3bf 8f4f 	dsb	sy
 8002056:	e7fe      	b.n	8002056 <xQueueGenericCreateStatic+0x1a>
		configASSERT( pxStaticQueue != NULL );
 8002058:	b17b      	cbz	r3, 800207a <xQueueGenericCreateStatic+0x3e>
		configASSERT( !( ( pucQueueStorage != NULL ) && ( uxItemSize == 0 ) ) );
 800205a:	b38a      	cbz	r2, 80020c0 <xQueueGenericCreateStatic+0x84>
 800205c:	b1b1      	cbz	r1, 800208c <xQueueGenericCreateStatic+0x50>
			volatile size_t xSize = sizeof( StaticQueue_t );
 800205e:	2550      	movs	r5, #80	@ 0x50
 8002060:	9503      	str	r5, [sp, #12]
			configASSERT( xSize == sizeof( Queue_t ) );
 8002062:	9d03      	ldr	r5, [sp, #12]
 8002064:	2d50      	cmp	r5, #80	@ 0x50
 8002066:	d01a      	beq.n	800209e <xQueueGenericCreateStatic+0x62>
 8002068:	f04f 0350 	mov.w	r3, #80	@ 0x50
 800206c:	f383 8811 	msr	BASEPRI, r3
 8002070:	f3bf 8f6f 	isb	sy
 8002074:	f3bf 8f4f 	dsb	sy
 8002078:	e7fe      	b.n	8002078 <xQueueGenericCreateStatic+0x3c>
 800207a:	f04f 0350 	mov.w	r3, #80	@ 0x50
 800207e:	f383 8811 	msr	BASEPRI, r3
 8002082:	f3bf 8f6f 	isb	sy
 8002086:	f3bf 8f4f 	dsb	sy
		configASSERT( pxStaticQueue != NULL );
 800208a:	e7fe      	b.n	800208a <xQueueGenericCreateStatic+0x4e>
 800208c:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002090:	f383 8811 	msr	BASEPRI, r3
 8002094:	f3bf 8f6f 	isb	sy
 8002098:	f3bf 8f4f 	dsb	sy
		configASSERT( !( ( pucQueueStorage != NULL ) && ( uxItemSize == 0 ) ) );
 800209c:	e7fe      	b.n	800209c <xQueueGenericCreateStatic+0x60>
			( void ) xSize; /* Keeps lint quiet when configASSERT() is not defined. */
 800209e:	9d03      	ldr	r5, [sp, #12]
		pxNewQueue->pcHead = ( int8_t * ) pxNewQueue;
 80020a0:	601a      	str	r2, [r3, #0]
	pxNewQueue->uxItemSize = uxItemSize;
 80020a2:	e9c3 010f 	strd	r0, r1, [r3, #60]	@ 0x3c
				pxNewQueue->ucStaticallyAllocated = pdTRUE;
 80020a6:	2101      	movs	r1, #1
 80020a8:	f883 1046 	strb.w	r1, [r3, #70]	@ 0x46
	( void ) xQueueGenericReset( pxNewQueue, pdTRUE );
 80020ac:	4618      	mov	r0, r3
				pxNewQueue->ucStaticallyAllocated = pdTRUE;
 80020ae:	9301      	str	r3, [sp, #4]
	( void ) xQueueGenericReset( pxNewQueue, pdTRUE );
 80020b0:	f7ff ff7e 	bl	8001fb0 <xQueueGenericReset>
		pxNewQueue->ucQueueType = ucQueueType;
 80020b4:	9b01      	ldr	r3, [sp, #4]
	}
 80020b6:	4618      	mov	r0, r3
		pxNewQueue->ucQueueType = ucQueueType;
 80020b8:	f883 404c 	strb.w	r4, [r3, #76]	@ 0x4c
	}
 80020bc:	b005      	add	sp, #20
 80020be:	bd30      	pop	{r4, r5, pc}
		configASSERT( !( ( pucQueueStorage == NULL ) && ( uxItemSize != 0 ) ) );
 80020c0:	b939      	cbnz	r1, 80020d2 <xQueueGenericCreateStatic+0x96>
			volatile size_t xSize = sizeof( StaticQueue_t );
 80020c2:	2250      	movs	r2, #80	@ 0x50
 80020c4:	9203      	str	r2, [sp, #12]
			configASSERT( xSize == sizeof( Queue_t ) );
 80020c6:	9a03      	ldr	r2, [sp, #12]
 80020c8:	2a50      	cmp	r2, #80	@ 0x50
 80020ca:	d1cd      	bne.n	8002068 <xQueueGenericCreateStatic+0x2c>
			( void ) xSize; /* Keeps lint quiet when configASSERT() is not defined. */
 80020cc:	9a03      	ldr	r2, [sp, #12]
		pxNewQueue->pcHead = ( int8_t * ) pxNewQueue;
 80020ce:	461a      	mov	r2, r3
 80020d0:	e7e6      	b.n	80020a0 <xQueueGenericCreateStatic+0x64>
 80020d2:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80020d6:	f383 8811 	msr	BASEPRI, r3
 80020da:	f3bf 8f6f 	isb	sy
 80020de:	f3bf 8f4f 	dsb	sy
		configASSERT( !( ( pucQueueStorage == NULL ) && ( uxItemSize != 0 ) ) );
 80020e2:	e7fe      	b.n	80020e2 <xQueueGenericCreateStatic+0xa6>

080020e4 <xQueueGenericSend>:
{
 80020e4:	b5f0      	push	{r4, r5, r6, r7, lr}
 80020e6:	b085      	sub	sp, #20
 80020e8:	9201      	str	r2, [sp, #4]
	configASSERT( pxQueue );
 80020ea:	2800      	cmp	r0, #0
 80020ec:	d07e      	beq.n	80021ec <xQueueGenericSend+0x108>
	configASSERT( !( ( pvItemToQueue == NULL ) && ( pxQueue->uxItemSize != ( UBaseType_t ) 0U ) ) );
 80020ee:	460e      	mov	r6, r1
 80020f0:	461d      	mov	r5, r3
 80020f2:	4604      	mov	r4, r0
 80020f4:	2900      	cmp	r1, #0
 80020f6:	d069      	beq.n	80021cc <xQueueGenericSend+0xe8>
	configASSERT( !( ( xCopyPosition == queueOVERWRITE ) && ( pxQueue->uxLength != 1 ) ) );
 80020f8:	2d02      	cmp	r5, #2
 80020fa:	d10b      	bne.n	8002114 <xQueueGenericSend+0x30>
 80020fc:	6be3      	ldr	r3, [r4, #60]	@ 0x3c
 80020fe:	2b01      	cmp	r3, #1
 8002100:	d008      	beq.n	8002114 <xQueueGenericSend+0x30>
 8002102:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002106:	f383 8811 	msr	BASEPRI, r3
 800210a:	f3bf 8f6f 	isb	sy
 800210e:	f3bf 8f4f 	dsb	sy
 8002112:	e7fe      	b.n	8002112 <xQueueGenericSend+0x2e>
		configASSERT( !( ( xTaskGetSchedulerState() == taskSCHEDULER_SUSPENDED ) && ( xTicksToWait != 0 ) ) );
 8002114:	f000 febc 	bl	8002e90 <xTaskGetSchedulerState>
 8002118:	2800      	cmp	r0, #0
 800211a:	d070      	beq.n	80021fe <xQueueGenericSend+0x11a>
		taskENTER_CRITICAL();
 800211c:	f001 f97a 	bl	8003414 <vPortEnterCritical>
			if( ( pxQueue->uxMessagesWaiting < pxQueue->uxLength ) || ( xCopyPosition == queueOVERWRITE ) )
 8002120:	6ba2      	ldr	r2, [r4, #56]	@ 0x38
 8002122:	6be3      	ldr	r3, [r4, #60]	@ 0x3c
 8002124:	429a      	cmp	r2, r3
 8002126:	d376      	bcc.n	8002216 <xQueueGenericSend+0x132>
 8002128:	2d02      	cmp	r5, #2
 800212a:	d074      	beq.n	8002216 <xQueueGenericSend+0x132>
 800212c:	2200      	movs	r2, #0
		prvLockQueue( pxQueue );
 800212e:	4617      	mov	r7, r2
 8002130:	e00e      	b.n	8002150 <xQueueGenericSend+0x6c>
		else
		{
			xReturn = pdFALSE;
		}
	}
	taskEXIT_CRITICAL();
 8002132:	f001 f991 	bl	8003458 <vPortExitCritical>
				prvUnlockQueue( pxQueue );
 8002136:	4620      	mov	r0, r4
 8002138:	f7ff fef4 	bl	8001f24 <prvUnlockQueue>
				( void ) xTaskResumeAll();
 800213c:	f000 fd52 	bl	8002be4 <xTaskResumeAll>
		taskENTER_CRITICAL();
 8002140:	f001 f968 	bl	8003414 <vPortEnterCritical>
			if( ( pxQueue->uxMessagesWaiting < pxQueue->uxLength ) || ( xCopyPosition == queueOVERWRITE ) )
 8002144:	6ba1      	ldr	r1, [r4, #56]	@ 0x38
 8002146:	6be3      	ldr	r3, [r4, #60]	@ 0x3c
 8002148:	4299      	cmp	r1, r3
 800214a:	f04f 0201 	mov.w	r2, #1
 800214e:	d362      	bcc.n	8002216 <xQueueGenericSend+0x132>
				if( xTicksToWait == ( TickType_t ) 0 )
 8002150:	9801      	ldr	r0, [sp, #4]
 8002152:	2800      	cmp	r0, #0
 8002154:	d076      	beq.n	8002244 <xQueueGenericSend+0x160>
				else if( xEntryTimeSet == pdFALSE )
 8002156:	2a00      	cmp	r2, #0
 8002158:	d044      	beq.n	80021e4 <xQueueGenericSend+0x100>
		taskEXIT_CRITICAL();
 800215a:	f001 f97d 	bl	8003458 <vPortExitCritical>
		vTaskSuspendAll();
 800215e:	f000 fbf9 	bl	8002954 <vTaskSuspendAll>
		prvLockQueue( pxQueue );
 8002162:	f001 f957 	bl	8003414 <vPortEnterCritical>
 8002166:	f894 3044 	ldrb.w	r3, [r4, #68]	@ 0x44
 800216a:	2bff      	cmp	r3, #255	@ 0xff
 800216c:	bf08      	it	eq
 800216e:	f884 7044 	strbeq.w	r7, [r4, #68]	@ 0x44
 8002172:	f894 2045 	ldrb.w	r2, [r4, #69]	@ 0x45
 8002176:	2aff      	cmp	r2, #255	@ 0xff
 8002178:	bf08      	it	eq
 800217a:	f884 7045 	strbeq.w	r7, [r4, #69]	@ 0x45
 800217e:	f001 f96b 	bl	8003458 <vPortExitCritical>
		if( xTaskCheckForTimeOut( &xTimeOut, &xTicksToWait ) == pdFALSE )
 8002182:	a901      	add	r1, sp, #4
 8002184:	a802      	add	r0, sp, #8
 8002186:	f000 fe3b 	bl	8002e00 <xTaskCheckForTimeOut>
 800218a:	2800      	cmp	r0, #0
 800218c:	d15e      	bne.n	800224c <xQueueGenericSend+0x168>
	taskENTER_CRITICAL();
 800218e:	f001 f941 	bl	8003414 <vPortEnterCritical>
		if( pxQueue->uxMessagesWaiting == pxQueue->uxLength )
 8002192:	6ba2      	ldr	r2, [r4, #56]	@ 0x38
 8002194:	6be3      	ldr	r3, [r4, #60]	@ 0x3c
 8002196:	429a      	cmp	r2, r3
 8002198:	d1cb      	bne.n	8002132 <xQueueGenericSend+0x4e>
	taskEXIT_CRITICAL();
 800219a:	f001 f95d 	bl	8003458 <vPortExitCritical>
				vTaskPlaceOnEventList( &( pxQueue->xTasksWaitingToSend ), xTicksToWait );
 800219e:	9901      	ldr	r1, [sp, #4]
 80021a0:	f104 0010 	add.w	r0, r4, #16
 80021a4:	f000 fda4 	bl	8002cf0 <vTaskPlaceOnEventList>
				prvUnlockQueue( pxQueue );
 80021a8:	4620      	mov	r0, r4
 80021aa:	f7ff febb 	bl	8001f24 <prvUnlockQueue>
				if( xTaskResumeAll() == pdFALSE )
 80021ae:	f000 fd19 	bl	8002be4 <xTaskResumeAll>
 80021b2:	2800      	cmp	r0, #0
 80021b4:	d1c4      	bne.n	8002140 <xQueueGenericSend+0x5c>
					portYIELD_WITHIN_API();
 80021b6:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 80021ba:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 80021be:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
 80021c2:	f3bf 8f4f 	dsb	sy
 80021c6:	f3bf 8f6f 	isb	sy
 80021ca:	e7b9      	b.n	8002140 <xQueueGenericSend+0x5c>
	configASSERT( !( ( pvItemToQueue == NULL ) && ( pxQueue->uxItemSize != ( UBaseType_t ) 0U ) ) );
 80021cc:	6c03      	ldr	r3, [r0, #64]	@ 0x40
 80021ce:	2b00      	cmp	r3, #0
 80021d0:	d092      	beq.n	80020f8 <xQueueGenericSend+0x14>
 80021d2:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80021d6:	f383 8811 	msr	BASEPRI, r3
 80021da:	f3bf 8f6f 	isb	sy
 80021de:	f3bf 8f4f 	dsb	sy
 80021e2:	e7fe      	b.n	80021e2 <xQueueGenericSend+0xfe>
					vTaskInternalSetTimeOutState( &xTimeOut );
 80021e4:	a802      	add	r0, sp, #8
 80021e6:	f000 fdff 	bl	8002de8 <vTaskInternalSetTimeOutState>
					xEntryTimeSet = pdTRUE;
 80021ea:	e7b6      	b.n	800215a <xQueueGenericSend+0x76>
 80021ec:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80021f0:	f383 8811 	msr	BASEPRI, r3
 80021f4:	f3bf 8f6f 	isb	sy
 80021f8:	f3bf 8f4f 	dsb	sy
	configASSERT( pxQueue );
 80021fc:	e7fe      	b.n	80021fc <xQueueGenericSend+0x118>
		configASSERT( !( ( xTaskGetSchedulerState() == taskSCHEDULER_SUSPENDED ) && ( xTicksToWait != 0 ) ) );
 80021fe:	9b01      	ldr	r3, [sp, #4]
 8002200:	2b00      	cmp	r3, #0
 8002202:	d08b      	beq.n	800211c <xQueueGenericSend+0x38>
 8002204:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002208:	f383 8811 	msr	BASEPRI, r3
 800220c:	f3bf 8f6f 	isb	sy
 8002210:	f3bf 8f4f 	dsb	sy
 8002214:	e7fe      	b.n	8002214 <xQueueGenericSend+0x130>
					xYieldRequired = prvCopyDataToQueue( pxQueue, pvItemToQueue, xCopyPosition );
 8002216:	462a      	mov	r2, r5
 8002218:	4631      	mov	r1, r6
 800221a:	4620      	mov	r0, r4
 800221c:	f7ff fe46 	bl	8001eac <prvCopyDataToQueue>
					if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToReceive ) ) == pdFALSE )
 8002220:	6a63      	ldr	r3, [r4, #36]	@ 0x24
 8002222:	b9cb      	cbnz	r3, 8002258 <xQueueGenericSend+0x174>
					else if( xYieldRequired != pdFALSE )
 8002224:	b148      	cbz	r0, 800223a <xQueueGenericSend+0x156>
						queueYIELD_IF_USING_PREEMPTION();
 8002226:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 800222a:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 800222e:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
 8002232:	f3bf 8f4f 	dsb	sy
 8002236:	f3bf 8f6f 	isb	sy
				taskEXIT_CRITICAL();
 800223a:	f001 f90d 	bl	8003458 <vPortExitCritical>
				return pdPASS;
 800223e:	2001      	movs	r0, #1
}
 8002240:	b005      	add	sp, #20
 8002242:	bdf0      	pop	{r4, r5, r6, r7, pc}
					taskEXIT_CRITICAL();
 8002244:	f001 f908 	bl	8003458 <vPortExitCritical>
					return errQUEUE_FULL;
 8002248:	2000      	movs	r0, #0
 800224a:	e7f9      	b.n	8002240 <xQueueGenericSend+0x15c>
			prvUnlockQueue( pxQueue );
 800224c:	4620      	mov	r0, r4
 800224e:	f7ff fe69 	bl	8001f24 <prvUnlockQueue>
			( void ) xTaskResumeAll();
 8002252:	f000 fcc7 	bl	8002be4 <xTaskResumeAll>
			return errQUEUE_FULL;
 8002256:	e7f7      	b.n	8002248 <xQueueGenericSend+0x164>
						if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToReceive ) ) != pdFALSE )
 8002258:	f104 0024 	add.w	r0, r4, #36	@ 0x24
 800225c:	f000 fd80 	bl	8002d60 <xTaskRemoveFromEventList>
 8002260:	2800      	cmp	r0, #0
 8002262:	d0ea      	beq.n	800223a <xQueueGenericSend+0x156>
 8002264:	e7df      	b.n	8002226 <xQueueGenericSend+0x142>
 8002266:	bf00      	nop

08002268 <xQueueGenericSendFromISR>:
	configASSERT( pxQueue );
 8002268:	2800      	cmp	r0, #0
 800226a:	d048      	beq.n	80022fe <xQueueGenericSendFromISR+0x96>
{
 800226c:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 8002270:	460e      	mov	r6, r1
 8002272:	4617      	mov	r7, r2
 8002274:	461d      	mov	r5, r3
 8002276:	4604      	mov	r4, r0
	configASSERT( !( ( pvItemToQueue == NULL ) && ( pxQueue->uxItemSize != ( UBaseType_t ) 0U ) ) );
 8002278:	b321      	cbz	r1, 80022c4 <xQueueGenericSendFromISR+0x5c>
	configASSERT( !( ( xCopyPosition == queueOVERWRITE ) && ( pxQueue->uxLength != 1 ) ) );
 800227a:	2d02      	cmp	r5, #2
 800227c:	d10b      	bne.n	8002296 <xQueueGenericSendFromISR+0x2e>
 800227e:	6be3      	ldr	r3, [r4, #60]	@ 0x3c
 8002280:	2b01      	cmp	r3, #1
 8002282:	d008      	beq.n	8002296 <xQueueGenericSendFromISR+0x2e>
 8002284:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002288:	f383 8811 	msr	BASEPRI, r3
 800228c:	f3bf 8f6f 	isb	sy
 8002290:	f3bf 8f4f 	dsb	sy
 8002294:	e7fe      	b.n	8002294 <xQueueGenericSendFromISR+0x2c>
	portASSERT_IF_INTERRUPT_PRIORITY_INVALID();
 8002296:	f001 f9d9 	bl	800364c <vPortValidateInterruptPriority>

portFORCE_INLINE static uint32_t ulPortRaiseBASEPRI( void )
{
uint32_t ulOriginalBASEPRI, ulNewBASEPRI;

	__asm volatile
 800229a:	f3ef 8811 	mrs	r8, BASEPRI
 800229e:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80022a2:	f383 8811 	msr	BASEPRI, r3
 80022a6:	f3bf 8f6f 	isb	sy
 80022aa:	f3bf 8f4f 	dsb	sy
		if( ( pxQueue->uxMessagesWaiting < pxQueue->uxLength ) || ( xCopyPosition == queueOVERWRITE ) )
 80022ae:	6ba2      	ldr	r2, [r4, #56]	@ 0x38
 80022b0:	6be3      	ldr	r3, [r4, #60]	@ 0x3c
 80022b2:	429a      	cmp	r2, r3
 80022b4:	d312      	bcc.n	80022dc <xQueueGenericSendFromISR+0x74>
 80022b6:	2d02      	cmp	r5, #2
 80022b8:	d010      	beq.n	80022dc <xQueueGenericSendFromISR+0x74>
			xReturn = errQUEUE_FULL;
 80022ba:	2000      	movs	r0, #0
}
/*-----------------------------------------------------------*/

portFORCE_INLINE static void vPortSetBASEPRI( uint32_t ulNewMaskValue )
{
	__asm volatile
 80022bc:	f388 8811 	msr	BASEPRI, r8
}
 80022c0:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
	configASSERT( !( ( pvItemToQueue == NULL ) && ( pxQueue->uxItemSize != ( UBaseType_t ) 0U ) ) );
 80022c4:	6c03      	ldr	r3, [r0, #64]	@ 0x40
 80022c6:	2b00      	cmp	r3, #0
 80022c8:	d0d7      	beq.n	800227a <xQueueGenericSendFromISR+0x12>
	__asm volatile
 80022ca:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80022ce:	f383 8811 	msr	BASEPRI, r3
 80022d2:	f3bf 8f6f 	isb	sy
 80022d6:	f3bf 8f4f 	dsb	sy
 80022da:	e7fe      	b.n	80022da <xQueueGenericSendFromISR+0x72>
			( void ) prvCopyDataToQueue( pxQueue, pvItemToQueue, xCopyPosition );
 80022dc:	462a      	mov	r2, r5
			const int8_t cTxLock = pxQueue->cTxLock;
 80022de:	f894 5045 	ldrb.w	r5, [r4, #69]	@ 0x45
			const UBaseType_t uxPreviousMessagesWaiting = pxQueue->uxMessagesWaiting;
 80022e2:	6ba3      	ldr	r3, [r4, #56]	@ 0x38
			( void ) prvCopyDataToQueue( pxQueue, pvItemToQueue, xCopyPosition );
 80022e4:	4631      	mov	r1, r6
			const int8_t cTxLock = pxQueue->cTxLock;
 80022e6:	b26d      	sxtb	r5, r5
			( void ) prvCopyDataToQueue( pxQueue, pvItemToQueue, xCopyPosition );
 80022e8:	4620      	mov	r0, r4
 80022ea:	f7ff fddf 	bl	8001eac <prvCopyDataToQueue>
			if( cTxLock == queueUNLOCKED )
 80022ee:	1c6b      	adds	r3, r5, #1
 80022f0:	d00e      	beq.n	8002310 <xQueueGenericSendFromISR+0xa8>
				pxQueue->cTxLock = ( int8_t ) ( cTxLock + 1 );
 80022f2:	1c6b      	adds	r3, r5, #1
 80022f4:	b25b      	sxtb	r3, r3
 80022f6:	f884 3045 	strb.w	r3, [r4, #69]	@ 0x45
			xReturn = pdPASS;
 80022fa:	2001      	movs	r0, #1
 80022fc:	e7de      	b.n	80022bc <xQueueGenericSendFromISR+0x54>
 80022fe:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002302:	f383 8811 	msr	BASEPRI, r3
 8002306:	f3bf 8f6f 	isb	sy
 800230a:	f3bf 8f4f 	dsb	sy
	configASSERT( pxQueue );
 800230e:	e7fe      	b.n	800230e <xQueueGenericSendFromISR+0xa6>
					if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToReceive ) ) == pdFALSE )
 8002310:	6a63      	ldr	r3, [r4, #36]	@ 0x24
 8002312:	2b00      	cmp	r3, #0
 8002314:	d0f1      	beq.n	80022fa <xQueueGenericSendFromISR+0x92>
						if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToReceive ) ) != pdFALSE )
 8002316:	f104 0024 	add.w	r0, r4, #36	@ 0x24
 800231a:	f000 fd21 	bl	8002d60 <xTaskRemoveFromEventList>
 800231e:	2800      	cmp	r0, #0
 8002320:	d0eb      	beq.n	80022fa <xQueueGenericSendFromISR+0x92>
							if( pxHigherPriorityTaskWoken != NULL )
 8002322:	2f00      	cmp	r7, #0
 8002324:	d0e9      	beq.n	80022fa <xQueueGenericSendFromISR+0x92>
								*pxHigherPriorityTaskWoken = pdTRUE;
 8002326:	2301      	movs	r3, #1
 8002328:	603b      	str	r3, [r7, #0]
 800232a:	e7e6      	b.n	80022fa <xQueueGenericSendFromISR+0x92>

0800232c <xQueueReceive>:
{
 800232c:	b5f0      	push	{r4, r5, r6, r7, lr}
 800232e:	b085      	sub	sp, #20
 8002330:	9201      	str	r2, [sp, #4]
	configASSERT( ( pxQueue ) );
 8002332:	2800      	cmp	r0, #0
 8002334:	f000 8084 	beq.w	8002440 <xQueueReceive+0x114>
	configASSERT( !( ( ( pvBuffer ) == NULL ) && ( ( pxQueue )->uxItemSize != ( UBaseType_t ) 0U ) ) );
 8002338:	460e      	mov	r6, r1
 800233a:	4604      	mov	r4, r0
 800233c:	2900      	cmp	r1, #0
 800233e:	d041      	beq.n	80023c4 <xQueueReceive+0x98>
		configASSERT( !( ( xTaskGetSchedulerState() == taskSCHEDULER_SUSPENDED ) && ( xTicksToWait != 0 ) ) );
 8002340:	f000 fda6 	bl	8002e90 <xTaskGetSchedulerState>
 8002344:	2800      	cmp	r0, #0
 8002346:	d049      	beq.n	80023dc <xQueueReceive+0xb0>
		taskENTER_CRITICAL();
 8002348:	f001 f864 	bl	8003414 <vPortEnterCritical>
			const UBaseType_t uxMessagesWaiting = pxQueue->uxMessagesWaiting;
 800234c:	6ba5      	ldr	r5, [r4, #56]	@ 0x38
			if( uxMessagesWaiting > ( UBaseType_t ) 0 )
 800234e:	2d00      	cmp	r5, #0
 8002350:	d17f      	bne.n	8002452 <xQueueReceive+0x126>
				if( xTicksToWait == ( TickType_t ) 0 )
 8002352:	9b01      	ldr	r3, [sp, #4]
 8002354:	b38b      	cbz	r3, 80023ba <xQueueReceive+0x8e>
					vTaskInternalSetTimeOutState( &xTimeOut );
 8002356:	a802      	add	r0, sp, #8
 8002358:	f000 fd46 	bl	8002de8 <vTaskInternalSetTimeOutState>
		prvLockQueue( pxQueue );
 800235c:	462f      	mov	r7, r5
		taskEXIT_CRITICAL();
 800235e:	f001 f87b 	bl	8003458 <vPortExitCritical>
		vTaskSuspendAll();
 8002362:	f000 faf7 	bl	8002954 <vTaskSuspendAll>
		prvLockQueue( pxQueue );
 8002366:	f001 f855 	bl	8003414 <vPortEnterCritical>
 800236a:	f894 3044 	ldrb.w	r3, [r4, #68]	@ 0x44
 800236e:	2bff      	cmp	r3, #255	@ 0xff
 8002370:	bf08      	it	eq
 8002372:	f884 7044 	strbeq.w	r7, [r4, #68]	@ 0x44
 8002376:	f894 3045 	ldrb.w	r3, [r4, #69]	@ 0x45
 800237a:	2bff      	cmp	r3, #255	@ 0xff
 800237c:	bf08      	it	eq
 800237e:	f884 7045 	strbeq.w	r7, [r4, #69]	@ 0x45
 8002382:	f001 f869 	bl	8003458 <vPortExitCritical>
		if( xTaskCheckForTimeOut( &xTimeOut, &xTicksToWait ) == pdFALSE )
 8002386:	a901      	add	r1, sp, #4
 8002388:	a802      	add	r0, sp, #8
 800238a:	f000 fd39 	bl	8002e00 <xTaskCheckForTimeOut>
 800238e:	2800      	cmp	r0, #0
 8002390:	d130      	bne.n	80023f4 <xQueueReceive+0xc8>
	taskENTER_CRITICAL();
 8002392:	f001 f83f 	bl	8003414 <vPortEnterCritical>
		if( pxQueue->uxMessagesWaiting == ( UBaseType_t )  0 )
 8002396:	6ba3      	ldr	r3, [r4, #56]	@ 0x38
 8002398:	2b00      	cmp	r3, #0
 800239a:	d038      	beq.n	800240e <xQueueReceive+0xe2>
	taskEXIT_CRITICAL();
 800239c:	f001 f85c 	bl	8003458 <vPortExitCritical>
				prvUnlockQueue( pxQueue );
 80023a0:	4620      	mov	r0, r4
 80023a2:	f7ff fdbf 	bl	8001f24 <prvUnlockQueue>
				( void ) xTaskResumeAll();
 80023a6:	f000 fc1d 	bl	8002be4 <xTaskResumeAll>
		taskENTER_CRITICAL();
 80023aa:	f001 f833 	bl	8003414 <vPortEnterCritical>
			const UBaseType_t uxMessagesWaiting = pxQueue->uxMessagesWaiting;
 80023ae:	6ba5      	ldr	r5, [r4, #56]	@ 0x38
			if( uxMessagesWaiting > ( UBaseType_t ) 0 )
 80023b0:	2d00      	cmp	r5, #0
 80023b2:	d14e      	bne.n	8002452 <xQueueReceive+0x126>
				if( xTicksToWait == ( TickType_t ) 0 )
 80023b4:	9b01      	ldr	r3, [sp, #4]
 80023b6:	2b00      	cmp	r3, #0
 80023b8:	d1d1      	bne.n	800235e <xQueueReceive+0x32>
					taskEXIT_CRITICAL();
 80023ba:	f001 f84d 	bl	8003458 <vPortExitCritical>
					return errQUEUE_EMPTY;
 80023be:	2000      	movs	r0, #0
}
 80023c0:	b005      	add	sp, #20
 80023c2:	bdf0      	pop	{r4, r5, r6, r7, pc}
	configASSERT( !( ( ( pvBuffer ) == NULL ) && ( ( pxQueue )->uxItemSize != ( UBaseType_t ) 0U ) ) );
 80023c4:	6c03      	ldr	r3, [r0, #64]	@ 0x40
 80023c6:	2b00      	cmp	r3, #0
 80023c8:	d0ba      	beq.n	8002340 <xQueueReceive+0x14>
 80023ca:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80023ce:	f383 8811 	msr	BASEPRI, r3
 80023d2:	f3bf 8f6f 	isb	sy
 80023d6:	f3bf 8f4f 	dsb	sy
 80023da:	e7fe      	b.n	80023da <xQueueReceive+0xae>
		configASSERT( !( ( xTaskGetSchedulerState() == taskSCHEDULER_SUSPENDED ) && ( xTicksToWait != 0 ) ) );
 80023dc:	9b01      	ldr	r3, [sp, #4]
 80023de:	2b00      	cmp	r3, #0
 80023e0:	d0b2      	beq.n	8002348 <xQueueReceive+0x1c>
 80023e2:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80023e6:	f383 8811 	msr	BASEPRI, r3
 80023ea:	f3bf 8f6f 	isb	sy
 80023ee:	f3bf 8f4f 	dsb	sy
 80023f2:	e7fe      	b.n	80023f2 <xQueueReceive+0xc6>
			prvUnlockQueue( pxQueue );
 80023f4:	4620      	mov	r0, r4
 80023f6:	f7ff fd95 	bl	8001f24 <prvUnlockQueue>
			( void ) xTaskResumeAll();
 80023fa:	f000 fbf3 	bl	8002be4 <xTaskResumeAll>
	taskENTER_CRITICAL();
 80023fe:	f001 f809 	bl	8003414 <vPortEnterCritical>
		if( pxQueue->uxMessagesWaiting == ( UBaseType_t )  0 )
 8002402:	6ba3      	ldr	r3, [r4, #56]	@ 0x38
 8002404:	2b00      	cmp	r3, #0
 8002406:	d0d8      	beq.n	80023ba <xQueueReceive+0x8e>
	taskEXIT_CRITICAL();
 8002408:	f001 f826 	bl	8003458 <vPortExitCritical>
	return xReturn;
 800240c:	e7cd      	b.n	80023aa <xQueueReceive+0x7e>
	taskEXIT_CRITICAL();
 800240e:	f001 f823 	bl	8003458 <vPortExitCritical>
				vTaskPlaceOnEventList( &( pxQueue->xTasksWaitingToReceive ), xTicksToWait );
 8002412:	9901      	ldr	r1, [sp, #4]
 8002414:	f104 0024 	add.w	r0, r4, #36	@ 0x24
 8002418:	f000 fc6a 	bl	8002cf0 <vTaskPlaceOnEventList>
				prvUnlockQueue( pxQueue );
 800241c:	4620      	mov	r0, r4
 800241e:	f7ff fd81 	bl	8001f24 <prvUnlockQueue>
				if( xTaskResumeAll() == pdFALSE )
 8002422:	f000 fbdf 	bl	8002be4 <xTaskResumeAll>
 8002426:	2800      	cmp	r0, #0
 8002428:	d1bf      	bne.n	80023aa <xQueueReceive+0x7e>
					portYIELD_WITHIN_API();
 800242a:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 800242e:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 8002432:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
 8002436:	f3bf 8f4f 	dsb	sy
 800243a:	f3bf 8f6f 	isb	sy
 800243e:	e7b4      	b.n	80023aa <xQueueReceive+0x7e>
 8002440:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002444:	f383 8811 	msr	BASEPRI, r3
 8002448:	f3bf 8f6f 	isb	sy
 800244c:	f3bf 8f4f 	dsb	sy
	configASSERT( ( pxQueue ) );
 8002450:	e7fe      	b.n	8002450 <xQueueReceive+0x124>
	if( pxQueue->uxItemSize != ( UBaseType_t ) 0 )
 8002452:	6c22      	ldr	r2, [r4, #64]	@ 0x40
 8002454:	b152      	cbz	r2, 800246c <xQueueReceive+0x140>
		pxQueue->u.xQueue.pcReadFrom += pxQueue->uxItemSize; /*lint !e9016 Pointer arithmetic on char types ok, especially in this use case where it is the clearest way of conveying intent. */
 8002456:	68e1      	ldr	r1, [r4, #12]
		if( pxQueue->u.xQueue.pcReadFrom >= pxQueue->u.xQueue.pcTail ) /*lint !e946 MISRA exception justified as use of the relational operator is the cleanest solutions. */
 8002458:	68a3      	ldr	r3, [r4, #8]
		pxQueue->u.xQueue.pcReadFrom += pxQueue->uxItemSize; /*lint !e9016 Pointer arithmetic on char types ok, especially in this use case where it is the clearest way of conveying intent. */
 800245a:	4411      	add	r1, r2
		if( pxQueue->u.xQueue.pcReadFrom >= pxQueue->u.xQueue.pcTail ) /*lint !e946 MISRA exception justified as use of the relational operator is the cleanest solutions. */
 800245c:	4299      	cmp	r1, r3
		pxQueue->u.xQueue.pcReadFrom += pxQueue->uxItemSize; /*lint !e9016 Pointer arithmetic on char types ok, especially in this use case where it is the clearest way of conveying intent. */
 800245e:	60e1      	str	r1, [r4, #12]
			pxQueue->u.xQueue.pcReadFrom = pxQueue->pcHead;
 8002460:	bf24      	itt	cs
 8002462:	6821      	ldrcs	r1, [r4, #0]
 8002464:	60e1      	strcs	r1, [r4, #12]
		( void ) memcpy( ( void * ) pvBuffer, ( void * ) pxQueue->u.xQueue.pcReadFrom, ( size_t ) pxQueue->uxItemSize ); /*lint !e961 !e418 !e9087 MISRA exception as the casts are only redundant for some ports.  Also previous logic ensures a null pointer can only be passed to memcpy() when the count is 0.  Cast to void required by function signature and safe as no alignment requirement and copy length specified in bytes. */
 8002466:	4630      	mov	r0, r6
 8002468:	f001 fa4e 	bl	8003908 <memcpy>
				pxQueue->uxMessagesWaiting = uxMessagesWaiting - ( UBaseType_t ) 1;
 800246c:	3d01      	subs	r5, #1
 800246e:	63a5      	str	r5, [r4, #56]	@ 0x38
				if( listLIST_IS_EMPTY( &( pxQueue->xTasksWaitingToSend ) ) == pdFALSE )
 8002470:	6923      	ldr	r3, [r4, #16]
 8002472:	b91b      	cbnz	r3, 800247c <xQueueReceive+0x150>
				taskEXIT_CRITICAL();
 8002474:	f000 fff0 	bl	8003458 <vPortExitCritical>
				return pdPASS;
 8002478:	2001      	movs	r0, #1
 800247a:	e7a1      	b.n	80023c0 <xQueueReceive+0x94>
					if( xTaskRemoveFromEventList( &( pxQueue->xTasksWaitingToSend ) ) != pdFALSE )
 800247c:	f104 0010 	add.w	r0, r4, #16
 8002480:	f000 fc6e 	bl	8002d60 <xTaskRemoveFromEventList>
 8002484:	2800      	cmp	r0, #0
 8002486:	d0f5      	beq.n	8002474 <xQueueReceive+0x148>
						queueYIELD_IF_USING_PREEMPTION();
 8002488:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 800248c:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 8002490:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
 8002494:	f3bf 8f4f 	dsb	sy
 8002498:	f3bf 8f6f 	isb	sy
 800249c:	e7ea      	b.n	8002474 <xQueueReceive+0x148>
 800249e:	bf00      	nop

080024a0 <vQueueAddToRegistry>:
/*-----------------------------------------------------------*/

#if ( configQUEUE_REGISTRY_SIZE > 0 )

	void vQueueAddToRegistry( QueueHandle_t xQueue, const char *pcQueueName ) /*lint !e971 Unqualified char types are allowed for strings and single characters only. */
	{
 80024a0:	b410      	push	{r4}
 80024a2:	4a0a      	ldr	r2, [pc, #40]	@ (80024cc <vQueueAddToRegistry+0x2c>)
	UBaseType_t ux;

		/* See if there is an empty space in the registry.  A NULL name denotes
		a free slot. */
		for( ux = ( UBaseType_t ) 0U; ux < ( UBaseType_t ) configQUEUE_REGISTRY_SIZE; ux++ )
 80024a4:	2300      	movs	r3, #0
		{
			if( xQueueRegistry[ ux ].pcQueueName == NULL )
 80024a6:	f852 4033 	ldr.w	r4, [r2, r3, lsl #3]
 80024aa:	eb02 0cc3 	add.w	ip, r2, r3, lsl #3
 80024ae:	b12c      	cbz	r4, 80024bc <vQueueAddToRegistry+0x1c>
		for( ux = ( UBaseType_t ) 0U; ux < ( UBaseType_t ) configQUEUE_REGISTRY_SIZE; ux++ )
 80024b0:	3301      	adds	r3, #1
 80024b2:	2b08      	cmp	r3, #8
 80024b4:	d1f7      	bne.n	80024a6 <vQueueAddToRegistry+0x6>
			else
			{
				mtCOVERAGE_TEST_MARKER();
			}
		}
	}
 80024b6:	f85d 4b04 	ldr.w	r4, [sp], #4
 80024ba:	4770      	bx	lr
				xQueueRegistry[ ux ].pcQueueName = pcQueueName;
 80024bc:	f842 1033 	str.w	r1, [r2, r3, lsl #3]
	}
 80024c0:	f85d 4b04 	ldr.w	r4, [sp], #4
				xQueueRegistry[ ux ].xHandle = xQueue;
 80024c4:	f8cc 0004 	str.w	r0, [ip, #4]
	}
 80024c8:	4770      	bx	lr
 80024ca:	bf00      	nop
 80024cc:	200007e8 	.word	0x200007e8

080024d0 <vQueueWaitForMessageRestricted>:
/*-----------------------------------------------------------*/

#if ( configUSE_TIMERS == 1 )

	void vQueueWaitForMessageRestricted( QueueHandle_t xQueue, TickType_t xTicksToWait, const BaseType_t xWaitIndefinitely )
	{
 80024d0:	b570      	push	{r4, r5, r6, lr}
 80024d2:	4604      	mov	r4, r0
 80024d4:	460e      	mov	r6, r1
 80024d6:	4615      	mov	r5, r2
		will not actually cause the task to block, just place it on a blocked
		list.  It will not block until the scheduler is unlocked - at which
		time a yield will be performed.  If an item is added to the queue while
		the queue is locked, and the calling task blocks on the queue, then the
		calling task will be immediately unblocked when the queue is unlocked. */
		prvLockQueue( pxQueue );
 80024d8:	f000 ff9c 	bl	8003414 <vPortEnterCritical>
 80024dc:	f894 3044 	ldrb.w	r3, [r4, #68]	@ 0x44
 80024e0:	2bff      	cmp	r3, #255	@ 0xff
 80024e2:	bf04      	itt	eq
 80024e4:	2300      	moveq	r3, #0
 80024e6:	f884 3044 	strbeq.w	r3, [r4, #68]	@ 0x44
 80024ea:	f894 3045 	ldrb.w	r3, [r4, #69]	@ 0x45
 80024ee:	2bff      	cmp	r3, #255	@ 0xff
 80024f0:	bf04      	itt	eq
 80024f2:	2300      	moveq	r3, #0
 80024f4:	f884 3045 	strbeq.w	r3, [r4, #69]	@ 0x45
 80024f8:	f000 ffae 	bl	8003458 <vPortExitCritical>
		if( pxQueue->uxMessagesWaiting == ( UBaseType_t ) 0U )
 80024fc:	6ba3      	ldr	r3, [r4, #56]	@ 0x38
 80024fe:	b123      	cbz	r3, 800250a <vQueueWaitForMessageRestricted+0x3a>
		}
		else
		{
			mtCOVERAGE_TEST_MARKER();
		}
		prvUnlockQueue( pxQueue );
 8002500:	4620      	mov	r0, r4
	}
 8002502:	e8bd 4070 	ldmia.w	sp!, {r4, r5, r6, lr}
		prvUnlockQueue( pxQueue );
 8002506:	f7ff bd0d 	b.w	8001f24 <prvUnlockQueue>
			vTaskPlaceOnEventListRestricted( &( pxQueue->xTasksWaitingToReceive ), xTicksToWait, xWaitIndefinitely );
 800250a:	f104 0024 	add.w	r0, r4, #36	@ 0x24
 800250e:	462a      	mov	r2, r5
 8002510:	4631      	mov	r1, r6
 8002512:	f000 fc07 	bl	8002d24 <vTaskPlaceOnEventListRestricted>
		prvUnlockQueue( pxQueue );
 8002516:	4620      	mov	r0, r4
	}
 8002518:	e8bd 4070 	ldmia.w	sp!, {r4, r5, r6, lr}
		prvUnlockQueue( pxQueue );
 800251c:	f7ff bd02 	b.w	8001f24 <prvUnlockQueue>

08002520 <prvAddNewTaskToReadyList>:
	}
}
/*-----------------------------------------------------------*/

static void prvAddNewTaskToReadyList( TCB_t *pxNewTCB )
{
 8002520:	e92d 41f0 	stmdb	sp!, {r4, r5, r6, r7, r8, lr}
 8002524:	4605      	mov	r5, r0
	/* Ensure interrupts don't access the task lists while the lists are being
	updated. */
	taskENTER_CRITICAL();
 8002526:	f000 ff75 	bl	8003414 <vPortEnterCritical>
	{
		uxCurrentNumberOfTasks++;
 800252a:	4a33      	ldr	r2, [pc, #204]	@ (80025f8 <prvAddNewTaskToReadyList+0xd8>)
		if( pxCurrentTCB == NULL )
 800252c:	4e33      	ldr	r6, [pc, #204]	@ (80025fc <prvAddNewTaskToReadyList+0xdc>)
		uxCurrentNumberOfTasks++;
 800252e:	6813      	ldr	r3, [r2, #0]
 8002530:	3301      	adds	r3, #1
 8002532:	6013      	str	r3, [r2, #0]
		if( pxCurrentTCB == NULL )
 8002534:	6833      	ldr	r3, [r6, #0]
 8002536:	2b00      	cmp	r3, #0
 8002538:	d031      	beq.n	800259e <prvAddNewTaskToReadyList+0x7e>
		else
		{
			/* If the scheduler is not already running, make this task the
			current task if it is the highest priority task to be created
			so far. */
			if( xSchedulerRunning == pdFALSE )
 800253a:	4c31      	ldr	r4, [pc, #196]	@ (8002600 <prvAddNewTaskToReadyList+0xe0>)
			{
				if( pxCurrentTCB->uxPriority <= pxNewTCB->uxPriority )
 800253c:	6ae8      	ldr	r0, [r5, #44]	@ 0x2c
			if( xSchedulerRunning == pdFALSE )
 800253e:	6823      	ldr	r3, [r4, #0]
 8002540:	b333      	cbz	r3, 8002590 <prvAddNewTaskToReadyList+0x70>
 8002542:	4f30      	ldr	r7, [pc, #192]	@ (8002604 <prvAddNewTaskToReadyList+0xe4>)
			{
				mtCOVERAGE_TEST_MARKER();
			}
		}

		uxTaskNumber++;
 8002544:	4a30      	ldr	r2, [pc, #192]	@ (8002608 <prvAddNewTaskToReadyList+0xe8>)
			pxNewTCB->uxTCBNumber = uxTaskNumber;
		}
		#endif /* configUSE_TRACE_FACILITY */
		traceTASK_CREATE( pxNewTCB );

		prvAddTaskToReadyList( pxNewTCB );
 8002546:	4931      	ldr	r1, [pc, #196]	@ (800260c <prvAddNewTaskToReadyList+0xec>)
		uxTaskNumber++;
 8002548:	6813      	ldr	r3, [r2, #0]
 800254a:	3301      	adds	r3, #1
 800254c:	6013      	str	r3, [r2, #0]
			pxNewTCB->uxTCBNumber = uxTaskNumber;
 800254e:	646b      	str	r3, [r5, #68]	@ 0x44
		prvAddTaskToReadyList( pxNewTCB );
 8002550:	680b      	ldr	r3, [r1, #0]
 8002552:	4283      	cmp	r3, r0
 8002554:	bf38      	it	cc
 8002556:	6008      	strcc	r0, [r1, #0]
 8002558:	eb00 0080 	add.w	r0, r0, r0, lsl #2
 800255c:	eb07 0080 	add.w	r0, r7, r0, lsl #2
 8002560:	1d29      	adds	r1, r5, #4
 8002562:	f7ff fc69 	bl	8001e38 <vListInsertEnd>

		portSETUP_TCB( pxNewTCB );
	}
	taskEXIT_CRITICAL();
 8002566:	f000 ff77 	bl	8003458 <vPortExitCritical>

	if( xSchedulerRunning != pdFALSE )
 800256a:	6823      	ldr	r3, [r4, #0]
 800256c:	b173      	cbz	r3, 800258c <prvAddNewTaskToReadyList+0x6c>
	{
		/* If the created task is of a higher priority than the current task
		then it should run now. */
		if( pxCurrentTCB->uxPriority < pxNewTCB->uxPriority )
 800256e:	6832      	ldr	r2, [r6, #0]
 8002570:	6aeb      	ldr	r3, [r5, #44]	@ 0x2c
 8002572:	6ad2      	ldr	r2, [r2, #44]	@ 0x2c
 8002574:	429a      	cmp	r2, r3
 8002576:	d209      	bcs.n	800258c <prvAddNewTaskToReadyList+0x6c>
		{
			taskYIELD_IF_USING_PREEMPTION();
 8002578:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 800257c:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 8002580:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
 8002584:	f3bf 8f4f 	dsb	sy
 8002588:	f3bf 8f6f 	isb	sy
	}
	else
	{
		mtCOVERAGE_TEST_MARKER();
	}
}
 800258c:	e8bd 81f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, pc}
				if( pxCurrentTCB->uxPriority <= pxNewTCB->uxPriority )
 8002590:	6833      	ldr	r3, [r6, #0]
 8002592:	4f1c      	ldr	r7, [pc, #112]	@ (8002604 <prvAddNewTaskToReadyList+0xe4>)
 8002594:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8002596:	4283      	cmp	r3, r0
					pxCurrentTCB = pxNewTCB;
 8002598:	bf98      	it	ls
 800259a:	6035      	strls	r5, [r6, #0]
 800259c:	e7d2      	b.n	8002544 <prvAddNewTaskToReadyList+0x24>
			pxCurrentTCB = pxNewTCB;
 800259e:	6035      	str	r5, [r6, #0]
			if( uxCurrentNumberOfTasks == ( UBaseType_t ) 1 )
 80025a0:	6813      	ldr	r3, [r2, #0]
 80025a2:	2b01      	cmp	r3, #1
 80025a4:	d003      	beq.n	80025ae <prvAddNewTaskToReadyList+0x8e>
				if( pxCurrentTCB->uxPriority <= pxNewTCB->uxPriority )
 80025a6:	6ae8      	ldr	r0, [r5, #44]	@ 0x2c
 80025a8:	4f16      	ldr	r7, [pc, #88]	@ (8002604 <prvAddNewTaskToReadyList+0xe4>)
 80025aa:	4c15      	ldr	r4, [pc, #84]	@ (8002600 <prvAddNewTaskToReadyList+0xe0>)
 80025ac:	e7ca      	b.n	8002544 <prvAddNewTaskToReadyList+0x24>
 80025ae:	4f15      	ldr	r7, [pc, #84]	@ (8002604 <prvAddNewTaskToReadyList+0xe4>)
 80025b0:	463c      	mov	r4, r7
 80025b2:	f507 688c 	add.w	r8, r7, #1120	@ 0x460
{
UBaseType_t uxPriority;

	for( uxPriority = ( UBaseType_t ) 0U; uxPriority < ( UBaseType_t ) configMAX_PRIORITIES; uxPriority++ )
	{
		vListInitialise( &( pxReadyTasksLists[ uxPriority ] ) );
 80025b6:	4620      	mov	r0, r4
	for( uxPriority = ( UBaseType_t ) 0U; uxPriority < ( UBaseType_t ) configMAX_PRIORITIES; uxPriority++ )
 80025b8:	3414      	adds	r4, #20
		vListInitialise( &( pxReadyTasksLists[ uxPriority ] ) );
 80025ba:	f7ff fc2d 	bl	8001e18 <vListInitialise>
	for( uxPriority = ( UBaseType_t ) 0U; uxPriority < ( UBaseType_t ) configMAX_PRIORITIES; uxPriority++ )
 80025be:	4544      	cmp	r4, r8
 80025c0:	d1f9      	bne.n	80025b6 <prvAddNewTaskToReadyList+0x96>
	}

	vListInitialise( &xDelayedTaskList1 );
 80025c2:	f8df 8064 	ldr.w	r8, [pc, #100]	@ 8002628 <prvAddNewTaskToReadyList+0x108>
	vListInitialise( &xDelayedTaskList2 );
 80025c6:	4c12      	ldr	r4, [pc, #72]	@ (8002610 <prvAddNewTaskToReadyList+0xf0>)
	vListInitialise( &xDelayedTaskList1 );
 80025c8:	4640      	mov	r0, r8
 80025ca:	f7ff fc25 	bl	8001e18 <vListInitialise>
	vListInitialise( &xDelayedTaskList2 );
 80025ce:	4620      	mov	r0, r4
 80025d0:	f7ff fc22 	bl	8001e18 <vListInitialise>
	vListInitialise( &xPendingReadyList );
 80025d4:	480f      	ldr	r0, [pc, #60]	@ (8002614 <prvAddNewTaskToReadyList+0xf4>)
 80025d6:	f7ff fc1f 	bl	8001e18 <vListInitialise>

	#if ( INCLUDE_vTaskDelete == 1 )
	{
		vListInitialise( &xTasksWaitingTermination );
 80025da:	480f      	ldr	r0, [pc, #60]	@ (8002618 <prvAddNewTaskToReadyList+0xf8>)
 80025dc:	f7ff fc1c 	bl	8001e18 <vListInitialise>
	}
	#endif /* INCLUDE_vTaskDelete */

	#if ( INCLUDE_vTaskSuspend == 1 )
	{
		vListInitialise( &xSuspendedTaskList );
 80025e0:	480e      	ldr	r0, [pc, #56]	@ (800261c <prvAddNewTaskToReadyList+0xfc>)
 80025e2:	f7ff fc19 	bl	8001e18 <vListInitialise>
	}
	#endif /* INCLUDE_vTaskSuspend */

	/* Start with pxDelayedTaskList using list1 and the pxOverflowDelayedTaskList
	using list2. */
	pxDelayedTaskList = &xDelayedTaskList1;
 80025e6:	4b0e      	ldr	r3, [pc, #56]	@ (8002620 <prvAddNewTaskToReadyList+0x100>)
		prvAddTaskToReadyList( pxNewTCB );
 80025e8:	6ae8      	ldr	r0, [r5, #44]	@ 0x2c
	pxDelayedTaskList = &xDelayedTaskList1;
 80025ea:	f8c3 8000 	str.w	r8, [r3]
	pxOverflowDelayedTaskList = &xDelayedTaskList2;
 80025ee:	4b0d      	ldr	r3, [pc, #52]	@ (8002624 <prvAddNewTaskToReadyList+0x104>)
 80025f0:	601c      	str	r4, [r3, #0]
 80025f2:	4c03      	ldr	r4, [pc, #12]	@ (8002600 <prvAddNewTaskToReadyList+0xe0>)
}
 80025f4:	e7a6      	b.n	8002544 <prvAddNewTaskToReadyList+0x24>
 80025f6:	bf00      	nop
 80025f8:	2000084c 	.word	0x2000084c
 80025fc:	20000d20 	.word	0x20000d20
 8002600:	20000840 	.word	0x20000840
 8002604:	200008c0 	.word	0x200008c0
 8002608:	20000830 	.word	0x20000830
 800260c:	20000844 	.word	0x20000844
 8002610:	20000898 	.word	0x20000898
 8002614:	2000087c 	.word	0x2000087c
 8002618:	20000868 	.word	0x20000868
 800261c:	20000850 	.word	0x20000850
 8002620:	20000894 	.word	0x20000894
 8002624:	20000890 	.word	0x20000890
 8002628:	200008ac 	.word	0x200008ac

0800262c <prvAddCurrentTaskToDelayedList>:

#endif
/*-----------------------------------------------------------*/

static void prvAddCurrentTaskToDelayedList( TickType_t xTicksToWait, const BaseType_t xCanBlockIndefinitely )
{
 800262c:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
TickType_t xTimeToWake;
const TickType_t xConstTickCount = xTickCount;
 800262e:	4b1c      	ldr	r3, [pc, #112]	@ (80026a0 <prvAddCurrentTaskToDelayedList+0x74>)
	}
	#endif

	/* Remove the task from the ready list before adding it to the blocked list
	as the same list item is used for both lists. */
	if( uxListRemove( &( pxCurrentTCB->xStateListItem ) ) == ( UBaseType_t ) 0 )
 8002630:	4e1c      	ldr	r6, [pc, #112]	@ (80026a4 <prvAddCurrentTaskToDelayedList+0x78>)
const TickType_t xConstTickCount = xTickCount;
 8002632:	681d      	ldr	r5, [r3, #0]
	if( uxListRemove( &( pxCurrentTCB->xStateListItem ) ) == ( UBaseType_t ) 0 )
 8002634:	6833      	ldr	r3, [r6, #0]
{
 8002636:	4604      	mov	r4, r0
	if( uxListRemove( &( pxCurrentTCB->xStateListItem ) ) == ( UBaseType_t ) 0 )
 8002638:	1d18      	adds	r0, r3, #4
{
 800263a:	460f      	mov	r7, r1
	if( uxListRemove( &( pxCurrentTCB->xStateListItem ) ) == ( UBaseType_t ) 0 )
 800263c:	f7ff fc22 	bl	8001e84 <uxListRemove>
		mtCOVERAGE_TEST_MARKER();
	}

	#if ( INCLUDE_vTaskSuspend == 1 )
	{
		if( ( xTicksToWait == portMAX_DELAY ) && ( xCanBlockIndefinitely != pdFALSE ) )
 8002640:	1c63      	adds	r3, r4, #1
 8002642:	d00f      	beq.n	8002664 <prvAddCurrentTaskToDelayedList+0x38>
			does not occur.  This may overflow but this doesn't matter, the
			kernel will manage it correctly. */
			xTimeToWake = xConstTickCount + xTicksToWait;

			/* The list item will be inserted in wake time order. */
			listSET_LIST_ITEM_VALUE( &( pxCurrentTCB->xStateListItem ), xTimeToWake );
 8002644:	6833      	ldr	r3, [r6, #0]
 8002646:	192d      	adds	r5, r5, r4
 8002648:	605d      	str	r5, [r3, #4]

			if( xTimeToWake < xConstTickCount )
 800264a:	d210      	bcs.n	800266e <prvAddCurrentTaskToDelayedList+0x42>
			}
			else
			{
				/* The wake time has not overflowed, so the current block list
				is used. */
				vListInsert( pxDelayedTaskList, &( pxCurrentTCB->xStateListItem ) );
 800264c:	4b16      	ldr	r3, [pc, #88]	@ (80026a8 <prvAddCurrentTaskToDelayedList+0x7c>)
 800264e:	6818      	ldr	r0, [r3, #0]
 8002650:	6831      	ldr	r1, [r6, #0]
 8002652:	3104      	adds	r1, #4
 8002654:	f7ff fbfe 	bl	8001e54 <vListInsert>

				/* If the task entering the blocked state was placed at the
				head of the list of blocked tasks then xNextTaskUnblockTime
				needs to be updated too. */
				if( xTimeToWake < xNextTaskUnblockTime )
 8002658:	4b14      	ldr	r3, [pc, #80]	@ (80026ac <prvAddCurrentTaskToDelayedList+0x80>)
 800265a:	681a      	ldr	r2, [r3, #0]
 800265c:	42aa      	cmp	r2, r5
 800265e:	d900      	bls.n	8002662 <prvAddCurrentTaskToDelayedList+0x36>
				{
					xNextTaskUnblockTime = xTimeToWake;
 8002660:	601d      	str	r5, [r3, #0]

		/* Avoid compiler warning when INCLUDE_vTaskSuspend is not 1. */
		( void ) xCanBlockIndefinitely;
	}
	#endif /* INCLUDE_vTaskSuspend */
}
 8002662:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
		if( ( xTicksToWait == portMAX_DELAY ) && ( xCanBlockIndefinitely != pdFALSE ) )
 8002664:	b9a7      	cbnz	r7, 8002690 <prvAddCurrentTaskToDelayedList+0x64>
			listSET_LIST_ITEM_VALUE( &( pxCurrentTCB->xStateListItem ), xTimeToWake );
 8002666:	6833      	ldr	r3, [r6, #0]
 8002668:	1e6a      	subs	r2, r5, #1
 800266a:	605a      	str	r2, [r3, #4]
			if( xTimeToWake < xConstTickCount )
 800266c:	b13d      	cbz	r5, 800267e <prvAddCurrentTaskToDelayedList+0x52>
				vListInsert( pxOverflowDelayedTaskList, &( pxCurrentTCB->xStateListItem ) );
 800266e:	4b10      	ldr	r3, [pc, #64]	@ (80026b0 <prvAddCurrentTaskToDelayedList+0x84>)
 8002670:	6818      	ldr	r0, [r3, #0]
 8002672:	6831      	ldr	r1, [r6, #0]
}
 8002674:	e8bd 40f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, lr}
				vListInsert( pxOverflowDelayedTaskList, &( pxCurrentTCB->xStateListItem ) );
 8002678:	3104      	adds	r1, #4
 800267a:	f7ff bbeb 	b.w	8001e54 <vListInsert>
				vListInsert( pxDelayedTaskList, &( pxCurrentTCB->xStateListItem ) );
 800267e:	4b0a      	ldr	r3, [pc, #40]	@ (80026a8 <prvAddCurrentTaskToDelayedList+0x7c>)
 8002680:	6818      	ldr	r0, [r3, #0]
 8002682:	6831      	ldr	r1, [r6, #0]
 8002684:	3104      	adds	r1, #4
 8002686:	f7ff fbe5 	bl	8001e54 <vListInsert>
				if( xTimeToWake < xNextTaskUnblockTime )
 800268a:	4b08      	ldr	r3, [pc, #32]	@ (80026ac <prvAddCurrentTaskToDelayedList+0x80>)
 800268c:	681b      	ldr	r3, [r3, #0]
}
 800268e:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
			vListInsertEnd( &xSuspendedTaskList, &( pxCurrentTCB->xStateListItem ) );
 8002690:	6831      	ldr	r1, [r6, #0]
 8002692:	4808      	ldr	r0, [pc, #32]	@ (80026b4 <prvAddCurrentTaskToDelayedList+0x88>)
}
 8002694:	e8bd 40f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, lr}
			vListInsertEnd( &xSuspendedTaskList, &( pxCurrentTCB->xStateListItem ) );
 8002698:	3104      	adds	r1, #4
 800269a:	f7ff bbcd 	b.w	8001e38 <vListInsertEnd>
 800269e:	bf00      	nop
 80026a0:	20000848 	.word	0x20000848
 80026a4:	20000d20 	.word	0x20000d20
 80026a8:	20000894 	.word	0x20000894
 80026ac:	2000082c 	.word	0x2000082c
 80026b0:	20000890 	.word	0x20000890
 80026b4:	20000850 	.word	0x20000850

080026b8 <prvInitialiseNewTask.constprop.0>:
static void prvInitialiseNewTask( 	TaskFunction_t pxTaskCode,
 80026b8:	e92d 47f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, lr}
 80026bc:	9c0a      	ldr	r4, [sp, #40]	@ 0x28
 80026be:	f8dd 8024 	ldr.w	r8, [sp, #36]	@ 0x24
		( void ) memset( pxNewTCB->pxStack, ( int ) tskSTACK_FILL_BYTE, ( size_t ) ulStackDepth * sizeof( StackType_t ) );
 80026c2:	0095      	lsls	r5, r2, #2
 80026c4:	462a      	mov	r2, r5
static void prvInitialiseNewTask( 	TaskFunction_t pxTaskCode,
 80026c6:	4606      	mov	r6, r0
 80026c8:	4689      	mov	r9, r1
		( void ) memset( pxNewTCB->pxStack, ( int ) tskSTACK_FILL_BYTE, ( size_t ) ulStackDepth * sizeof( StackType_t ) );
 80026ca:	6b20      	ldr	r0, [r4, #48]	@ 0x30
 80026cc:	21a5      	movs	r1, #165	@ 0xa5
static void prvInitialiseNewTask( 	TaskFunction_t pxTaskCode,
 80026ce:	461f      	mov	r7, r3
		( void ) memset( pxNewTCB->pxStack, ( int ) tskSTACK_FILL_BYTE, ( size_t ) ulStackDepth * sizeof( StackType_t ) );
 80026d0:	f001 f8ee 	bl	80038b0 <memset>
		pxTopOfStack = &( pxNewTCB->pxStack[ ulStackDepth - ( uint32_t ) 1 ] );
 80026d4:	6b23      	ldr	r3, [r4, #48]	@ 0x30
 80026d6:	3d04      	subs	r5, #4
 80026d8:	441d      	add	r5, r3
		pxTopOfStack = ( StackType_t * ) ( ( ( portPOINTER_SIZE_TYPE ) pxTopOfStack ) & ( ~( ( portPOINTER_SIZE_TYPE ) portBYTE_ALIGNMENT_MASK ) ) ); /*lint !e923 !e9033 !e9078 MISRA exception.  Avoiding casts between pointers and integers is not practical.  Size differences accounted for using portPOINTER_SIZE_TYPE type.  Checked by assert(). */
 80026da:	f025 0507 	bic.w	r5, r5, #7
	if( pcName != NULL )
 80026de:	f1b9 0f00 	cmp.w	r9, #0
 80026e2:	d037      	beq.n	8002754 <prvInitialiseNewTask.constprop.0+0x9c>
 80026e4:	f109 3cff 	add.w	ip, r9, #**********
 80026e8:	f109 010f 	add.w	r1, r9, #15
 80026ec:	f104 0333 	add.w	r3, r4, #51	@ 0x33
			pxNewTCB->pcTaskName[ x ] = pcName[ x ];
 80026f0:	f81c 2f01 	ldrb.w	r2, [ip, #1]!
 80026f4:	f803 2f01 	strb.w	r2, [r3, #1]!
			if( pcName[ x ] == ( char ) 0x00 )
 80026f8:	b10a      	cbz	r2, 80026fe <prvInitialiseNewTask.constprop.0+0x46>
		for( x = ( UBaseType_t ) 0; x < ( UBaseType_t ) configMAX_TASK_NAME_LEN; x++ )
 80026fa:	458c      	cmp	ip, r1
 80026fc:	d1f8      	bne.n	80026f0 <prvInitialiseNewTask.constprop.0+0x38>
		pxNewTCB->pcTaskName[ configMAX_TASK_NAME_LEN - 1 ] = '\0';
 80026fe:	2300      	movs	r3, #0
 8002700:	f884 3043 	strb.w	r3, [r4, #67]	@ 0x43
	if( uxPriority >= ( UBaseType_t ) configMAX_PRIORITIES )
 8002704:	9b08      	ldr	r3, [sp, #32]
 8002706:	2b37      	cmp	r3, #55	@ 0x37
 8002708:	bf28      	it	cs
 800270a:	2337      	movcs	r3, #55	@ 0x37
		pxNewTCB->uxMutexesHeld = 0;
 800270c:	f04f 0a00 	mov.w	sl, #0
	pxNewTCB->uxPriority = uxPriority;
 8002710:	62e3      	str	r3, [r4, #44]	@ 0x2c
		pxNewTCB->uxBasePriority = uxPriority;
 8002712:	64e3      	str	r3, [r4, #76]	@ 0x4c
	vListInitialiseItem( &( pxNewTCB->xStateListItem ) );
 8002714:	1d20      	adds	r0, r4, #4
		pxNewTCB->uxMutexesHeld = 0;
 8002716:	f8c4 a050 	str.w	sl, [r4, #80]	@ 0x50
	if( uxPriority >= ( UBaseType_t ) configMAX_PRIORITIES )
 800271a:	4699      	mov	r9, r3
	vListInitialiseItem( &( pxNewTCB->xStateListItem ) );
 800271c:	f7ff fb88 	bl	8001e30 <vListInitialiseItem>
	vListInitialiseItem( &( pxNewTCB->xEventListItem ) );
 8002720:	f104 0018 	add.w	r0, r4, #24
 8002724:	f7ff fb84 	bl	8001e30 <vListInitialiseItem>
	listSET_LIST_ITEM_VALUE( &( pxNewTCB->xEventListItem ), ( TickType_t ) configMAX_PRIORITIES - ( TickType_t ) uxPriority ); /*lint !e961 MISRA exception as the casts are only redundant for some ports. */
 8002728:	f1c9 0338 	rsb	r3, r9, #56	@ 0x38
		pxNewTCB->ulNotifiedValue = 0;
 800272c:	f8c4 a054 	str.w	sl, [r4, #84]	@ 0x54
	listSET_LIST_ITEM_VALUE( &( pxNewTCB->xEventListItem ), ( TickType_t ) configMAX_PRIORITIES - ( TickType_t ) uxPriority ); /*lint !e961 MISRA exception as the casts are only redundant for some ports. */
 8002730:	61a3      	str	r3, [r4, #24]
		pxNewTCB->ucNotifyState = taskNOT_WAITING_NOTIFICATION;
 8002732:	f884 a058 	strb.w	sl, [r4, #88]	@ 0x58
	listSET_LIST_ITEM_OWNER( &( pxNewTCB->xStateListItem ), pxNewTCB );
 8002736:	6124      	str	r4, [r4, #16]
	listSET_LIST_ITEM_OWNER( &( pxNewTCB->xEventListItem ), pxNewTCB );
 8002738:	6264      	str	r4, [r4, #36]	@ 0x24
			pxNewTCB->pxTopOfStack = pxPortInitialiseStack( pxTopOfStack, pxTaskCode, pvParameters );
 800273a:	463a      	mov	r2, r7
 800273c:	4631      	mov	r1, r6
 800273e:	4628      	mov	r0, r5
 8002740:	f000 fe3c 	bl	80033bc <pxPortInitialiseStack>
 8002744:	6020      	str	r0, [r4, #0]
	if( pxCreatedTask != NULL )
 8002746:	f1b8 0f00 	cmp.w	r8, #0
 800274a:	d001      	beq.n	8002750 <prvInitialiseNewTask.constprop.0+0x98>
		*pxCreatedTask = ( TaskHandle_t ) pxNewTCB;
 800274c:	f8c8 4000 	str.w	r4, [r8]
}
 8002750:	e8bd 87f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, pc}
		pxNewTCB->pcTaskName[ 0 ] = 0x00;
 8002754:	f884 9034 	strb.w	r9, [r4, #52]	@ 0x34
 8002758:	e7d4      	b.n	8002704 <prvInitialiseNewTask.constprop.0+0x4c>
 800275a:	bf00      	nop

0800275c <prvIdleTask>:
{
 800275c:	b580      	push	{r7, lr}
 800275e:	4c21      	ldr	r4, [pc, #132]	@ (80027e4 <prvIdleTask+0x88>)
 8002760:	4e21      	ldr	r6, [pc, #132]	@ (80027e8 <prvIdleTask+0x8c>)
 8002762:	4d22      	ldr	r5, [pc, #136]	@ (80027ec <prvIdleTask+0x90>)
 8002764:	4f22      	ldr	r7, [pc, #136]	@ (80027f0 <prvIdleTask+0x94>)
		while( uxDeletedTasksWaitingCleanUp > ( UBaseType_t ) 0U )
 8002766:	6823      	ldr	r3, [r4, #0]
 8002768:	b353      	cbz	r3, 80027c0 <prvIdleTask+0x64>
			taskENTER_CRITICAL();
 800276a:	f000 fe53 	bl	8003414 <vPortEnterCritical>
				pxTCB = listGET_OWNER_OF_HEAD_ENTRY( ( &xTasksWaitingTermination ) ); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 800276e:	68f3      	ldr	r3, [r6, #12]
 8002770:	f8d3 800c 	ldr.w	r8, [r3, #12]
				( void ) uxListRemove( &( pxTCB->xStateListItem ) );
 8002774:	f108 0004 	add.w	r0, r8, #4
 8002778:	f7ff fb84 	bl	8001e84 <uxListRemove>
				--uxCurrentNumberOfTasks;
 800277c:	682b      	ldr	r3, [r5, #0]
 800277e:	3b01      	subs	r3, #1
 8002780:	602b      	str	r3, [r5, #0]
				--uxDeletedTasksWaitingCleanUp;
 8002782:	6823      	ldr	r3, [r4, #0]
 8002784:	3b01      	subs	r3, #1
 8002786:	6023      	str	r3, [r4, #0]
			taskEXIT_CRITICAL();
 8002788:	f000 fe66 	bl	8003458 <vPortExitCritical>
			if( pxTCB->ucStaticallyAllocated == tskDYNAMICALLY_ALLOCATED_STACK_AND_TCB )
 800278c:	f898 3059 	ldrb.w	r3, [r8, #89]	@ 0x59
 8002790:	b163      	cbz	r3, 80027ac <prvIdleTask+0x50>
			else if( pxTCB->ucStaticallyAllocated == tskSTATICALLY_ALLOCATED_STACK_ONLY )
 8002792:	2b01      	cmp	r3, #1
 8002794:	d022      	beq.n	80027dc <prvIdleTask+0x80>
				configASSERT( pxTCB->ucStaticallyAllocated == tskSTATICALLY_ALLOCATED_STACK_AND_TCB	);
 8002796:	2b02      	cmp	r3, #2
 8002798:	d0e5      	beq.n	8002766 <prvIdleTask+0xa>
 800279a:	f04f 0350 	mov.w	r3, #80	@ 0x50
 800279e:	f383 8811 	msr	BASEPRI, r3
 80027a2:	f3bf 8f6f 	isb	sy
 80027a6:	f3bf 8f4f 	dsb	sy
 80027aa:	e7fe      	b.n	80027aa <prvIdleTask+0x4e>
				vPortFree( pxTCB->pxStack );
 80027ac:	f8d8 0030 	ldr.w	r0, [r8, #48]	@ 0x30
 80027b0:	f001 f83e 	bl	8003830 <vPortFree>
				vPortFree( pxTCB );
 80027b4:	4640      	mov	r0, r8
 80027b6:	f001 f83b 	bl	8003830 <vPortFree>
		while( uxDeletedTasksWaitingCleanUp > ( UBaseType_t ) 0U )
 80027ba:	6823      	ldr	r3, [r4, #0]
 80027bc:	2b00      	cmp	r3, #0
 80027be:	d1d4      	bne.n	800276a <prvIdleTask+0xe>
			if( listCURRENT_LIST_LENGTH( &( pxReadyTasksLists[ tskIDLE_PRIORITY ] ) ) > ( UBaseType_t ) 1 )
 80027c0:	683b      	ldr	r3, [r7, #0]
 80027c2:	2b01      	cmp	r3, #1
 80027c4:	d9cf      	bls.n	8002766 <prvIdleTask+0xa>
				taskYIELD();
 80027c6:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 80027ca:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 80027ce:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
 80027d2:	f3bf 8f4f 	dsb	sy
 80027d6:	f3bf 8f6f 	isb	sy
 80027da:	e7c4      	b.n	8002766 <prvIdleTask+0xa>
				vPortFree( pxTCB );
 80027dc:	4640      	mov	r0, r8
 80027de:	f001 f827 	bl	8003830 <vPortFree>
 80027e2:	e7c0      	b.n	8002766 <prvIdleTask+0xa>
 80027e4:	20000864 	.word	0x20000864
 80027e8:	20000868 	.word	0x20000868
 80027ec:	2000084c 	.word	0x2000084c
 80027f0:	200008c0 	.word	0x200008c0

080027f4 <xTaskCreateStatic>:
	{
 80027f4:	b530      	push	{r4, r5, lr}
 80027f6:	b087      	sub	sp, #28
 80027f8:	9c0b      	ldr	r4, [sp, #44]	@ 0x2c
		configASSERT( puxStackBuffer != NULL );
 80027fa:	b1c4      	cbz	r4, 800282e <xTaskCreateStatic+0x3a>
		configASSERT( pxTaskBuffer != NULL );
 80027fc:	9d0c      	ldr	r5, [sp, #48]	@ 0x30
 80027fe:	b16d      	cbz	r5, 800281c <xTaskCreateStatic+0x28>
			volatile size_t xSize = sizeof( StaticTask_t );
 8002800:	255c      	movs	r5, #92	@ 0x5c
 8002802:	9505      	str	r5, [sp, #20]
			configASSERT( xSize == sizeof( TCB_t ) );
 8002804:	9d05      	ldr	r5, [sp, #20]
 8002806:	2d5c      	cmp	r5, #92	@ 0x5c
 8002808:	d01a      	beq.n	8002840 <xTaskCreateStatic+0x4c>
 800280a:	f04f 0350 	mov.w	r3, #80	@ 0x50
 800280e:	f383 8811 	msr	BASEPRI, r3
 8002812:	f3bf 8f6f 	isb	sy
 8002816:	f3bf 8f4f 	dsb	sy
 800281a:	e7fe      	b.n	800281a <xTaskCreateStatic+0x26>
 800281c:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002820:	f383 8811 	msr	BASEPRI, r3
 8002824:	f3bf 8f6f 	isb	sy
 8002828:	f3bf 8f4f 	dsb	sy
		configASSERT( pxTaskBuffer != NULL );
 800282c:	e7fe      	b.n	800282c <xTaskCreateStatic+0x38>
 800282e:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002832:	f383 8811 	msr	BASEPRI, r3
 8002836:	f3bf 8f6f 	isb	sy
 800283a:	f3bf 8f4f 	dsb	sy
		configASSERT( puxStackBuffer != NULL );
 800283e:	e7fe      	b.n	800283e <xTaskCreateStatic+0x4a>
			pxNewTCB->pxStack = ( StackType_t * ) puxStackBuffer;
 8002840:	9d0c      	ldr	r5, [sp, #48]	@ 0x30
 8002842:	632c      	str	r4, [r5, #48]	@ 0x30
				pxNewTCB->ucStaticallyAllocated = tskSTATICALLY_ALLOCATED_STACK_AND_TCB;
 8002844:	2402      	movs	r4, #2
 8002846:	f885 4059 	strb.w	r4, [r5, #89]	@ 0x59
			prvInitialiseNewTask( pxTaskCode, pcName, ulStackDepth, pvParameters, uxPriority, &xReturn, pxNewTCB, NULL );
 800284a:	ac04      	add	r4, sp, #16
 800284c:	e9cd 4501 	strd	r4, r5, [sp, #4]
 8002850:	9c0a      	ldr	r4, [sp, #40]	@ 0x28
 8002852:	9400      	str	r4, [sp, #0]
			( void ) xSize; /* Prevent lint warning when configASSERT() is not used. */
 8002854:	9c05      	ldr	r4, [sp, #20]
			prvInitialiseNewTask( pxTaskCode, pcName, ulStackDepth, pvParameters, uxPriority, &xReturn, pxNewTCB, NULL );
 8002856:	f7ff ff2f 	bl	80026b8 <prvInitialiseNewTask.constprop.0>
			prvAddNewTaskToReadyList( pxNewTCB );
 800285a:	980c      	ldr	r0, [sp, #48]	@ 0x30
 800285c:	f7ff fe60 	bl	8002520 <prvAddNewTaskToReadyList>
	}
 8002860:	9804      	ldr	r0, [sp, #16]
 8002862:	b007      	add	sp, #28
 8002864:	bd30      	pop	{r4, r5, pc}
 8002866:	bf00      	nop

08002868 <xTaskCreate>:
	{
 8002868:	e92d 43f0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, lr}
 800286c:	4607      	mov	r7, r0
 800286e:	b085      	sub	sp, #20
			pxStack = pvPortMalloc( ( ( ( size_t ) usStackDepth ) * sizeof( StackType_t ) ) ); /*lint !e9079 All values returned by pvPortMalloc() have at least the alignment required by the MCU's stack and this allocation is the stack. */
 8002870:	0090      	lsls	r0, r2, #2
	{
 8002872:	4615      	mov	r5, r2
 8002874:	4688      	mov	r8, r1
 8002876:	4699      	mov	r9, r3
			pxStack = pvPortMalloc( ( ( ( size_t ) usStackDepth ) * sizeof( StackType_t ) ) ); /*lint !e9079 All values returned by pvPortMalloc() have at least the alignment required by the MCU's stack and this allocation is the stack. */
 8002878:	f000 ff46 	bl	8003708 <pvPortMalloc>
			if( pxStack != NULL )
 800287c:	b1f0      	cbz	r0, 80028bc <xTaskCreate+0x54>
				pxNewTCB = ( TCB_t * ) pvPortMalloc( sizeof( TCB_t ) ); /*lint !e9087 !e9079 All values returned by pvPortMalloc() have at least the alignment required by the MCU's stack, and the first member of TCB_t is always a pointer to the task's stack. */
 800287e:	4604      	mov	r4, r0
 8002880:	205c      	movs	r0, #92	@ 0x5c
 8002882:	f000 ff41 	bl	8003708 <pvPortMalloc>
				if( pxNewTCB != NULL )
 8002886:	4606      	mov	r6, r0
 8002888:	b1a8      	cbz	r0, 80028b6 <xTaskCreate+0x4e>
					pxNewTCB->pxStack = pxStack;
 800288a:	6304      	str	r4, [r0, #48]	@ 0x30
				pxNewTCB->ucStaticallyAllocated = tskDYNAMICALLY_ALLOCATED_STACK_AND_TCB;
 800288c:	2400      	movs	r4, #0
 800288e:	f886 4059 	strb.w	r4, [r6, #89]	@ 0x59
			prvInitialiseNewTask( pxTaskCode, pcName, ( uint32_t ) usStackDepth, pvParameters, uxPriority, pxCreatedTask, pxNewTCB, NULL );
 8002892:	9c0d      	ldr	r4, [sp, #52]	@ 0x34
 8002894:	9401      	str	r4, [sp, #4]
 8002896:	9c0c      	ldr	r4, [sp, #48]	@ 0x30
 8002898:	9602      	str	r6, [sp, #8]
 800289a:	464b      	mov	r3, r9
 800289c:	462a      	mov	r2, r5
 800289e:	4641      	mov	r1, r8
 80028a0:	4638      	mov	r0, r7
 80028a2:	9400      	str	r4, [sp, #0]
 80028a4:	f7ff ff08 	bl	80026b8 <prvInitialiseNewTask.constprop.0>
			prvAddNewTaskToReadyList( pxNewTCB );
 80028a8:	4630      	mov	r0, r6
 80028aa:	f7ff fe39 	bl	8002520 <prvAddNewTaskToReadyList>
			xReturn = pdPASS;
 80028ae:	2001      	movs	r0, #1
	}
 80028b0:	b005      	add	sp, #20
 80028b2:	e8bd 83f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, pc}
					vPortFree( pxStack );
 80028b6:	4620      	mov	r0, r4
 80028b8:	f000 ffba 	bl	8003830 <vPortFree>
			xReturn = errCOULD_NOT_ALLOCATE_REQUIRED_MEMORY;
 80028bc:	f04f 30ff 	mov.w	r0, #**********
	}
 80028c0:	b005      	add	sp, #20
 80028c2:	e8bd 83f0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, pc}
 80028c6:	bf00      	nop

080028c8 <vTaskStartScheduler>:
{
 80028c8:	b530      	push	{r4, r5, lr}
 80028ca:	b089      	sub	sp, #36	@ 0x24
		StaticTask_t *pxIdleTaskTCBBuffer = NULL;
 80028cc:	2400      	movs	r4, #0
		vApplicationGetIdleTaskMemory( &pxIdleTaskTCBBuffer, &pxIdleTaskStackBuffer, &ulIdleTaskStackSize );
 80028ce:	aa07      	add	r2, sp, #28
 80028d0:	a906      	add	r1, sp, #24
 80028d2:	a805      	add	r0, sp, #20
		StackType_t *pxIdleTaskStackBuffer = NULL;
 80028d4:	e9cd 4405 	strd	r4, r4, [sp, #20]
		vApplicationGetIdleTaskMemory( &pxIdleTaskTCBBuffer, &pxIdleTaskStackBuffer, &ulIdleTaskStackSize );
 80028d8:	f7ff fa80 	bl	8001ddc <vApplicationGetIdleTaskMemory>
		xIdleTaskHandle = xTaskCreateStatic(	prvIdleTask,
 80028dc:	e9dd 2305 	ldrd	r2, r3, [sp, #20]
 80028e0:	4917      	ldr	r1, [pc, #92]	@ (8002940 <vTaskStartScheduler+0x78>)
 80028e2:	4818      	ldr	r0, [pc, #96]	@ (8002944 <vTaskStartScheduler+0x7c>)
 80028e4:	9400      	str	r4, [sp, #0]
 80028e6:	e9cd 3201 	strd	r3, r2, [sp, #4]
 80028ea:	9a07      	ldr	r2, [sp, #28]
 80028ec:	4623      	mov	r3, r4
 80028ee:	f7ff ff81 	bl	80027f4 <xTaskCreateStatic>
		if( xIdleTaskHandle != NULL )
 80028f2:	b170      	cbz	r0, 8002912 <vTaskStartScheduler+0x4a>
			xReturn = xTimerCreateTimerTask();
 80028f4:	f000 fb56 	bl	8002fa4 <xTimerCreateTimerTask>
	if( xReturn == pdPASS )
 80028f8:	2801      	cmp	r0, #1
 80028fa:	d00c      	beq.n	8002916 <vTaskStartScheduler+0x4e>
		configASSERT( xReturn != errCOULD_NOT_ALLOCATE_REQUIRED_MEMORY );
 80028fc:	3001      	adds	r0, #1
 80028fe:	d108      	bne.n	8002912 <vTaskStartScheduler+0x4a>
 8002900:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002904:	f383 8811 	msr	BASEPRI, r3
 8002908:	f3bf 8f6f 	isb	sy
 800290c:	f3bf 8f4f 	dsb	sy
 8002910:	e7fe      	b.n	8002910 <vTaskStartScheduler+0x48>
}
 8002912:	b009      	add	sp, #36	@ 0x24
 8002914:	bd30      	pop	{r4, r5, pc}
 8002916:	f04f 0350 	mov.w	r3, #80	@ 0x50
 800291a:	f383 8811 	msr	BASEPRI, r3
 800291e:	f3bf 8f6f 	isb	sy
 8002922:	f3bf 8f4f 	dsb	sy
		xNextTaskUnblockTime = portMAX_DELAY;
 8002926:	4908      	ldr	r1, [pc, #32]	@ (8002948 <vTaskStartScheduler+0x80>)
		xSchedulerRunning = pdTRUE;
 8002928:	4a08      	ldr	r2, [pc, #32]	@ (800294c <vTaskStartScheduler+0x84>)
		xTickCount = ( TickType_t ) configINITIAL_TICK_COUNT;
 800292a:	4b09      	ldr	r3, [pc, #36]	@ (8002950 <vTaskStartScheduler+0x88>)
		xNextTaskUnblockTime = portMAX_DELAY;
 800292c:	f04f 35ff 	mov.w	r5, #**********
 8002930:	600d      	str	r5, [r1, #0]
		xSchedulerRunning = pdTRUE;
 8002932:	6010      	str	r0, [r2, #0]
		xTickCount = ( TickType_t ) configINITIAL_TICK_COUNT;
 8002934:	601c      	str	r4, [r3, #0]
}
 8002936:	b009      	add	sp, #36	@ 0x24
 8002938:	e8bd 4030 	ldmia.w	sp!, {r4, r5, lr}
		if( xPortStartScheduler() != pdFALSE )
 800293c:	f000 bdfc 	b.w	8003538 <xPortStartScheduler>
 8002940:	08003948 	.word	0x08003948
 8002944:	0800275d 	.word	0x0800275d
 8002948:	2000082c 	.word	0x2000082c
 800294c:	20000840 	.word	0x20000840
 8002950:	20000848 	.word	0x20000848

08002954 <vTaskSuspendAll>:
	++uxSchedulerSuspended;
 8002954:	4a02      	ldr	r2, [pc, #8]	@ (8002960 <vTaskSuspendAll+0xc>)
 8002956:	6813      	ldr	r3, [r2, #0]
 8002958:	3301      	adds	r3, #1
 800295a:	6013      	str	r3, [r2, #0]
}
 800295c:	4770      	bx	lr
 800295e:	bf00      	nop
 8002960:	20000828 	.word	0x20000828

08002964 <xTaskGetTickCount>:
		xTicks = xTickCount;
 8002964:	4b01      	ldr	r3, [pc, #4]	@ (800296c <xTaskGetTickCount+0x8>)
 8002966:	6818      	ldr	r0, [r3, #0]
}
 8002968:	4770      	bx	lr
 800296a:	bf00      	nop
 800296c:	20000848 	.word	0x20000848

08002970 <xTaskIncrementTick>:
{
 8002970:	e92d 4ff0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
	if( uxSchedulerSuspended == ( UBaseType_t ) pdFALSE )
 8002974:	4b4c      	ldr	r3, [pc, #304]	@ (8002aa8 <xTaskIncrementTick+0x138>)
 8002976:	681b      	ldr	r3, [r3, #0]
{
 8002978:	b083      	sub	sp, #12
	if( uxSchedulerSuspended == ( UBaseType_t ) pdFALSE )
 800297a:	2b00      	cmp	r3, #0
 800297c:	d145      	bne.n	8002a0a <xTaskIncrementTick+0x9a>
		const TickType_t xConstTickCount = xTickCount + ( TickType_t ) 1;
 800297e:	4b4b      	ldr	r3, [pc, #300]	@ (8002aac <xTaskIncrementTick+0x13c>)
 8002980:	681e      	ldr	r6, [r3, #0]
 8002982:	3601      	adds	r6, #1
		xTickCount = xConstTickCount;
 8002984:	601e      	str	r6, [r3, #0]
		if( xConstTickCount == ( TickType_t ) 0U ) /*lint !e774 'if' does not always evaluate to false as it is looking for an overflow. */
 8002986:	2e00      	cmp	r6, #0
 8002988:	d048      	beq.n	8002a1c <xTaskIncrementTick+0xac>
 800298a:	4b49      	ldr	r3, [pc, #292]	@ (8002ab0 <xTaskIncrementTick+0x140>)
 800298c:	9301      	str	r3, [sp, #4]
		if( xConstTickCount >= xNextTaskUnblockTime )
 800298e:	9b01      	ldr	r3, [sp, #4]
 8002990:	681b      	ldr	r3, [r3, #0]
 8002992:	429e      	cmp	r6, r3
 8002994:	d34f      	bcc.n	8002a36 <xTaskIncrementTick+0xc6>
				if( listLIST_IS_EMPTY( pxDelayedTaskList ) != pdFALSE )
 8002996:	4f47      	ldr	r7, [pc, #284]	@ (8002ab4 <xTaskIncrementTick+0x144>)
 8002998:	f8df 912c 	ldr.w	r9, [pc, #300]	@ 8002ac8 <xTaskIncrementTick+0x158>
 800299c:	683b      	ldr	r3, [r7, #0]
 800299e:	f8df a12c 	ldr.w	sl, [pc, #300]	@ 8002acc <xTaskIncrementTick+0x15c>
 80029a2:	681d      	ldr	r5, [r3, #0]
 80029a4:	2d00      	cmp	r5, #0
 80029a6:	d072      	beq.n	8002a8e <xTaskIncrementTick+0x11e>
					prvAddTaskToReadyList( pxTCB );
 80029a8:	f8df 8124 	ldr.w	r8, [pc, #292]	@ 8002ad0 <xTaskIncrementTick+0x160>
BaseType_t xSwitchRequired = pdFALSE;
 80029ac:	2500      	movs	r5, #0
 80029ae:	e020      	b.n	80029f2 <xTaskIncrementTick+0x82>
					( void ) uxListRemove( &( pxTCB->xStateListItem ) );
 80029b0:	f7ff fa68 	bl	8001e84 <uxListRemove>
					if( listLIST_ITEM_CONTAINER( &( pxTCB->xEventListItem ) ) != NULL )
 80029b4:	6aa3      	ldr	r3, [r4, #40]	@ 0x28
						( void ) uxListRemove( &( pxTCB->xEventListItem ) );
 80029b6:	f104 0018 	add.w	r0, r4, #24
					if( listLIST_ITEM_CONTAINER( &( pxTCB->xEventListItem ) ) != NULL )
 80029ba:	b10b      	cbz	r3, 80029c0 <xTaskIncrementTick+0x50>
						( void ) uxListRemove( &( pxTCB->xEventListItem ) );
 80029bc:	f7ff fa62 	bl	8001e84 <uxListRemove>
					prvAddTaskToReadyList( pxTCB );
 80029c0:	6ae0      	ldr	r0, [r4, #44]	@ 0x2c
 80029c2:	f8d8 3000 	ldr.w	r3, [r8]
 80029c6:	4298      	cmp	r0, r3
 80029c8:	bf88      	it	hi
 80029ca:	f8c8 0000 	strhi.w	r0, [r8]
 80029ce:	eb00 0080 	add.w	r0, r0, r0, lsl #2
 80029d2:	4659      	mov	r1, fp
 80029d4:	eb09 0080 	add.w	r0, r9, r0, lsl #2
 80029d8:	f7ff fa2e 	bl	8001e38 <vListInsertEnd>
						if( pxTCB->uxPriority >= pxCurrentTCB->uxPriority )
 80029dc:	f8da 3000 	ldr.w	r3, [sl]
 80029e0:	6ae2      	ldr	r2, [r4, #44]	@ 0x2c
 80029e2:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
							xSwitchRequired = pdTRUE;
 80029e4:	429a      	cmp	r2, r3
 80029e6:	bf28      	it	cs
 80029e8:	2501      	movcs	r5, #1
				if( listLIST_IS_EMPTY( pxDelayedTaskList ) != pdFALSE )
 80029ea:	683b      	ldr	r3, [r7, #0]
 80029ec:	681b      	ldr	r3, [r3, #0]
 80029ee:	2b00      	cmp	r3, #0
 80029f0:	d04d      	beq.n	8002a8e <xTaskIncrementTick+0x11e>
					pxTCB = listGET_OWNER_OF_HEAD_ENTRY( pxDelayedTaskList ); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 80029f2:	683b      	ldr	r3, [r7, #0]
 80029f4:	68db      	ldr	r3, [r3, #12]
 80029f6:	68dc      	ldr	r4, [r3, #12]
					xItemValue = listGET_LIST_ITEM_VALUE( &( pxTCB->xStateListItem ) );
 80029f8:	6863      	ldr	r3, [r4, #4]
					( void ) uxListRemove( &( pxTCB->xStateListItem ) );
 80029fa:	f104 0b04 	add.w	fp, r4, #4
					if( xConstTickCount < xItemValue )
 80029fe:	429e      	cmp	r6, r3
					( void ) uxListRemove( &( pxTCB->xStateListItem ) );
 8002a00:	4658      	mov	r0, fp
					if( xConstTickCount < xItemValue )
 8002a02:	d2d5      	bcs.n	80029b0 <xTaskIncrementTick+0x40>
						xNextTaskUnblockTime = xItemValue;
 8002a04:	9a01      	ldr	r2, [sp, #4]
 8002a06:	6013      	str	r3, [r2, #0]
						break; /*lint !e9011 Code structure here is deedmed easier to understand with multiple breaks. */
 8002a08:	e01a      	b.n	8002a40 <xTaskIncrementTick+0xd0>
		++xPendedTicks;
 8002a0a:	4a2b      	ldr	r2, [pc, #172]	@ (8002ab8 <xTaskIncrementTick+0x148>)
 8002a0c:	6813      	ldr	r3, [r2, #0]
BaseType_t xSwitchRequired = pdFALSE;
 8002a0e:	2500      	movs	r5, #0
		++xPendedTicks;
 8002a10:	3301      	adds	r3, #1
}
 8002a12:	4628      	mov	r0, r5
		++xPendedTicks;
 8002a14:	6013      	str	r3, [r2, #0]
}
 8002a16:	b003      	add	sp, #12
 8002a18:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
			taskSWITCH_DELAYED_LISTS();
 8002a1c:	4b25      	ldr	r3, [pc, #148]	@ (8002ab4 <xTaskIncrementTick+0x144>)
 8002a1e:	681a      	ldr	r2, [r3, #0]
 8002a20:	6812      	ldr	r2, [r2, #0]
 8002a22:	b30a      	cbz	r2, 8002a68 <xTaskIncrementTick+0xf8>
 8002a24:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002a28:	f383 8811 	msr	BASEPRI, r3
 8002a2c:	f3bf 8f6f 	isb	sy
 8002a30:	f3bf 8f4f 	dsb	sy
 8002a34:	e7fe      	b.n	8002a34 <xTaskIncrementTick+0xc4>
 8002a36:	f8df 9090 	ldr.w	r9, [pc, #144]	@ 8002ac8 <xTaskIncrementTick+0x158>
 8002a3a:	f8df a090 	ldr.w	sl, [pc, #144]	@ 8002acc <xTaskIncrementTick+0x15c>
BaseType_t xSwitchRequired = pdFALSE;
 8002a3e:	2500      	movs	r5, #0
			if( listCURRENT_LIST_LENGTH( &( pxReadyTasksLists[ pxCurrentTCB->uxPriority ] ) ) > ( UBaseType_t ) 1 )
 8002a40:	f8da 3000 	ldr.w	r3, [sl]
			if( xYieldPending != pdFALSE )
 8002a44:	491d      	ldr	r1, [pc, #116]	@ (8002abc <xTaskIncrementTick+0x14c>)
			if( listCURRENT_LIST_LENGTH( &( pxReadyTasksLists[ pxCurrentTCB->uxPriority ] ) ) > ( UBaseType_t ) 1 )
 8002a46:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8002a48:	eb03 0383 	add.w	r3, r3, r3, lsl #2
 8002a4c:	009b      	lsls	r3, r3, #2
 8002a4e:	f859 2003 	ldr.w	r2, [r9, r3]
			if( xYieldPending != pdFALSE )
 8002a52:	680b      	ldr	r3, [r1, #0]
				xSwitchRequired = pdTRUE;
 8002a54:	2a02      	cmp	r2, #2
 8002a56:	bf28      	it	cs
 8002a58:	2501      	movcs	r5, #1
				xSwitchRequired = pdTRUE;
 8002a5a:	2b00      	cmp	r3, #0
 8002a5c:	bf18      	it	ne
 8002a5e:	2501      	movne	r5, #1
}
 8002a60:	4628      	mov	r0, r5
 8002a62:	b003      	add	sp, #12
 8002a64:	e8bd 8ff0 	ldmia.w	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, pc}
			taskSWITCH_DELAYED_LISTS();
 8002a68:	4a15      	ldr	r2, [pc, #84]	@ (8002ac0 <xTaskIncrementTick+0x150>)
 8002a6a:	6818      	ldr	r0, [r3, #0]
 8002a6c:	6811      	ldr	r1, [r2, #0]
 8002a6e:	6019      	str	r1, [r3, #0]
 8002a70:	4914      	ldr	r1, [pc, #80]	@ (8002ac4 <xTaskIncrementTick+0x154>)
 8002a72:	6010      	str	r0, [r2, #0]
 8002a74:	680a      	ldr	r2, [r1, #0]
 8002a76:	3201      	adds	r2, #1
 8002a78:	600a      	str	r2, [r1, #0]
	if( listLIST_IS_EMPTY( pxDelayedTaskList ) != pdFALSE )
 8002a7a:	681a      	ldr	r2, [r3, #0]
 8002a7c:	6812      	ldr	r2, [r2, #0]
 8002a7e:	b95a      	cbnz	r2, 8002a98 <xTaskIncrementTick+0x128>
		xNextTaskUnblockTime = portMAX_DELAY;
 8002a80:	4b0b      	ldr	r3, [pc, #44]	@ (8002ab0 <xTaskIncrementTick+0x140>)
 8002a82:	9301      	str	r3, [sp, #4]
 8002a84:	461a      	mov	r2, r3
 8002a86:	f04f 33ff 	mov.w	r3, #**********
 8002a8a:	6013      	str	r3, [r2, #0]
 8002a8c:	e77f      	b.n	800298e <xTaskIncrementTick+0x1e>
					xNextTaskUnblockTime = portMAX_DELAY; /*lint !e961 MISRA exception as the casts are only redundant for some ports. */
 8002a8e:	9a01      	ldr	r2, [sp, #4]
 8002a90:	f04f 33ff 	mov.w	r3, #**********
 8002a94:	6013      	str	r3, [r2, #0]
					break;
 8002a96:	e7d3      	b.n	8002a40 <xTaskIncrementTick+0xd0>
		( pxTCB ) = listGET_OWNER_OF_HEAD_ENTRY( pxDelayedTaskList ); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002a98:	681b      	ldr	r3, [r3, #0]
		xNextTaskUnblockTime = listGET_LIST_ITEM_VALUE( &( ( pxTCB )->xStateListItem ) );
 8002a9a:	4a05      	ldr	r2, [pc, #20]	@ (8002ab0 <xTaskIncrementTick+0x140>)
		( pxTCB ) = listGET_OWNER_OF_HEAD_ENTRY( pxDelayedTaskList ); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002a9c:	68db      	ldr	r3, [r3, #12]
		xNextTaskUnblockTime = listGET_LIST_ITEM_VALUE( &( ( pxTCB )->xStateListItem ) );
 8002a9e:	9201      	str	r2, [sp, #4]
 8002aa0:	68db      	ldr	r3, [r3, #12]
 8002aa2:	685b      	ldr	r3, [r3, #4]
 8002aa4:	6013      	str	r3, [r2, #0]
}
 8002aa6:	e772      	b.n	800298e <xTaskIncrementTick+0x1e>
 8002aa8:	20000828 	.word	0x20000828
 8002aac:	20000848 	.word	0x20000848
 8002ab0:	2000082c 	.word	0x2000082c
 8002ab4:	20000894 	.word	0x20000894
 8002ab8:	2000083c 	.word	0x2000083c
 8002abc:	20000838 	.word	0x20000838
 8002ac0:	20000890 	.word	0x20000890
 8002ac4:	20000834 	.word	0x20000834
 8002ac8:	200008c0 	.word	0x200008c0
 8002acc:	20000d20 	.word	0x20000d20
 8002ad0:	20000844 	.word	0x20000844

08002ad4 <xTaskResumeAll.part.0>:
BaseType_t xTaskResumeAll( void )
 8002ad4:	e92d 4ff8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, sl, fp, lr}
	taskENTER_CRITICAL();
 8002ad8:	f000 fc9c 	bl	8003414 <vPortEnterCritical>
		--uxSchedulerSuspended;
 8002adc:	4b37      	ldr	r3, [pc, #220]	@ (8002bbc <xTaskResumeAll.part.0+0xe8>)
 8002ade:	681a      	ldr	r2, [r3, #0]
 8002ae0:	3a01      	subs	r2, #1
 8002ae2:	601a      	str	r2, [r3, #0]
		if( uxSchedulerSuspended == ( UBaseType_t ) pdFALSE )
 8002ae4:	681b      	ldr	r3, [r3, #0]
 8002ae6:	2b00      	cmp	r3, #0
 8002ae8:	d157      	bne.n	8002b9a <xTaskResumeAll.part.0+0xc6>
			if( uxCurrentNumberOfTasks > ( UBaseType_t ) 0U )
 8002aea:	4b35      	ldr	r3, [pc, #212]	@ (8002bc0 <xTaskResumeAll.part.0+0xec>)
 8002aec:	681b      	ldr	r3, [r3, #0]
 8002aee:	2b00      	cmp	r3, #0
 8002af0:	d053      	beq.n	8002b9a <xTaskResumeAll.part.0+0xc6>
				while( listLIST_IS_EMPTY( &xPendingReadyList ) == pdFALSE )
 8002af2:	4d34      	ldr	r5, [pc, #208]	@ (8002bc4 <xTaskResumeAll.part.0+0xf0>)
 8002af4:	682b      	ldr	r3, [r5, #0]
 8002af6:	2b00      	cmp	r3, #0
 8002af8:	d05c      	beq.n	8002bb4 <xTaskResumeAll.part.0+0xe0>
 8002afa:	4e33      	ldr	r6, [pc, #204]	@ (8002bc8 <xTaskResumeAll.part.0+0xf4>)
 8002afc:	f8df 80dc 	ldr.w	r8, [pc, #220]	@ 8002bdc <xTaskResumeAll.part.0+0x108>
 8002b00:	4f32      	ldr	r7, [pc, #200]	@ (8002bcc <xTaskResumeAll.part.0+0xf8>)
 8002b02:	f8df 90dc 	ldr.w	r9, [pc, #220]	@ 8002be0 <xTaskResumeAll.part.0+0x10c>
						xYieldPending = pdTRUE;
 8002b06:	f04f 0a01 	mov.w	sl, #1
					pxTCB = listGET_OWNER_OF_HEAD_ENTRY( ( &xPendingReadyList ) ); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002b0a:	68eb      	ldr	r3, [r5, #12]
 8002b0c:	68dc      	ldr	r4, [r3, #12]
					( void ) uxListRemove( &( pxTCB->xStateListItem ) );
 8002b0e:	f104 0b04 	add.w	fp, r4, #4
					( void ) uxListRemove( &( pxTCB->xEventListItem ) );
 8002b12:	f104 0018 	add.w	r0, r4, #24
 8002b16:	f7ff f9b5 	bl	8001e84 <uxListRemove>
					( void ) uxListRemove( &( pxTCB->xStateListItem ) );
 8002b1a:	4658      	mov	r0, fp
 8002b1c:	f7ff f9b2 	bl	8001e84 <uxListRemove>
					prvAddTaskToReadyList( pxTCB );
 8002b20:	6ae3      	ldr	r3, [r4, #44]	@ 0x2c
 8002b22:	6832      	ldr	r2, [r6, #0]
 8002b24:	eb03 0083 	add.w	r0, r3, r3, lsl #2
 8002b28:	4293      	cmp	r3, r2
 8002b2a:	4659      	mov	r1, fp
 8002b2c:	eb08 0080 	add.w	r0, r8, r0, lsl #2
 8002b30:	bf88      	it	hi
 8002b32:	6033      	strhi	r3, [r6, #0]
 8002b34:	f7ff f980 	bl	8001e38 <vListInsertEnd>
					if( pxTCB->uxPriority >= pxCurrentTCB->uxPriority )
 8002b38:	683b      	ldr	r3, [r7, #0]
 8002b3a:	6ae2      	ldr	r2, [r4, #44]	@ 0x2c
 8002b3c:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8002b3e:	429a      	cmp	r2, r3
						xYieldPending = pdTRUE;
 8002b40:	bf28      	it	cs
 8002b42:	f8c9 a000 	strcs.w	sl, [r9]
				while( listLIST_IS_EMPTY( &xPendingReadyList ) == pdFALSE )
 8002b46:	682b      	ldr	r3, [r5, #0]
 8002b48:	2b00      	cmp	r3, #0
 8002b4a:	d1de      	bne.n	8002b0a <xTaskResumeAll.part.0+0x36>
	if( listLIST_IS_EMPTY( pxDelayedTaskList ) != pdFALSE )
 8002b4c:	4b20      	ldr	r3, [pc, #128]	@ (8002bd0 <xTaskResumeAll.part.0+0xfc>)
 8002b4e:	681a      	ldr	r2, [r3, #0]
 8002b50:	6812      	ldr	r2, [r2, #0]
 8002b52:	bb42      	cbnz	r2, 8002ba6 <xTaskResumeAll.part.0+0xd2>
		xNextTaskUnblockTime = portMAX_DELAY;
 8002b54:	4b1f      	ldr	r3, [pc, #124]	@ (8002bd4 <xTaskResumeAll.part.0+0x100>)
 8002b56:	f04f 32ff 	mov.w	r2, #**********
 8002b5a:	601a      	str	r2, [r3, #0]
					TickType_t xPendedCounts = xPendedTicks; /* Non-volatile copy. */
 8002b5c:	4e1e      	ldr	r6, [pc, #120]	@ (8002bd8 <xTaskResumeAll.part.0+0x104>)
 8002b5e:	6834      	ldr	r4, [r6, #0]
					if( xPendedCounts > ( TickType_t ) 0U )
 8002b60:	b144      	cbz	r4, 8002b74 <xTaskResumeAll.part.0+0xa0>
								xYieldPending = pdTRUE;
 8002b62:	2501      	movs	r5, #1
							if( xTaskIncrementTick() != pdFALSE )
 8002b64:	f7ff ff04 	bl	8002970 <xTaskIncrementTick>
 8002b68:	b108      	cbz	r0, 8002b6e <xTaskResumeAll.part.0+0x9a>
								xYieldPending = pdTRUE;
 8002b6a:	f8c9 5000 	str.w	r5, [r9]
						} while( xPendedCounts > ( TickType_t ) 0U );
 8002b6e:	3c01      	subs	r4, #1
 8002b70:	d1f8      	bne.n	8002b64 <xTaskResumeAll.part.0+0x90>
						xPendedTicks = 0;
 8002b72:	6034      	str	r4, [r6, #0]
				if( xYieldPending != pdFALSE )
 8002b74:	f8d9 3000 	ldr.w	r3, [r9]
 8002b78:	b17b      	cbz	r3, 8002b9a <xTaskResumeAll.part.0+0xc6>
					taskYIELD_IF_USING_PREEMPTION();
 8002b7a:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 8002b7e:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 8002b82:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
 8002b86:	f3bf 8f4f 	dsb	sy
 8002b8a:	f3bf 8f6f 	isb	sy
						xAlreadyYielded = pdTRUE;
 8002b8e:	2401      	movs	r4, #1
	taskEXIT_CRITICAL();
 8002b90:	f000 fc62 	bl	8003458 <vPortExitCritical>
}
 8002b94:	4620      	mov	r0, r4
 8002b96:	e8bd 8ff8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, sl, fp, pc}
BaseType_t xAlreadyYielded = pdFALSE;
 8002b9a:	2400      	movs	r4, #0
	taskEXIT_CRITICAL();
 8002b9c:	f000 fc5c 	bl	8003458 <vPortExitCritical>
}
 8002ba0:	4620      	mov	r0, r4
 8002ba2:	e8bd 8ff8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, sl, fp, pc}
		( pxTCB ) = listGET_OWNER_OF_HEAD_ENTRY( pxDelayedTaskList ); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002ba6:	681a      	ldr	r2, [r3, #0]
		xNextTaskUnblockTime = listGET_LIST_ITEM_VALUE( &( ( pxTCB )->xStateListItem ) );
 8002ba8:	4b0a      	ldr	r3, [pc, #40]	@ (8002bd4 <xTaskResumeAll.part.0+0x100>)
		( pxTCB ) = listGET_OWNER_OF_HEAD_ENTRY( pxDelayedTaskList ); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002baa:	68d2      	ldr	r2, [r2, #12]
		xNextTaskUnblockTime = listGET_LIST_ITEM_VALUE( &( ( pxTCB )->xStateListItem ) );
 8002bac:	68d2      	ldr	r2, [r2, #12]
 8002bae:	6852      	ldr	r2, [r2, #4]
 8002bb0:	601a      	str	r2, [r3, #0]
}
 8002bb2:	e7d3      	b.n	8002b5c <xTaskResumeAll.part.0+0x88>
 8002bb4:	f8df 9028 	ldr.w	r9, [pc, #40]	@ 8002be0 <xTaskResumeAll.part.0+0x10c>
 8002bb8:	e7d0      	b.n	8002b5c <xTaskResumeAll.part.0+0x88>
 8002bba:	bf00      	nop
 8002bbc:	20000828 	.word	0x20000828
 8002bc0:	2000084c 	.word	0x2000084c
 8002bc4:	2000087c 	.word	0x2000087c
 8002bc8:	20000844 	.word	0x20000844
 8002bcc:	20000d20 	.word	0x20000d20
 8002bd0:	20000894 	.word	0x20000894
 8002bd4:	2000082c 	.word	0x2000082c
 8002bd8:	2000083c 	.word	0x2000083c
 8002bdc:	200008c0 	.word	0x200008c0
 8002be0:	20000838 	.word	0x20000838

08002be4 <xTaskResumeAll>:
	configASSERT( uxSchedulerSuspended );
 8002be4:	4b06      	ldr	r3, [pc, #24]	@ (8002c00 <xTaskResumeAll+0x1c>)
 8002be6:	681b      	ldr	r3, [r3, #0]
 8002be8:	b943      	cbnz	r3, 8002bfc <xTaskResumeAll+0x18>
 8002bea:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002bee:	f383 8811 	msr	BASEPRI, r3
 8002bf2:	f3bf 8f6f 	isb	sy
 8002bf6:	f3bf 8f4f 	dsb	sy
 8002bfa:	e7fe      	b.n	8002bfa <xTaskResumeAll+0x16>
 8002bfc:	f7ff bf6a 	b.w	8002ad4 <xTaskResumeAll.part.0>
 8002c00:	20000828 	.word	0x20000828

08002c04 <vTaskDelay>:
	{
 8002c04:	b510      	push	{r4, lr}
		if( xTicksToDelay > ( TickType_t ) 0U )
 8002c06:	b950      	cbnz	r0, 8002c1e <vTaskDelay+0x1a>
			portYIELD_WITHIN_API();
 8002c08:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 8002c0c:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 8002c10:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
 8002c14:	f3bf 8f4f 	dsb	sy
 8002c18:	f3bf 8f6f 	isb	sy
	}
 8002c1c:	bd10      	pop	{r4, pc}
			configASSERT( uxSchedulerSuspended == 0 );
 8002c1e:	4c10      	ldr	r4, [pc, #64]	@ (8002c60 <vTaskDelay+0x5c>)
 8002c20:	6821      	ldr	r1, [r4, #0]
 8002c22:	b141      	cbz	r1, 8002c36 <vTaskDelay+0x32>
 8002c24:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002c28:	f383 8811 	msr	BASEPRI, r3
 8002c2c:	f3bf 8f6f 	isb	sy
 8002c30:	f3bf 8f4f 	dsb	sy
 8002c34:	e7fe      	b.n	8002c34 <vTaskDelay+0x30>
	++uxSchedulerSuspended;
 8002c36:	6823      	ldr	r3, [r4, #0]
 8002c38:	3301      	adds	r3, #1
 8002c3a:	6023      	str	r3, [r4, #0]
				prvAddCurrentTaskToDelayedList( xTicksToDelay, pdFALSE );
 8002c3c:	f7ff fcf6 	bl	800262c <prvAddCurrentTaskToDelayedList>
	configASSERT( uxSchedulerSuspended );
 8002c40:	6823      	ldr	r3, [r4, #0]
 8002c42:	b943      	cbnz	r3, 8002c56 <vTaskDelay+0x52>
 8002c44:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002c48:	f383 8811 	msr	BASEPRI, r3
 8002c4c:	f3bf 8f6f 	isb	sy
 8002c50:	f3bf 8f4f 	dsb	sy
 8002c54:	e7fe      	b.n	8002c54 <vTaskDelay+0x50>
 8002c56:	f7ff ff3d 	bl	8002ad4 <xTaskResumeAll.part.0>
		if( xAlreadyYielded == pdFALSE )
 8002c5a:	2800      	cmp	r0, #0
 8002c5c:	d0d4      	beq.n	8002c08 <vTaskDelay+0x4>
	}
 8002c5e:	bd10      	pop	{r4, pc}
 8002c60:	20000828 	.word	0x20000828

08002c64 <vTaskSwitchContext>:
	if( uxSchedulerSuspended != ( UBaseType_t ) pdFALSE )
 8002c64:	4b1d      	ldr	r3, [pc, #116]	@ (8002cdc <vTaskSwitchContext+0x78>)
 8002c66:	681b      	ldr	r3, [r3, #0]
 8002c68:	b11b      	cbz	r3, 8002c72 <vTaskSwitchContext+0xe>
		xYieldPending = pdTRUE;
 8002c6a:	4b1d      	ldr	r3, [pc, #116]	@ (8002ce0 <vTaskSwitchContext+0x7c>)
 8002c6c:	2201      	movs	r2, #1
 8002c6e:	601a      	str	r2, [r3, #0]
 8002c70:	4770      	bx	lr
		xYieldPending = pdFALSE;
 8002c72:	4a1b      	ldr	r2, [pc, #108]	@ (8002ce0 <vTaskSwitchContext+0x7c>)
		taskSELECT_HIGHEST_PRIORITY_TASK(); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002c74:	491b      	ldr	r1, [pc, #108]	@ (8002ce4 <vTaskSwitchContext+0x80>)
{
 8002c76:	b410      	push	{r4}
		taskSELECT_HIGHEST_PRIORITY_TASK(); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002c78:	4c1b      	ldr	r4, [pc, #108]	@ (8002ce8 <vTaskSwitchContext+0x84>)
		xYieldPending = pdFALSE;
 8002c7a:	6013      	str	r3, [r2, #0]
		taskSELECT_HIGHEST_PRIORITY_TASK(); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002c7c:	6823      	ldr	r3, [r4, #0]
 8002c7e:	eb03 0283 	add.w	r2, r3, r3, lsl #2
 8002c82:	0092      	lsls	r2, r2, #2
 8002c84:	0098      	lsls	r0, r3, #2
 8002c86:	588a      	ldr	r2, [r1, r2]
 8002c88:	b942      	cbnz	r2, 8002c9c <vTaskSwitchContext+0x38>
 8002c8a:	b1f3      	cbz	r3, 8002cca <vTaskSwitchContext+0x66>
 8002c8c:	3b01      	subs	r3, #1
 8002c8e:	eb03 0283 	add.w	r2, r3, r3, lsl #2
 8002c92:	0098      	lsls	r0, r3, #2
 8002c94:	f851 2022 	ldr.w	r2, [r1, r2, lsl #2]
 8002c98:	2a00      	cmp	r2, #0
 8002c9a:	d0f6      	beq.n	8002c8a <vTaskSwitchContext+0x26>
 8002c9c:	4418      	add	r0, r3
 8002c9e:	eb01 0c80 	add.w	ip, r1, r0, lsl #2
 8002ca2:	4662      	mov	r2, ip
 8002ca4:	f8dc 1004 	ldr.w	r1, [ip, #4]
 8002ca8:	6849      	ldr	r1, [r1, #4]
 8002caa:	f8cc 1004 	str.w	r1, [ip, #4]
 8002cae:	3208      	adds	r2, #8
 8002cb0:	4291      	cmp	r1, r2
 8002cb2:	bf08      	it	eq
 8002cb4:	6849      	ldreq	r1, [r1, #4]
 8002cb6:	4a0d      	ldr	r2, [pc, #52]	@ (8002cec <vTaskSwitchContext+0x88>)
 8002cb8:	bf08      	it	eq
 8002cba:	f8cc 1004 	streq.w	r1, [ip, #4]
 8002cbe:	68c9      	ldr	r1, [r1, #12]
 8002cc0:	6011      	str	r1, [r2, #0]
 8002cc2:	6023      	str	r3, [r4, #0]
}
 8002cc4:	f85d 4b04 	ldr.w	r4, [sp], #4
 8002cc8:	4770      	bx	lr
 8002cca:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002cce:	f383 8811 	msr	BASEPRI, r3
 8002cd2:	f3bf 8f6f 	isb	sy
 8002cd6:	f3bf 8f4f 	dsb	sy
		taskSELECT_HIGHEST_PRIORITY_TASK(); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002cda:	e7fe      	b.n	8002cda <vTaskSwitchContext+0x76>
 8002cdc:	20000828 	.word	0x20000828
 8002ce0:	20000838 	.word	0x20000838
 8002ce4:	200008c0 	.word	0x200008c0
 8002ce8:	20000844 	.word	0x20000844
 8002cec:	20000d20 	.word	0x20000d20

08002cf0 <vTaskPlaceOnEventList>:
	configASSERT( pxEventList );
 8002cf0:	b160      	cbz	r0, 8002d0c <vTaskPlaceOnEventList+0x1c>
{
 8002cf2:	b510      	push	{r4, lr}
	vListInsert( pxEventList, &( pxCurrentTCB->xEventListItem ) );
 8002cf4:	4b0a      	ldr	r3, [pc, #40]	@ (8002d20 <vTaskPlaceOnEventList+0x30>)
 8002cf6:	460c      	mov	r4, r1
 8002cf8:	6819      	ldr	r1, [r3, #0]
 8002cfa:	3118      	adds	r1, #24
 8002cfc:	f7ff f8aa 	bl	8001e54 <vListInsert>
	prvAddCurrentTaskToDelayedList( xTicksToWait, pdTRUE );
 8002d00:	4620      	mov	r0, r4
 8002d02:	2101      	movs	r1, #1
}
 8002d04:	e8bd 4010 	ldmia.w	sp!, {r4, lr}
	prvAddCurrentTaskToDelayedList( xTicksToWait, pdTRUE );
 8002d08:	f7ff bc90 	b.w	800262c <prvAddCurrentTaskToDelayedList>
 8002d0c:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002d10:	f383 8811 	msr	BASEPRI, r3
 8002d14:	f3bf 8f6f 	isb	sy
 8002d18:	f3bf 8f4f 	dsb	sy
	configASSERT( pxEventList );
 8002d1c:	e7fe      	b.n	8002d1c <vTaskPlaceOnEventList+0x2c>
 8002d1e:	bf00      	nop
 8002d20:	20000d20 	.word	0x20000d20

08002d24 <vTaskPlaceOnEventListRestricted>:
	{
 8002d24:	b538      	push	{r3, r4, r5, lr}
		configASSERT( pxEventList );
 8002d26:	b180      	cbz	r0, 8002d4a <vTaskPlaceOnEventListRestricted+0x26>
		vListInsertEnd( pxEventList, &( pxCurrentTCB->xEventListItem ) );
 8002d28:	4b0c      	ldr	r3, [pc, #48]	@ (8002d5c <vTaskPlaceOnEventListRestricted+0x38>)
 8002d2a:	460d      	mov	r5, r1
 8002d2c:	6819      	ldr	r1, [r3, #0]
 8002d2e:	4614      	mov	r4, r2
 8002d30:	3118      	adds	r1, #24
 8002d32:	f7ff f881 	bl	8001e38 <vListInsertEnd>
			xTicksToWait = portMAX_DELAY;
 8002d36:	2c00      	cmp	r4, #0
		prvAddCurrentTaskToDelayedList( xTicksToWait, xWaitIndefinitely );
 8002d38:	4621      	mov	r1, r4
 8002d3a:	bf0c      	ite	eq
 8002d3c:	4628      	moveq	r0, r5
 8002d3e:	f04f 30ff 	movne.w	r0, #**********
	}
 8002d42:	e8bd 4038 	ldmia.w	sp!, {r3, r4, r5, lr}
		prvAddCurrentTaskToDelayedList( xTicksToWait, xWaitIndefinitely );
 8002d46:	f7ff bc71 	b.w	800262c <prvAddCurrentTaskToDelayedList>
 8002d4a:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002d4e:	f383 8811 	msr	BASEPRI, r3
 8002d52:	f3bf 8f6f 	isb	sy
 8002d56:	f3bf 8f4f 	dsb	sy
		configASSERT( pxEventList );
 8002d5a:	e7fe      	b.n	8002d5a <vTaskPlaceOnEventListRestricted+0x36>
 8002d5c:	20000d20 	.word	0x20000d20

08002d60 <xTaskRemoveFromEventList>:
{
 8002d60:	b538      	push	{r3, r4, r5, lr}
	pxUnblockedTCB = listGET_OWNER_OF_HEAD_ENTRY( pxEventList ); /*lint !e9079 void * is used as this macro is used with timers and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8002d62:	68c3      	ldr	r3, [r0, #12]
 8002d64:	68dc      	ldr	r4, [r3, #12]
	configASSERT( pxUnblockedTCB );
 8002d66:	b34c      	cbz	r4, 8002dbc <xTaskRemoveFromEventList+0x5c>
	( void ) uxListRemove( &( pxUnblockedTCB->xEventListItem ) );
 8002d68:	f104 0518 	add.w	r5, r4, #24
 8002d6c:	4628      	mov	r0, r5
 8002d6e:	f7ff f889 	bl	8001e84 <uxListRemove>
	if( uxSchedulerSuspended == ( UBaseType_t ) pdFALSE )
 8002d72:	4b17      	ldr	r3, [pc, #92]	@ (8002dd0 <xTaskRemoveFromEventList+0x70>)
 8002d74:	681b      	ldr	r3, [r3, #0]
 8002d76:	b173      	cbz	r3, 8002d96 <xTaskRemoveFromEventList+0x36>
		vListInsertEnd( &( xPendingReadyList ), &( pxUnblockedTCB->xEventListItem ) );
 8002d78:	4816      	ldr	r0, [pc, #88]	@ (8002dd4 <xTaskRemoveFromEventList+0x74>)
 8002d7a:	4629      	mov	r1, r5
 8002d7c:	f7ff f85c 	bl	8001e38 <vListInsertEnd>
	if( pxUnblockedTCB->uxPriority > pxCurrentTCB->uxPriority )
 8002d80:	4b15      	ldr	r3, [pc, #84]	@ (8002dd8 <xTaskRemoveFromEventList+0x78>)
 8002d82:	6ae2      	ldr	r2, [r4, #44]	@ 0x2c
 8002d84:	681b      	ldr	r3, [r3, #0]
 8002d86:	6adb      	ldr	r3, [r3, #44]	@ 0x2c
 8002d88:	429a      	cmp	r2, r3
		xYieldPending = pdTRUE;
 8002d8a:	bf83      	ittte	hi
 8002d8c:	4b13      	ldrhi	r3, [pc, #76]	@ (8002ddc <xTaskRemoveFromEventList+0x7c>)
 8002d8e:	2001      	movhi	r0, #1
 8002d90:	6018      	strhi	r0, [r3, #0]
		xReturn = pdFALSE;
 8002d92:	2000      	movls	r0, #0
}
 8002d94:	bd38      	pop	{r3, r4, r5, pc}
		( void ) uxListRemove( &( pxUnblockedTCB->xStateListItem ) );
 8002d96:	1d25      	adds	r5, r4, #4
 8002d98:	4628      	mov	r0, r5
 8002d9a:	f7ff f873 	bl	8001e84 <uxListRemove>
		prvAddTaskToReadyList( pxUnblockedTCB );
 8002d9e:	4a10      	ldr	r2, [pc, #64]	@ (8002de0 <xTaskRemoveFromEventList+0x80>)
 8002da0:	6ae3      	ldr	r3, [r4, #44]	@ 0x2c
 8002da2:	6811      	ldr	r1, [r2, #0]
 8002da4:	480f      	ldr	r0, [pc, #60]	@ (8002de4 <xTaskRemoveFromEventList+0x84>)
 8002da6:	428b      	cmp	r3, r1
 8002da8:	bf88      	it	hi
 8002daa:	6013      	strhi	r3, [r2, #0]
 8002dac:	eb03 0383 	add.w	r3, r3, r3, lsl #2
 8002db0:	4629      	mov	r1, r5
 8002db2:	eb00 0083 	add.w	r0, r0, r3, lsl #2
 8002db6:	f7ff f83f 	bl	8001e38 <vListInsertEnd>
 8002dba:	e7e1      	b.n	8002d80 <xTaskRemoveFromEventList+0x20>
 8002dbc:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002dc0:	f383 8811 	msr	BASEPRI, r3
 8002dc4:	f3bf 8f6f 	isb	sy
 8002dc8:	f3bf 8f4f 	dsb	sy
	configASSERT( pxUnblockedTCB );
 8002dcc:	e7fe      	b.n	8002dcc <xTaskRemoveFromEventList+0x6c>
 8002dce:	bf00      	nop
 8002dd0:	20000828 	.word	0x20000828
 8002dd4:	2000087c 	.word	0x2000087c
 8002dd8:	20000d20 	.word	0x20000d20
 8002ddc:	20000838 	.word	0x20000838
 8002de0:	20000844 	.word	0x20000844
 8002de4:	200008c0 	.word	0x200008c0

08002de8 <vTaskInternalSetTimeOutState>:
	pxTimeOut->xOverflowCount = xNumOfOverflows;
 8002de8:	4a03      	ldr	r2, [pc, #12]	@ (8002df8 <vTaskInternalSetTimeOutState+0x10>)
	pxTimeOut->xTimeOnEntering = xTickCount;
 8002dea:	4b04      	ldr	r3, [pc, #16]	@ (8002dfc <vTaskInternalSetTimeOutState+0x14>)
	pxTimeOut->xOverflowCount = xNumOfOverflows;
 8002dec:	6812      	ldr	r2, [r2, #0]
	pxTimeOut->xTimeOnEntering = xTickCount;
 8002dee:	681b      	ldr	r3, [r3, #0]
 8002df0:	e9c0 2300 	strd	r2, r3, [r0]
}
 8002df4:	4770      	bx	lr
 8002df6:	bf00      	nop
 8002df8:	20000834 	.word	0x20000834
 8002dfc:	20000848 	.word	0x20000848

08002e00 <xTaskCheckForTimeOut>:
{
 8002e00:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
	configASSERT( pxTimeOut );
 8002e02:	b308      	cbz	r0, 8002e48 <xTaskCheckForTimeOut+0x48>
	configASSERT( pxTicksToWait );
 8002e04:	460d      	mov	r5, r1
 8002e06:	b1b1      	cbz	r1, 8002e36 <xTaskCheckForTimeOut+0x36>
 8002e08:	4604      	mov	r4, r0
	taskENTER_CRITICAL();
 8002e0a:	f000 fb03 	bl	8003414 <vPortEnterCritical>
			if( *pxTicksToWait == portMAX_DELAY )
 8002e0e:	682b      	ldr	r3, [r5, #0]
		const TickType_t xConstTickCount = xTickCount;
 8002e10:	4a1a      	ldr	r2, [pc, #104]	@ (8002e7c <xTaskCheckForTimeOut+0x7c>)
			if( *pxTicksToWait == portMAX_DELAY )
 8002e12:	1c58      	adds	r0, r3, #1
		const TickType_t xConstTickCount = xTickCount;
 8002e14:	6811      	ldr	r1, [r2, #0]
			if( *pxTicksToWait == portMAX_DELAY )
 8002e16:	d02c      	beq.n	8002e72 <xTaskCheckForTimeOut+0x72>
		if( ( xNumOfOverflows != pxTimeOut->xOverflowCount ) && ( xConstTickCount >= pxTimeOut->xTimeOnEntering ) ) /*lint !e525 Indentation preferred as is to make code within pre-processor directives clearer. */
 8002e18:	f8df c064 	ldr.w	ip, [pc, #100]	@ 8002e80 <xTaskCheckForTimeOut+0x80>
 8002e1c:	e9d4 6000 	ldrd	r6, r0, [r4]
 8002e20:	f8dc 7000 	ldr.w	r7, [ip]
 8002e24:	42be      	cmp	r6, r7
 8002e26:	d018      	beq.n	8002e5a <xTaskCheckForTimeOut+0x5a>
 8002e28:	4288      	cmp	r0, r1
 8002e2a:	d816      	bhi.n	8002e5a <xTaskCheckForTimeOut+0x5a>
			xReturn = pdTRUE;
 8002e2c:	2401      	movs	r4, #1
	taskEXIT_CRITICAL();
 8002e2e:	f000 fb13 	bl	8003458 <vPortExitCritical>
}
 8002e32:	4620      	mov	r0, r4
 8002e34:	bdf8      	pop	{r3, r4, r5, r6, r7, pc}
 8002e36:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002e3a:	f383 8811 	msr	BASEPRI, r3
 8002e3e:	f3bf 8f6f 	isb	sy
 8002e42:	f3bf 8f4f 	dsb	sy
	configASSERT( pxTicksToWait );
 8002e46:	e7fe      	b.n	8002e46 <xTaskCheckForTimeOut+0x46>
 8002e48:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002e4c:	f383 8811 	msr	BASEPRI, r3
 8002e50:	f3bf 8f6f 	isb	sy
 8002e54:	f3bf 8f4f 	dsb	sy
	configASSERT( pxTimeOut );
 8002e58:	e7fe      	b.n	8002e58 <xTaskCheckForTimeOut+0x58>
		const TickType_t xElapsedTime = xConstTickCount - pxTimeOut->xTimeOnEntering;
 8002e5a:	eba1 0e00 	sub.w	lr, r1, r0
		else if( xElapsedTime < *pxTicksToWait ) /*lint !e961 Explicit casting is only redundant with some compilers, whereas others require it to prevent integer conversion errors. */
 8002e5e:	4573      	cmp	r3, lr
 8002e60:	d909      	bls.n	8002e76 <xTaskCheckForTimeOut+0x76>
			*pxTicksToWait -= xElapsedTime;
 8002e62:	1a5b      	subs	r3, r3, r1
	pxTimeOut->xOverflowCount = xNumOfOverflows;
 8002e64:	f8dc 1000 	ldr.w	r1, [ip]
	pxTimeOut->xTimeOnEntering = xTickCount;
 8002e68:	6812      	ldr	r2, [r2, #0]
			*pxTicksToWait -= xElapsedTime;
 8002e6a:	4403      	add	r3, r0
 8002e6c:	602b      	str	r3, [r5, #0]
	pxTimeOut->xTimeOnEntering = xTickCount;
 8002e6e:	e9c4 1200 	strd	r1, r2, [r4]
				xReturn = pdFALSE;
 8002e72:	2400      	movs	r4, #0
 8002e74:	e7db      	b.n	8002e2e <xTaskCheckForTimeOut+0x2e>
			*pxTicksToWait = 0;
 8002e76:	2300      	movs	r3, #0
 8002e78:	602b      	str	r3, [r5, #0]
			xReturn = pdTRUE;
 8002e7a:	e7d7      	b.n	8002e2c <xTaskCheckForTimeOut+0x2c>
 8002e7c:	20000848 	.word	0x20000848
 8002e80:	20000834 	.word	0x20000834

08002e84 <vTaskMissedYield>:
	xYieldPending = pdTRUE;
 8002e84:	4b01      	ldr	r3, [pc, #4]	@ (8002e8c <vTaskMissedYield+0x8>)
 8002e86:	2201      	movs	r2, #1
 8002e88:	601a      	str	r2, [r3, #0]
}
 8002e8a:	4770      	bx	lr
 8002e8c:	20000838 	.word	0x20000838

08002e90 <xTaskGetSchedulerState>:
		if( xSchedulerRunning == pdFALSE )
 8002e90:	4b05      	ldr	r3, [pc, #20]	@ (8002ea8 <xTaskGetSchedulerState+0x18>)
 8002e92:	681b      	ldr	r3, [r3, #0]
 8002e94:	b133      	cbz	r3, 8002ea4 <xTaskGetSchedulerState+0x14>
			if( uxSchedulerSuspended == ( UBaseType_t ) pdFALSE )
 8002e96:	4b05      	ldr	r3, [pc, #20]	@ (8002eac <xTaskGetSchedulerState+0x1c>)
 8002e98:	6818      	ldr	r0, [r3, #0]
 8002e9a:	fab0 f080 	clz	r0, r0
 8002e9e:	0940      	lsrs	r0, r0, #5
 8002ea0:	0040      	lsls	r0, r0, #1
 8002ea2:	4770      	bx	lr
			xReturn = taskSCHEDULER_NOT_STARTED;
 8002ea4:	2001      	movs	r0, #1
	}
 8002ea6:	4770      	bx	lr
 8002ea8:	20000840 	.word	0x20000840
 8002eac:	20000828 	.word	0x20000828

08002eb0 <xTaskPriorityDisinherit>:
		if( pxMutexHolder != NULL )
 8002eb0:	b308      	cbz	r0, 8002ef6 <xTaskPriorityDisinherit+0x46>
	{
 8002eb2:	b538      	push	{r3, r4, r5, lr}
			configASSERT( pxTCB == pxCurrentTCB );
 8002eb4:	4b1d      	ldr	r3, [pc, #116]	@ (8002f2c <xTaskPriorityDisinherit+0x7c>)
 8002eb6:	681c      	ldr	r4, [r3, #0]
 8002eb8:	4284      	cmp	r4, r0
 8002eba:	d008      	beq.n	8002ece <xTaskPriorityDisinherit+0x1e>
 8002ebc:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002ec0:	f383 8811 	msr	BASEPRI, r3
 8002ec4:	f3bf 8f6f 	isb	sy
 8002ec8:	f3bf 8f4f 	dsb	sy
 8002ecc:	e7fe      	b.n	8002ecc <xTaskPriorityDisinherit+0x1c>
			configASSERT( pxTCB->uxMutexesHeld );
 8002ece:	6d23      	ldr	r3, [r4, #80]	@ 0x50
 8002ed0:	b143      	cbz	r3, 8002ee4 <xTaskPriorityDisinherit+0x34>
			if( pxTCB->uxPriority != pxTCB->uxBasePriority )
 8002ed2:	6ae1      	ldr	r1, [r4, #44]	@ 0x2c
 8002ed4:	6ce2      	ldr	r2, [r4, #76]	@ 0x4c
			( pxTCB->uxMutexesHeld )--;
 8002ed6:	3b01      	subs	r3, #1
			if( pxTCB->uxPriority != pxTCB->uxBasePriority )
 8002ed8:	4291      	cmp	r1, r2
			( pxTCB->uxMutexesHeld )--;
 8002eda:	6523      	str	r3, [r4, #80]	@ 0x50
			if( pxTCB->uxPriority != pxTCB->uxBasePriority )
 8002edc:	d000      	beq.n	8002ee0 <xTaskPriorityDisinherit+0x30>
				if( pxTCB->uxMutexesHeld == ( UBaseType_t ) 0 )
 8002ede:	b163      	cbz	r3, 8002efa <xTaskPriorityDisinherit+0x4a>
	BaseType_t xReturn = pdFALSE;
 8002ee0:	2000      	movs	r0, #0
	}
 8002ee2:	bd38      	pop	{r3, r4, r5, pc}
 8002ee4:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002ee8:	f383 8811 	msr	BASEPRI, r3
 8002eec:	f3bf 8f6f 	isb	sy
 8002ef0:	f3bf 8f4f 	dsb	sy
			configASSERT( pxTCB->uxMutexesHeld );
 8002ef4:	e7fe      	b.n	8002ef4 <xTaskPriorityDisinherit+0x44>
	BaseType_t xReturn = pdFALSE;
 8002ef6:	2000      	movs	r0, #0
	}
 8002ef8:	4770      	bx	lr
					if( uxListRemove( &( pxTCB->xStateListItem ) ) == ( UBaseType_t ) 0 )
 8002efa:	1d25      	adds	r5, r4, #4
 8002efc:	4628      	mov	r0, r5
 8002efe:	f7fe ffc1 	bl	8001e84 <uxListRemove>
					pxTCB->uxPriority = pxTCB->uxBasePriority;
 8002f02:	6ce3      	ldr	r3, [r4, #76]	@ 0x4c
					prvAddTaskToReadyList( pxTCB );
 8002f04:	4a0a      	ldr	r2, [pc, #40]	@ (8002f30 <xTaskPriorityDisinherit+0x80>)
					pxTCB->uxPriority = pxTCB->uxBasePriority;
 8002f06:	62e3      	str	r3, [r4, #44]	@ 0x2c
					listSET_LIST_ITEM_VALUE( &( pxTCB->xEventListItem ), ( TickType_t ) configMAX_PRIORITIES - ( TickType_t ) pxTCB->uxPriority ); /*lint !e961 MISRA exception as the casts are only redundant for some ports. */
 8002f08:	f1c3 0038 	rsb	r0, r3, #56	@ 0x38
 8002f0c:	61a0      	str	r0, [r4, #24]
					prvAddTaskToReadyList( pxTCB );
 8002f0e:	6810      	ldr	r0, [r2, #0]
 8002f10:	4283      	cmp	r3, r0
 8002f12:	4808      	ldr	r0, [pc, #32]	@ (8002f34 <xTaskPriorityDisinherit+0x84>)
 8002f14:	bf88      	it	hi
 8002f16:	6013      	strhi	r3, [r2, #0]
 8002f18:	eb03 0383 	add.w	r3, r3, r3, lsl #2
 8002f1c:	eb00 0083 	add.w	r0, r0, r3, lsl #2
 8002f20:	4629      	mov	r1, r5
 8002f22:	f7fe ff89 	bl	8001e38 <vListInsertEnd>
					xReturn = pdTRUE;
 8002f26:	2001      	movs	r0, #1
	}
 8002f28:	bd38      	pop	{r3, r4, r5, pc}
 8002f2a:	bf00      	nop
 8002f2c:	20000d20 	.word	0x20000d20
 8002f30:	20000844 	.word	0x20000844
 8002f34:	200008c0 	.word	0x200008c0

08002f38 <prvCheckForValidListAndQueue>:
	pxOverflowTimerList = pxTemp;
}
/*-----------------------------------------------------------*/

static void prvCheckForValidListAndQueue( void )
{
 8002f38:	b5f0      	push	{r4, r5, r6, r7, lr}
	/* Check that the list from which active timers are referenced, and the
	queue used to communicate with the timer service, have been
	initialised. */
	taskENTER_CRITICAL();
	{
		if( xTimerQueue == NULL )
 8002f3a:	4c12      	ldr	r4, [pc, #72]	@ (8002f84 <prvCheckForValidListAndQueue+0x4c>)
{
 8002f3c:	b083      	sub	sp, #12
	taskENTER_CRITICAL();
 8002f3e:	f000 fa69 	bl	8003414 <vPortEnterCritical>
		if( xTimerQueue == NULL )
 8002f42:	6825      	ldr	r5, [r4, #0]
 8002f44:	b125      	cbz	r5, 8002f50 <prvCheckForValidListAndQueue+0x18>
		{
			mtCOVERAGE_TEST_MARKER();
		}
	}
	taskEXIT_CRITICAL();
}
 8002f46:	b003      	add	sp, #12
 8002f48:	e8bd 40f0 	ldmia.w	sp!, {r4, r5, r6, r7, lr}
	taskEXIT_CRITICAL();
 8002f4c:	f000 ba84 	b.w	8003458 <vPortExitCritical>
			vListInitialise( &xActiveTimerList1 );
 8002f50:	4f0d      	ldr	r7, [pc, #52]	@ (8002f88 <prvCheckForValidListAndQueue+0x50>)
			vListInitialise( &xActiveTimerList2 );
 8002f52:	4e0e      	ldr	r6, [pc, #56]	@ (8002f8c <prvCheckForValidListAndQueue+0x54>)
			vListInitialise( &xActiveTimerList1 );
 8002f54:	4638      	mov	r0, r7
 8002f56:	f7fe ff5f 	bl	8001e18 <vListInitialise>
			vListInitialise( &xActiveTimerList2 );
 8002f5a:	4630      	mov	r0, r6
 8002f5c:	f7fe ff5c 	bl	8001e18 <vListInitialise>
			pxCurrentTimerList = &xActiveTimerList1;
 8002f60:	4a0b      	ldr	r2, [pc, #44]	@ (8002f90 <prvCheckForValidListAndQueue+0x58>)
				xTimerQueue = xQueueCreateStatic( ( UBaseType_t ) configTIMER_QUEUE_LENGTH, ( UBaseType_t ) sizeof( DaemonTaskMessage_t ), &( ucStaticTimerQueueStorage[ 0 ] ), &xStaticTimerQueue );
 8002f62:	9500      	str	r5, [sp, #0]
			pxCurrentTimerList = &xActiveTimerList1;
 8002f64:	6017      	str	r7, [r2, #0]
			pxOverflowTimerList = &xActiveTimerList2;
 8002f66:	4a0b      	ldr	r2, [pc, #44]	@ (8002f94 <prvCheckForValidListAndQueue+0x5c>)
				xTimerQueue = xQueueCreateStatic( ( UBaseType_t ) configTIMER_QUEUE_LENGTH, ( UBaseType_t ) sizeof( DaemonTaskMessage_t ), &( ucStaticTimerQueueStorage[ 0 ] ), &xStaticTimerQueue );
 8002f68:	4b0b      	ldr	r3, [pc, #44]	@ (8002f98 <prvCheckForValidListAndQueue+0x60>)
			pxOverflowTimerList = &xActiveTimerList2;
 8002f6a:	6016      	str	r6, [r2, #0]
				xTimerQueue = xQueueCreateStatic( ( UBaseType_t ) configTIMER_QUEUE_LENGTH, ( UBaseType_t ) sizeof( DaemonTaskMessage_t ), &( ucStaticTimerQueueStorage[ 0 ] ), &xStaticTimerQueue );
 8002f6c:	2110      	movs	r1, #16
 8002f6e:	4a0b      	ldr	r2, [pc, #44]	@ (8002f9c <prvCheckForValidListAndQueue+0x64>)
 8002f70:	200a      	movs	r0, #10
 8002f72:	f7ff f863 	bl	800203c <xQueueGenericCreateStatic>
 8002f76:	6020      	str	r0, [r4, #0]
				if( xTimerQueue != NULL )
 8002f78:	2800      	cmp	r0, #0
 8002f7a:	d0e4      	beq.n	8002f46 <prvCheckForValidListAndQueue+0xe>
					vQueueAddToRegistry( xTimerQueue, "TmrQ" );
 8002f7c:	4908      	ldr	r1, [pc, #32]	@ (8002fa0 <prvCheckForValidListAndQueue+0x68>)
 8002f7e:	f7ff fa8f 	bl	80024a0 <vQueueAddToRegistry>
 8002f82:	e7e0      	b.n	8002f46 <prvCheckForValidListAndQueue+0xe>
 8002f84:	20000e1c 	.word	0x20000e1c
 8002f88:	20000e3c 	.word	0x20000e3c
 8002f8c:	20000e28 	.word	0x20000e28
 8002f90:	20000e24 	.word	0x20000e24
 8002f94:	20000e20 	.word	0x20000e20
 8002f98:	20000d24 	.word	0x20000d24
 8002f9c:	20000d74 	.word	0x20000d74
 8002fa0:	08003950 	.word	0x08003950

08002fa4 <xTimerCreateTimerTask>:
{
 8002fa4:	b510      	push	{r4, lr}
 8002fa6:	b088      	sub	sp, #32
	prvCheckForValidListAndQueue();
 8002fa8:	f7ff ffc6 	bl	8002f38 <prvCheckForValidListAndQueue>
	if( xTimerQueue != NULL )
 8002fac:	4b12      	ldr	r3, [pc, #72]	@ (8002ff8 <xTimerCreateTimerTask+0x54>)
 8002fae:	681b      	ldr	r3, [r3, #0]
 8002fb0:	b1cb      	cbz	r3, 8002fe6 <xTimerCreateTimerTask+0x42>
			StaticTask_t *pxTimerTaskTCBBuffer = NULL;
 8002fb2:	2400      	movs	r4, #0
			vApplicationGetTimerTaskMemory( &pxTimerTaskTCBBuffer, &pxTimerTaskStackBuffer, &ulTimerTaskStackSize );
 8002fb4:	aa07      	add	r2, sp, #28
 8002fb6:	a906      	add	r1, sp, #24
 8002fb8:	a805      	add	r0, sp, #20
			StackType_t *pxTimerTaskStackBuffer = NULL;
 8002fba:	e9cd 4405 	strd	r4, r4, [sp, #20]
			vApplicationGetTimerTaskMemory( &pxTimerTaskTCBBuffer, &pxTimerTaskStackBuffer, &ulTimerTaskStackSize );
 8002fbe:	f7fe ff1b 	bl	8001df8 <vApplicationGetTimerTaskMemory>
			xTimerTaskHandle = xTaskCreateStatic(	prvTimerTask,
 8002fc2:	e9dd 0205 	ldrd	r0, r2, [sp, #20]
 8002fc6:	2302      	movs	r3, #2
 8002fc8:	e9cd 2001 	strd	r2, r0, [sp, #4]
 8002fcc:	9300      	str	r3, [sp, #0]
 8002fce:	490b      	ldr	r1, [pc, #44]	@ (8002ffc <xTimerCreateTimerTask+0x58>)
 8002fd0:	9a07      	ldr	r2, [sp, #28]
 8002fd2:	480b      	ldr	r0, [pc, #44]	@ (8003000 <xTimerCreateTimerTask+0x5c>)
 8002fd4:	4623      	mov	r3, r4
 8002fd6:	f7ff fc0d 	bl	80027f4 <xTaskCreateStatic>
 8002fda:	4b0a      	ldr	r3, [pc, #40]	@ (8003004 <xTimerCreateTimerTask+0x60>)
 8002fdc:	6018      	str	r0, [r3, #0]
			if( xTimerTaskHandle != NULL )
 8002fde:	b110      	cbz	r0, 8002fe6 <xTimerCreateTimerTask+0x42>
}
 8002fe0:	2001      	movs	r0, #1
 8002fe2:	b008      	add	sp, #32
 8002fe4:	bd10      	pop	{r4, pc}
 8002fe6:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8002fea:	f383 8811 	msr	BASEPRI, r3
 8002fee:	f3bf 8f6f 	isb	sy
 8002ff2:	f3bf 8f4f 	dsb	sy
	configASSERT( xReturn );
 8002ff6:	e7fe      	b.n	8002ff6 <xTimerCreateTimerTask+0x52>
 8002ff8:	20000e1c 	.word	0x20000e1c
 8002ffc:	08003958 	.word	0x08003958
 8003000:	080030f5 	.word	0x080030f5
 8003004:	20000e18 	.word	0x20000e18

08003008 <xTimerGenericCommand>:
	configASSERT( xTimer );
 8003008:	b1c0      	cbz	r0, 800303c <xTimerGenericCommand+0x34>
{
 800300a:	b530      	push	{r4, r5, lr}
	if( xTimerQueue != NULL )
 800300c:	4d18      	ldr	r5, [pc, #96]	@ (8003070 <xTimerGenericCommand+0x68>)
 800300e:	682c      	ldr	r4, [r5, #0]
{
 8003010:	b085      	sub	sp, #20
	if( xTimerQueue != NULL )
 8003012:	b184      	cbz	r4, 8003036 <xTimerGenericCommand+0x2e>
		if( xCommandID < tmrFIRST_FROM_ISR_COMMAND )
 8003014:	2905      	cmp	r1, #5
		xMessage.xMessageID = xCommandID;
 8003016:	e9cd 1200 	strd	r1, r2, [sp]
		xMessage.u.xTimerParameters.pxTimer = xTimer;
 800301a:	9002      	str	r0, [sp, #8]
		if( xCommandID < tmrFIRST_FROM_ISR_COMMAND )
 800301c:	dc17      	bgt.n	800304e <xTimerGenericCommand+0x46>
			if( xTaskGetSchedulerState() == taskSCHEDULER_RUNNING )
 800301e:	f7ff ff37 	bl	8002e90 <xTaskGetSchedulerState>
 8003022:	2802      	cmp	r0, #2
 8003024:	d01c      	beq.n	8003060 <xTimerGenericCommand+0x58>
				xReturn = xQueueSendToBack( xTimerQueue, &xMessage, tmrNO_DELAY );
 8003026:	2300      	movs	r3, #0
 8003028:	6828      	ldr	r0, [r5, #0]
 800302a:	461a      	mov	r2, r3
 800302c:	4669      	mov	r1, sp
 800302e:	f7ff f859 	bl	80020e4 <xQueueGenericSend>
}
 8003032:	b005      	add	sp, #20
 8003034:	bd30      	pop	{r4, r5, pc}
BaseType_t xReturn = pdFAIL;
 8003036:	4620      	mov	r0, r4
}
 8003038:	b005      	add	sp, #20
 800303a:	bd30      	pop	{r4, r5, pc}
 800303c:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8003040:	f383 8811 	msr	BASEPRI, r3
 8003044:	f3bf 8f6f 	isb	sy
 8003048:	f3bf 8f4f 	dsb	sy
	configASSERT( xTimer );
 800304c:	e7fe      	b.n	800304c <xTimerGenericCommand+0x44>
			xReturn = xQueueSendToBackFromISR( xTimerQueue, &xMessage, pxHigherPriorityTaskWoken );
 800304e:	469c      	mov	ip, r3
 8003050:	4662      	mov	r2, ip
 8003052:	2300      	movs	r3, #0
 8003054:	4669      	mov	r1, sp
 8003056:	4620      	mov	r0, r4
 8003058:	f7ff f906 	bl	8002268 <xQueueGenericSendFromISR>
}
 800305c:	b005      	add	sp, #20
 800305e:	bd30      	pop	{r4, r5, pc}
				xReturn = xQueueSendToBack( xTimerQueue, &xMessage, xTicksToWait );
 8003060:	6828      	ldr	r0, [r5, #0]
 8003062:	9a08      	ldr	r2, [sp, #32]
 8003064:	2300      	movs	r3, #0
 8003066:	4669      	mov	r1, sp
 8003068:	f7ff f83c 	bl	80020e4 <xQueueGenericSend>
 800306c:	e7e4      	b.n	8003038 <xTimerGenericCommand+0x30>
 800306e:	bf00      	nop
 8003070:	20000e1c 	.word	0x20000e1c

08003074 <prvSwitchTimerLists>:
{
 8003074:	b5f0      	push	{r4, r5, r6, r7, lr}
 8003076:	4e1d      	ldr	r6, [pc, #116]	@ (80030ec <prvSwitchTimerLists+0x78>)
 8003078:	b083      	sub	sp, #12
	while( listLIST_IS_EMPTY( pxCurrentTimerList ) == pdFALSE )
 800307a:	e00d      	b.n	8003098 <prvSwitchTimerLists+0x24>
		xNextExpireTime = listGET_ITEM_VALUE_OF_HEAD_ENTRY( pxCurrentTimerList );
 800307c:	68db      	ldr	r3, [r3, #12]
		pxTimer = ( Timer_t * ) listGET_OWNER_OF_HEAD_ENTRY( pxCurrentTimerList ); /*lint !e9087 !e9079 void * is used as this macro is used with tasks and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 800307e:	68dc      	ldr	r4, [r3, #12]
		xNextExpireTime = listGET_ITEM_VALUE_OF_HEAD_ENTRY( pxCurrentTimerList );
 8003080:	681f      	ldr	r7, [r3, #0]
		( void ) uxListRemove( &( pxTimer->xTimerListItem ) );
 8003082:	1d25      	adds	r5, r4, #4
 8003084:	4628      	mov	r0, r5
 8003086:	f7fe fefd 	bl	8001e84 <uxListRemove>
		pxTimer->pxCallbackFunction( ( TimerHandle_t ) pxTimer );
 800308a:	6a23      	ldr	r3, [r4, #32]
 800308c:	4620      	mov	r0, r4
 800308e:	4798      	blx	r3
		if( ( pxTimer->ucStatus & tmrSTATUS_IS_AUTORELOAD ) != 0 )
 8003090:	f894 3028 	ldrb.w	r3, [r4, #40]	@ 0x28
 8003094:	075b      	lsls	r3, r3, #29
 8003096:	d409      	bmi.n	80030ac <prvSwitchTimerLists+0x38>
	while( listLIST_IS_EMPTY( pxCurrentTimerList ) == pdFALSE )
 8003098:	6833      	ldr	r3, [r6, #0]
 800309a:	681a      	ldr	r2, [r3, #0]
 800309c:	2a00      	cmp	r2, #0
 800309e:	d1ed      	bne.n	800307c <prvSwitchTimerLists+0x8>
	pxCurrentTimerList = pxOverflowTimerList;
 80030a0:	4a13      	ldr	r2, [pc, #76]	@ (80030f0 <prvSwitchTimerLists+0x7c>)
 80030a2:	6811      	ldr	r1, [r2, #0]
 80030a4:	6031      	str	r1, [r6, #0]
	pxOverflowTimerList = pxTemp;
 80030a6:	6013      	str	r3, [r2, #0]
}
 80030a8:	b003      	add	sp, #12
 80030aa:	bdf0      	pop	{r4, r5, r6, r7, pc}
			xReloadTime = ( xNextExpireTime + pxTimer->xTimerPeriodInTicks );
 80030ac:	69a2      	ldr	r2, [r4, #24]
 80030ae:	eb07 0c02 	add.w	ip, r7, r2
			if( xReloadTime > xNextExpireTime )
 80030b2:	4567      	cmp	r7, ip
				xResult = xTimerGenericCommand( pxTimer, tmrCOMMAND_START_DONT_TRACE, xNextExpireTime, NULL, tmrNO_DELAY );
 80030b4:	f04f 0300 	mov.w	r3, #0
 80030b8:	4620      	mov	r0, r4
 80030ba:	463a      	mov	r2, r7
				vListInsert( pxCurrentTimerList, &( pxTimer->xTimerListItem ) );
 80030bc:	4629      	mov	r1, r5
			if( xReloadTime > xNextExpireTime )
 80030be:	d206      	bcs.n	80030ce <prvSwitchTimerLists+0x5a>
				vListInsert( pxCurrentTimerList, &( pxTimer->xTimerListItem ) );
 80030c0:	6830      	ldr	r0, [r6, #0]
				listSET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ), xReloadTime );
 80030c2:	f8c4 c004 	str.w	ip, [r4, #4]
				listSET_LIST_ITEM_OWNER( &( pxTimer->xTimerListItem ), pxTimer );
 80030c6:	6124      	str	r4, [r4, #16]
				vListInsert( pxCurrentTimerList, &( pxTimer->xTimerListItem ) );
 80030c8:	f7fe fec4 	bl	8001e54 <vListInsert>
 80030cc:	e7e4      	b.n	8003098 <prvSwitchTimerLists+0x24>
				xResult = xTimerGenericCommand( pxTimer, tmrCOMMAND_START_DONT_TRACE, xNextExpireTime, NULL, tmrNO_DELAY );
 80030ce:	9300      	str	r3, [sp, #0]
 80030d0:	4619      	mov	r1, r3
 80030d2:	f7ff ff99 	bl	8003008 <xTimerGenericCommand>
				configASSERT( xResult );
 80030d6:	2800      	cmp	r0, #0
 80030d8:	d1de      	bne.n	8003098 <prvSwitchTimerLists+0x24>
 80030da:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80030de:	f383 8811 	msr	BASEPRI, r3
 80030e2:	f3bf 8f6f 	isb	sy
 80030e6:	f3bf 8f4f 	dsb	sy
 80030ea:	e7fe      	b.n	80030ea <prvSwitchTimerLists+0x76>
 80030ec:	20000e24 	.word	0x20000e24
 80030f0:	20000e20 	.word	0x20000e20

080030f4 <prvTimerTask>:
{
 80030f4:	e92d 4ff0 	stmdb	sp!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
 80030f8:	4e8d      	ldr	r6, [pc, #564]	@ (8003330 <prvTimerTask+0x23c>)
 80030fa:	4d8e      	ldr	r5, [pc, #568]	@ (8003334 <prvTimerTask+0x240>)
 80030fc:	4c8e      	ldr	r4, [pc, #568]	@ (8003338 <prvTimerTask+0x244>)
 80030fe:	b089      	sub	sp, #36	@ 0x24
					portYIELD_WITHIN_API();
 8003100:	f04f 29e0 	mov.w	r9, #3758153728	@ 0xe000e000
 8003104:	f04f 5880 	mov.w	r8, #268435456	@ 0x10000000
	*pxListWasEmpty = listLIST_IS_EMPTY( pxCurrentTimerList );
 8003108:	6832      	ldr	r2, [r6, #0]
 800310a:	6817      	ldr	r7, [r2, #0]
 800310c:	2f00      	cmp	r7, #0
 800310e:	f040 809d 	bne.w	800324c <prvTimerTask+0x158>
	vTaskSuspendAll();
 8003112:	f7ff fc1f 	bl	8002954 <vTaskSuspendAll>
	xTimeNow = xTaskGetTickCount();
 8003116:	f7ff fc25 	bl	8002964 <xTaskGetTickCount>
	if( xTimeNow < xLastTime )
 800311a:	682a      	ldr	r2, [r5, #0]
 800311c:	4290      	cmp	r0, r2
	xTimeNow = xTaskGetTickCount();
 800311e:	4683      	mov	fp, r0
	if( xTimeNow < xLastTime )
 8003120:	f0c0 80a3 	bcc.w	800326a <prvTimerTask+0x176>
					xListWasEmpty = listLIST_IS_EMPTY( pxOverflowTimerList );
 8003124:	4b85      	ldr	r3, [pc, #532]	@ (800333c <prvTimerTask+0x248>)
	xLastTime = xTimeNow;
 8003126:	6028      	str	r0, [r5, #0]
					xListWasEmpty = listLIST_IS_EMPTY( pxOverflowTimerList );
 8003128:	681a      	ldr	r2, [r3, #0]
 800312a:	6812      	ldr	r2, [r2, #0]
 800312c:	fab2 f282 	clz	r2, r2
 8003130:	0952      	lsrs	r2, r2, #5
				vQueueWaitForMessageRestricted( xTimerQueue, ( xNextExpireTime - xTimeNow ), xListWasEmpty );
 8003132:	6820      	ldr	r0, [r4, #0]
 8003134:	eba7 010b 	sub.w	r1, r7, fp
 8003138:	f7ff f9ca 	bl	80024d0 <vQueueWaitForMessageRestricted>
				if( xTaskResumeAll() == pdFALSE )
 800313c:	f7ff fd52 	bl	8002be4 <xTaskResumeAll>
 8003140:	b928      	cbnz	r0, 800314e <prvTimerTask+0x5a>
					portYIELD_WITHIN_API();
 8003142:	f8c9 8d04 	str.w	r8, [r9, #3332]	@ 0xd04
 8003146:	f3bf 8f4f 	dsb	sy
 800314a:	f3bf 8f6f 	isb	sy
	while( xQueueReceive( xTimerQueue, &xMessage, tmrNO_DELAY ) != pdFAIL ) /*lint !e603 xMessage does not have to be initialised as it is passed out, not in, and it is not used unless xQueueReceive() returns pdTRUE. */
 800314e:	6820      	ldr	r0, [r4, #0]
 8003150:	2200      	movs	r2, #0
 8003152:	a904      	add	r1, sp, #16
 8003154:	f7ff f8ea 	bl	800232c <xQueueReceive>
 8003158:	2800      	cmp	r0, #0
 800315a:	d0d5      	beq.n	8003108 <prvTimerTask+0x14>
			if( xMessage.xMessageID < ( BaseType_t ) 0 )
 800315c:	9b04      	ldr	r3, [sp, #16]
				pxCallback->pxCallbackFunction( pxCallback->pvParameter1, pxCallback->ulParameter2 );
 800315e:	9806      	ldr	r0, [sp, #24]
			if( xMessage.xMessageID < ( BaseType_t ) 0 )
 8003160:	2b00      	cmp	r3, #0
 8003162:	db6c      	blt.n	800323e <prvTimerTask+0x14a>
			pxTimer = xMessage.u.xTimerParameters.pxTimer;
 8003164:	9f06      	ldr	r7, [sp, #24]
			if( listIS_CONTAINED_WITHIN( NULL, &( pxTimer->xTimerListItem ) ) == pdFALSE ) /*lint !e961. The cast is only redundant when NULL is passed into the macro. */
 8003166:	697b      	ldr	r3, [r7, #20]
 8003168:	b113      	cbz	r3, 8003170 <prvTimerTask+0x7c>
				( void ) uxListRemove( &( pxTimer->xTimerListItem ) );
 800316a:	1d38      	adds	r0, r7, #4
 800316c:	f7fe fe8a 	bl	8001e84 <uxListRemove>
	xTimeNow = xTaskGetTickCount();
 8003170:	f7ff fbf8 	bl	8002964 <xTaskGetTickCount>
	if( xTimeNow < xLastTime )
 8003174:	682b      	ldr	r3, [r5, #0]
 8003176:	4298      	cmp	r0, r3
	xTimeNow = xTaskGetTickCount();
 8003178:	4683      	mov	fp, r0
	if( xTimeNow < xLastTime )
 800317a:	d37d      	bcc.n	8003278 <prvTimerTask+0x184>
			switch( xMessage.xMessageID )
 800317c:	9b04      	ldr	r3, [sp, #16]
	xLastTime = xTimeNow;
 800317e:	f8c5 b000 	str.w	fp, [r5]
			switch( xMessage.xMessageID )
 8003182:	2b09      	cmp	r3, #9
 8003184:	d8e3      	bhi.n	800314e <prvTimerTask+0x5a>
 8003186:	e8df f003 	tbb	[pc, r3]
 800318a:	0505      	.short	0x0505
 800318c:	51344a05 	.word	0x51344a05
 8003190:	344a0505 	.word	0x344a0505
					pxTimer->ucStatus |= tmrSTATUS_IS_ACTIVE;
 8003194:	f897 2028 	ldrb.w	r2, [r7, #40]	@ 0x28
					if( prvInsertTimerInActiveList( pxTimer,  xMessage.u.xTimerParameters.xMessageValue + pxTimer->xTimerPeriodInTicks, xTimeNow, xMessage.u.xTimerParameters.xMessageValue ) != pdFALSE )
 8003198:	9b05      	ldr	r3, [sp, #20]
 800319a:	69b9      	ldr	r1, [r7, #24]
	listSET_LIST_ITEM_OWNER( &( pxTimer->xTimerListItem ), pxTimer );
 800319c:	613f      	str	r7, [r7, #16]
					pxTimer->ucStatus |= tmrSTATUS_IS_ACTIVE;
 800319e:	f042 0201 	orr.w	r2, r2, #1
 80031a2:	f887 2028 	strb.w	r2, [r7, #40]	@ 0x28
					if( prvInsertTimerInActiveList( pxTimer,  xMessage.u.xTimerParameters.xMessageValue + pxTimer->xTimerPeriodInTicks, xTimeNow, xMessage.u.xTimerParameters.xMessageValue ) != pdFALSE )
 80031a6:	185a      	adds	r2, r3, r1
 80031a8:	bf2c      	ite	cs
 80031aa:	2001      	movcs	r0, #1
 80031ac:	2000      	movcc	r0, #0
	if( xNextExpiryTime <= xTimeNow )
 80031ae:	455a      	cmp	r2, fp
	listSET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ), xNextExpiryTime );
 80031b0:	607a      	str	r2, [r7, #4]
	if( xNextExpiryTime <= xTimeNow )
 80031b2:	d864      	bhi.n	800327e <prvTimerTask+0x18a>
		if( ( ( TickType_t ) ( xTimeNow - xCommandTime ) ) >= pxTimer->xTimerPeriodInTicks ) /*lint !e961 MISRA exception as the casts are only redundant for some ports. */
 80031b4:	ebab 0303 	sub.w	r3, fp, r3
 80031b8:	4299      	cmp	r1, r3
 80031ba:	d82a      	bhi.n	8003212 <prvTimerTask+0x11e>
						pxTimer->pxCallbackFunction( ( TimerHandle_t ) pxTimer );
 80031bc:	6a3b      	ldr	r3, [r7, #32]
 80031be:	4638      	mov	r0, r7
 80031c0:	4798      	blx	r3
						if( ( pxTimer->ucStatus & tmrSTATUS_IS_AUTORELOAD ) != 0 )
 80031c2:	f897 3028 	ldrb.w	r3, [r7, #40]	@ 0x28
 80031c6:	0759      	lsls	r1, r3, #29
 80031c8:	d5c1      	bpl.n	800314e <prvTimerTask+0x5a>
							xResult = xTimerGenericCommand( pxTimer, tmrCOMMAND_START_DONT_TRACE, xMessage.u.xTimerParameters.xMessageValue + pxTimer->xTimerPeriodInTicks, NULL, tmrNO_DELAY );
 80031ca:	69bb      	ldr	r3, [r7, #24]
 80031cc:	9a05      	ldr	r2, [sp, #20]
 80031ce:	441a      	add	r2, r3
 80031d0:	2300      	movs	r3, #0
 80031d2:	9300      	str	r3, [sp, #0]
 80031d4:	4638      	mov	r0, r7
 80031d6:	4619      	mov	r1, r3
 80031d8:	f7ff ff16 	bl	8003008 <xTimerGenericCommand>
							configASSERT( xResult );
 80031dc:	2800      	cmp	r0, #0
 80031de:	d1b6      	bne.n	800314e <prvTimerTask+0x5a>
 80031e0:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80031e4:	f383 8811 	msr	BASEPRI, r3
 80031e8:	f3bf 8f6f 	isb	sy
 80031ec:	f3bf 8f4f 	dsb	sy
 80031f0:	e7fe      	b.n	80031f0 <prvTimerTask+0xfc>
					pxTimer->ucStatus |= tmrSTATUS_IS_ACTIVE;
 80031f2:	f897 2028 	ldrb.w	r2, [r7, #40]	@ 0x28
					pxTimer->xTimerPeriodInTicks = xMessage.u.xTimerParameters.xMessageValue;
 80031f6:	9b05      	ldr	r3, [sp, #20]
 80031f8:	61bb      	str	r3, [r7, #24]
					pxTimer->ucStatus |= tmrSTATUS_IS_ACTIVE;
 80031fa:	f042 0201 	orr.w	r2, r2, #1
 80031fe:	f887 2028 	strb.w	r2, [r7, #40]	@ 0x28
					configASSERT( ( pxTimer->xTimerPeriodInTicks > 0 ) );
 8003202:	2b00      	cmp	r3, #0
 8003204:	f000 8086 	beq.w	8003314 <prvTimerTask+0x220>
					( void ) prvInsertTimerInActiveList( pxTimer, ( xTimeNow + pxTimer->xTimerPeriodInTicks ), xTimeNow, xTimeNow );
 8003208:	445b      	add	r3, fp
	if( xNextExpiryTime <= xTimeNow )
 800320a:	455b      	cmp	r3, fp
	listSET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ), xNextExpiryTime );
 800320c:	607b      	str	r3, [r7, #4]
	listSET_LIST_ITEM_OWNER( &( pxTimer->xTimerListItem ), pxTimer );
 800320e:	613f      	str	r7, [r7, #16]
	if( xNextExpiryTime <= xTimeNow )
 8003210:	d839      	bhi.n	8003286 <prvTimerTask+0x192>
			vListInsert( pxOverflowTimerList, &( pxTimer->xTimerListItem ) );
 8003212:	4b4a      	ldr	r3, [pc, #296]	@ (800333c <prvTimerTask+0x248>)
 8003214:	1d39      	adds	r1, r7, #4
 8003216:	6818      	ldr	r0, [r3, #0]
 8003218:	f7fe fe1c 	bl	8001e54 <vListInsert>
 800321c:	e797      	b.n	800314e <prvTimerTask+0x5a>
					pxTimer->ucStatus &= ~tmrSTATUS_IS_ACTIVE;
 800321e:	f897 3028 	ldrb.w	r3, [r7, #40]	@ 0x28
 8003222:	f023 0301 	bic.w	r3, r3, #1
 8003226:	f887 3028 	strb.w	r3, [r7, #40]	@ 0x28
					break;
 800322a:	e790      	b.n	800314e <prvTimerTask+0x5a>
						if( ( pxTimer->ucStatus & tmrSTATUS_IS_STATICALLY_ALLOCATED ) == ( uint8_t ) 0 )
 800322c:	f897 3028 	ldrb.w	r3, [r7, #40]	@ 0x28
 8003230:	079a      	lsls	r2, r3, #30
 8003232:	d548      	bpl.n	80032c6 <prvTimerTask+0x1d2>
							pxTimer->ucStatus &= ~tmrSTATUS_IS_ACTIVE;
 8003234:	f023 0301 	bic.w	r3, r3, #1
 8003238:	f887 3028 	strb.w	r3, [r7, #40]	@ 0x28
 800323c:	e787      	b.n	800314e <prvTimerTask+0x5a>
				pxCallback->pxCallbackFunction( pxCallback->pvParameter1, pxCallback->ulParameter2 );
 800323e:	9b05      	ldr	r3, [sp, #20]
 8003240:	9907      	ldr	r1, [sp, #28]
 8003242:	4798      	blx	r3
		if( xMessage.xMessageID >= ( BaseType_t ) 0 )
 8003244:	9b04      	ldr	r3, [sp, #16]
 8003246:	2b00      	cmp	r3, #0
 8003248:	db81      	blt.n	800314e <prvTimerTask+0x5a>
 800324a:	e78b      	b.n	8003164 <prvTimerTask+0x70>
		xNextExpireTime = listGET_ITEM_VALUE_OF_HEAD_ENTRY( pxCurrentTimerList );
 800324c:	68d3      	ldr	r3, [r2, #12]
 800324e:	681f      	ldr	r7, [r3, #0]
	vTaskSuspendAll();
 8003250:	f7ff fb80 	bl	8002954 <vTaskSuspendAll>
	xTimeNow = xTaskGetTickCount();
 8003254:	f7ff fb86 	bl	8002964 <xTaskGetTickCount>
	if( xTimeNow < xLastTime )
 8003258:	682a      	ldr	r2, [r5, #0]
 800325a:	4290      	cmp	r0, r2
	xTimeNow = xTaskGetTickCount();
 800325c:	4683      	mov	fp, r0
	if( xTimeNow < xLastTime )
 800325e:	d304      	bcc.n	800326a <prvTimerTask+0x176>
			if( ( xListWasEmpty == pdFALSE ) && ( xNextExpireTime <= xTimeNow ) )
 8003260:	4287      	cmp	r7, r0
	xLastTime = xTimeNow;
 8003262:	6028      	str	r0, [r5, #0]
			if( ( xListWasEmpty == pdFALSE ) && ( xNextExpireTime <= xTimeNow ) )
 8003264:	d914      	bls.n	8003290 <prvTimerTask+0x19c>
 8003266:	2200      	movs	r2, #0
 8003268:	e763      	b.n	8003132 <prvTimerTask+0x3e>
		prvSwitchTimerLists();
 800326a:	f7ff ff03 	bl	8003074 <prvSwitchTimerLists>
	xLastTime = xTimeNow;
 800326e:	f8c5 b000 	str.w	fp, [r5]
			( void ) xTaskResumeAll();
 8003272:	f7ff fcb7 	bl	8002be4 <xTaskResumeAll>
 8003276:	e76a      	b.n	800314e <prvTimerTask+0x5a>
		prvSwitchTimerLists();
 8003278:	f7ff fefc 	bl	8003074 <prvSwitchTimerLists>
		*pxTimerListsWereSwitched = pdTRUE;
 800327c:	e77e      	b.n	800317c <prvTimerTask+0x88>
		if( ( xTimeNow < xCommandTime ) && ( xNextExpiryTime >= xCommandTime ) )
 800327e:	455b      	cmp	r3, fp
 8003280:	d901      	bls.n	8003286 <prvTimerTask+0x192>
 8003282:	2800      	cmp	r0, #0
 8003284:	d09a      	beq.n	80031bc <prvTimerTask+0xc8>
			vListInsert( pxCurrentTimerList, &( pxTimer->xTimerListItem ) );
 8003286:	6830      	ldr	r0, [r6, #0]
 8003288:	1d39      	adds	r1, r7, #4
 800328a:	f7fe fde3 	bl	8001e54 <vListInsert>
 800328e:	e75e      	b.n	800314e <prvTimerTask+0x5a>
				( void ) xTaskResumeAll();
 8003290:	f7ff fca8 	bl	8002be4 <xTaskResumeAll>
Timer_t * const pxTimer = ( Timer_t * ) listGET_OWNER_OF_HEAD_ENTRY( pxCurrentTimerList ); /*lint !e9087 !e9079 void * is used as this macro is used with tasks and co-routines too.  Alignment is known to be fine as the type of the pointer stored and retrieved is the same. */
 8003294:	6832      	ldr	r2, [r6, #0]
 8003296:	68d2      	ldr	r2, [r2, #12]
 8003298:	f8d2 a00c 	ldr.w	sl, [r2, #12]
	( void ) uxListRemove( &( pxTimer->xTimerListItem ) );
 800329c:	f10a 0104 	add.w	r1, sl, #4
 80032a0:	4608      	mov	r0, r1
 80032a2:	9103      	str	r1, [sp, #12]
 80032a4:	f7fe fdee 	bl	8001e84 <uxListRemove>
	if( ( pxTimer->ucStatus & tmrSTATUS_IS_AUTORELOAD ) != 0 )
 80032a8:	f89a 2028 	ldrb.w	r2, [sl, #40]	@ 0x28
 80032ac:	9903      	ldr	r1, [sp, #12]
 80032ae:	f012 0f04 	tst.w	r2, #4
 80032b2:	d10c      	bne.n	80032ce <prvTimerTask+0x1da>
		pxTimer->ucStatus &= ~tmrSTATUS_IS_ACTIVE;
 80032b4:	f022 0201 	bic.w	r2, r2, #1
 80032b8:	f88a 2028 	strb.w	r2, [sl, #40]	@ 0x28
	pxTimer->pxCallbackFunction( ( TimerHandle_t ) pxTimer );
 80032bc:	f8da 3020 	ldr.w	r3, [sl, #32]
 80032c0:	4650      	mov	r0, sl
 80032c2:	4798      	blx	r3
}
 80032c4:	e743      	b.n	800314e <prvTimerTask+0x5a>
							vPortFree( pxTimer );
 80032c6:	4638      	mov	r0, r7
 80032c8:	f000 fab2 	bl	8003830 <vPortFree>
 80032cc:	e73f      	b.n	800314e <prvTimerTask+0x5a>
		if( prvInsertTimerInActiveList( pxTimer, ( xNextExpireTime + pxTimer->xTimerPeriodInTicks ), xTimeNow, xNextExpireTime ) != pdFALSE )
 80032ce:	f8da 0018 	ldr.w	r0, [sl, #24]
	listSET_LIST_ITEM_OWNER( &( pxTimer->xTimerListItem ), pxTimer );
 80032d2:	f8ca a010 	str.w	sl, [sl, #16]
		if( prvInsertTimerInActiveList( pxTimer, ( xNextExpireTime + pxTimer->xTimerPeriodInTicks ), xTimeNow, xNextExpireTime ) != pdFALSE )
 80032d6:	183a      	adds	r2, r7, r0
	if( xNextExpiryTime <= xTimeNow )
 80032d8:	455a      	cmp	r2, fp
	listSET_LIST_ITEM_VALUE( &( pxTimer->xTimerListItem ), xNextExpiryTime );
 80032da:	f8ca 2004 	str.w	r2, [sl, #4]
	if( xNextExpiryTime <= xTimeNow )
 80032de:	d903      	bls.n	80032e8 <prvTimerTask+0x1f4>
			vListInsert( pxCurrentTimerList, &( pxTimer->xTimerListItem ) );
 80032e0:	6830      	ldr	r0, [r6, #0]
 80032e2:	f7fe fdb7 	bl	8001e54 <vListInsert>
	return xProcessTimerNow;
 80032e6:	e7e9      	b.n	80032bc <prvTimerTask+0x1c8>
		if( ( ( TickType_t ) ( xTimeNow - xCommandTime ) ) >= pxTimer->xTimerPeriodInTicks ) /*lint !e961 MISRA exception as the casts are only redundant for some ports. */
 80032e8:	ebab 0b07 	sub.w	fp, fp, r7
 80032ec:	4558      	cmp	r0, fp
 80032ee:	d81a      	bhi.n	8003326 <prvTimerTask+0x232>
			xResult = xTimerGenericCommand( pxTimer, tmrCOMMAND_START_DONT_TRACE, xNextExpireTime, NULL, tmrNO_DELAY );
 80032f0:	2300      	movs	r3, #0
 80032f2:	463a      	mov	r2, r7
 80032f4:	9300      	str	r3, [sp, #0]
 80032f6:	4619      	mov	r1, r3
 80032f8:	4650      	mov	r0, sl
 80032fa:	f7ff fe85 	bl	8003008 <xTimerGenericCommand>
			configASSERT( xResult );
 80032fe:	2800      	cmp	r0, #0
 8003300:	d1dc      	bne.n	80032bc <prvTimerTask+0x1c8>
 8003302:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8003306:	f383 8811 	msr	BASEPRI, r3
 800330a:	f3bf 8f6f 	isb	sy
 800330e:	f3bf 8f4f 	dsb	sy
 8003312:	e7fe      	b.n	8003312 <prvTimerTask+0x21e>
 8003314:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8003318:	f383 8811 	msr	BASEPRI, r3
 800331c:	f3bf 8f6f 	isb	sy
 8003320:	f3bf 8f4f 	dsb	sy
					configASSERT( ( pxTimer->xTimerPeriodInTicks > 0 ) );
 8003324:	e7fe      	b.n	8003324 <prvTimerTask+0x230>
			vListInsert( pxOverflowTimerList, &( pxTimer->xTimerListItem ) );
 8003326:	4b05      	ldr	r3, [pc, #20]	@ (800333c <prvTimerTask+0x248>)
 8003328:	6818      	ldr	r0, [r3, #0]
 800332a:	f7fe fd93 	bl	8001e54 <vListInsert>
	return xProcessTimerNow;
 800332e:	e7c5      	b.n	80032bc <prvTimerTask+0x1c8>
 8003330:	20000e24 	.word	0x20000e24
 8003334:	20000e14 	.word	0x20000e14
 8003338:	20000e1c 	.word	0x20000e1c
 800333c:	20000e20 	.word	0x20000e20

08003340 <prvPortStartFirstTask>:
{
	/* Start the first task.  This also clears the bit that indicates the FPU is
	in use in case the FPU was used before the scheduler was started - which
	would otherwise result in the unnecessary leaving of space in the SVC stack
	for lazy saving of FPU registers. */
	__asm volatile(
 8003340:	4808      	ldr	r0, [pc, #32]	@ (8003364 <prvPortStartFirstTask+0x24>)
 8003342:	6800      	ldr	r0, [r0, #0]
 8003344:	6800      	ldr	r0, [r0, #0]
 8003346:	f380 8808 	msr	MSP, r0
 800334a:	f04f 0000 	mov.w	r0, #0
 800334e:	f380 8814 	msr	CONTROL, r0
 8003352:	b662      	cpsie	i
 8003354:	b661      	cpsie	f
 8003356:	f3bf 8f4f 	dsb	sy
 800335a:	f3bf 8f6f 	isb	sy
 800335e:	df00      	svc	0
 8003360:	bf00      	nop
					" dsb					\n"
					" isb					\n"
					" svc 0					\n" /* System call to start first task. */
					" nop					\n"
				);
}
 8003362:	0000      	.short	0x0000
 8003364:	e000ed08 	.word	0xe000ed08

08003368 <vPortEnableVFP>:
/*-----------------------------------------------------------*/

/* This is a naked function. */
static void vPortEnableVFP( void )
{
	__asm volatile
 8003368:	f8df 000c 	ldr.w	r0, [pc, #12]	@ 8003378 <vPortEnableVFP+0x10>
 800336c:	6801      	ldr	r1, [r0, #0]
 800336e:	f441 0170 	orr.w	r1, r1, #15728640	@ 0xf00000
 8003372:	6001      	str	r1, [r0, #0]
 8003374:	4770      	bx	lr
		"								\n"
		"	orr r1, r1, #( 0xf << 20 )	\n" /* Enable CP10 and CP11 coprocessors, then save back. */
		"	str r1, [r0]				\n"
		"	bx r14						"
	);
}
 8003376:	0000      	.short	0x0000
 8003378:	e000ed88 	.word	0xe000ed88

0800337c <prvTaskExitError>:
	configASSERT( uxCriticalNesting == ~0UL );
 800337c:	4b0e      	ldr	r3, [pc, #56]	@ (80033b8 <prvTaskExitError+0x3c>)
 800337e:	681b      	ldr	r3, [r3, #0]
{
 8003380:	b082      	sub	sp, #8
volatile uint32_t ulDummy = 0;
 8003382:	2200      	movs	r2, #0
	configASSERT( uxCriticalNesting == ~0UL );
 8003384:	3301      	adds	r3, #1
volatile uint32_t ulDummy = 0;
 8003386:	9201      	str	r2, [sp, #4]
	configASSERT( uxCriticalNesting == ~0UL );
 8003388:	d008      	beq.n	800339c <prvTaskExitError+0x20>
 800338a:	f04f 0350 	mov.w	r3, #80	@ 0x50
 800338e:	f383 8811 	msr	BASEPRI, r3
 8003392:	f3bf 8f6f 	isb	sy
 8003396:	f3bf 8f4f 	dsb	sy
 800339a:	e7fe      	b.n	800339a <prvTaskExitError+0x1e>
 800339c:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80033a0:	f383 8811 	msr	BASEPRI, r3
 80033a4:	f3bf 8f6f 	isb	sy
 80033a8:	f3bf 8f4f 	dsb	sy
	while( ulDummy == 0 )
 80033ac:	9b01      	ldr	r3, [sp, #4]
 80033ae:	2b00      	cmp	r3, #0
 80033b0:	d0fc      	beq.n	80033ac <prvTaskExitError+0x30>
}
 80033b2:	b002      	add	sp, #8
 80033b4:	4770      	bx	lr
 80033b6:	bf00      	nop
 80033b8:	2000000c 	.word	0x2000000c

080033bc <pxPortInitialiseStack>:
{
 80033bc:	b410      	push	{r4}
	*pxTopOfStack = portINITIAL_XPSR;	/* xPSR */
 80033be:	f04f 7380 	mov.w	r3, #16777216	@ 0x1000000
	*pxTopOfStack = ( StackType_t ) portTASK_RETURN_ADDRESS;	/* LR */
 80033c2:	4c07      	ldr	r4, [pc, #28]	@ (80033e0 <pxPortInitialiseStack+0x24>)
	*pxTopOfStack = portINITIAL_XPSR;	/* xPSR */
 80033c4:	f840 3c04 	str.w	r3, [r0, #-4]
	*pxTopOfStack = ( ( StackType_t ) pxCode ) & portSTART_ADDRESS_MASK;	/* PC */
 80033c8:	f021 0101 	bic.w	r1, r1, #1
	*pxTopOfStack = portINITIAL_EXC_RETURN;
 80033cc:	f06f 0302 	mvn.w	r3, #2
	*pxTopOfStack = ( StackType_t ) portTASK_RETURN_ADDRESS;	/* LR */
 80033d0:	e940 4103 	strd	r4, r1, [r0, #-12]
	*pxTopOfStack = portINITIAL_EXC_RETURN;
 80033d4:	e940 3209 	strd	r3, r2, [r0, #-36]	@ 0x24
}
 80033d8:	f85d 4b04 	ldr.w	r4, [sp], #4
 80033dc:	3844      	subs	r0, #68	@ 0x44
 80033de:	4770      	bx	lr
 80033e0:	0800337d 	.word	0x0800337d
	...

080033f0 <SVC_Handler>:
	__asm volatile (
 80033f0:	4b07      	ldr	r3, [pc, #28]	@ (8003410 <pxCurrentTCBConst2>)
 80033f2:	6819      	ldr	r1, [r3, #0]
 80033f4:	6808      	ldr	r0, [r1, #0]
 80033f6:	e8b0 4ff0 	ldmia.w	r0!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
 80033fa:	f380 8809 	msr	PSP, r0
 80033fe:	f3bf 8f6f 	isb	sy
 8003402:	f04f 0000 	mov.w	r0, #0
 8003406:	f380 8811 	msr	BASEPRI, r0
 800340a:	4770      	bx	lr
 800340c:	f3af 8000 	nop.w

08003410 <pxCurrentTCBConst2>:
 8003410:	20000d20 	.word	0x20000d20

08003414 <vPortEnterCritical>:
 8003414:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8003418:	f383 8811 	msr	BASEPRI, r3
 800341c:	f3bf 8f6f 	isb	sy
 8003420:	f3bf 8f4f 	dsb	sy
	uxCriticalNesting++;
 8003424:	4a0b      	ldr	r2, [pc, #44]	@ (8003454 <vPortEnterCritical+0x40>)
 8003426:	6813      	ldr	r3, [r2, #0]
 8003428:	3301      	adds	r3, #1
	if( uxCriticalNesting == 1 )
 800342a:	2b01      	cmp	r3, #1
	uxCriticalNesting++;
 800342c:	6013      	str	r3, [r2, #0]
	if( uxCriticalNesting == 1 )
 800342e:	d000      	beq.n	8003432 <vPortEnterCritical+0x1e>
}
 8003430:	4770      	bx	lr
		configASSERT( ( portNVIC_INT_CTRL_REG & portVECTACTIVE_MASK ) == 0 );
 8003432:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 8003436:	f8d3 3d04 	ldr.w	r3, [r3, #3332]	@ 0xd04
 800343a:	b2db      	uxtb	r3, r3
 800343c:	2b00      	cmp	r3, #0
 800343e:	d0f7      	beq.n	8003430 <vPortEnterCritical+0x1c>
 8003440:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8003444:	f383 8811 	msr	BASEPRI, r3
 8003448:	f3bf 8f6f 	isb	sy
 800344c:	f3bf 8f4f 	dsb	sy
 8003450:	e7fe      	b.n	8003450 <vPortEnterCritical+0x3c>
 8003452:	bf00      	nop
 8003454:	2000000c 	.word	0x2000000c

08003458 <vPortExitCritical>:
	configASSERT( uxCriticalNesting );
 8003458:	4a08      	ldr	r2, [pc, #32]	@ (800347c <vPortExitCritical+0x24>)
 800345a:	6813      	ldr	r3, [r2, #0]
 800345c:	b943      	cbnz	r3, 8003470 <vPortExitCritical+0x18>
 800345e:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8003462:	f383 8811 	msr	BASEPRI, r3
 8003466:	f3bf 8f6f 	isb	sy
 800346a:	f3bf 8f4f 	dsb	sy
 800346e:	e7fe      	b.n	800346e <vPortExitCritical+0x16>
	uxCriticalNesting--;
 8003470:	3b01      	subs	r3, #1
 8003472:	6013      	str	r3, [r2, #0]
	if( uxCriticalNesting == 0 )
 8003474:	b90b      	cbnz	r3, 800347a <vPortExitCritical+0x22>
	__asm volatile
 8003476:	f383 8811 	msr	BASEPRI, r3
}
 800347a:	4770      	bx	lr
 800347c:	2000000c 	.word	0x2000000c

08003480 <PendSV_Handler>:
	__asm volatile
 8003480:	f3ef 8009 	mrs	r0, PSP
 8003484:	f3bf 8f6f 	isb	sy
 8003488:	4b15      	ldr	r3, [pc, #84]	@ (80034e0 <pxCurrentTCBConst>)
 800348a:	681a      	ldr	r2, [r3, #0]
 800348c:	f01e 0f10 	tst.w	lr, #16
 8003490:	bf08      	it	eq
 8003492:	ed20 8a10 	vstmdbeq	r0!, {s16-s31}
 8003496:	e920 4ff0 	stmdb	r0!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
 800349a:	6010      	str	r0, [r2, #0]
 800349c:	e92d 0009 	stmdb	sp!, {r0, r3}
 80034a0:	f04f 0050 	mov.w	r0, #80	@ 0x50
 80034a4:	f380 8811 	msr	BASEPRI, r0
 80034a8:	f3bf 8f4f 	dsb	sy
 80034ac:	f3bf 8f6f 	isb	sy
 80034b0:	f7ff fbd8 	bl	8002c64 <vTaskSwitchContext>
 80034b4:	f04f 0000 	mov.w	r0, #0
 80034b8:	f380 8811 	msr	BASEPRI, r0
 80034bc:	bc09      	pop	{r0, r3}
 80034be:	6819      	ldr	r1, [r3, #0]
 80034c0:	6808      	ldr	r0, [r1, #0]
 80034c2:	e8b0 4ff0 	ldmia.w	r0!, {r4, r5, r6, r7, r8, r9, sl, fp, lr}
 80034c6:	f01e 0f10 	tst.w	lr, #16
 80034ca:	bf08      	it	eq
 80034cc:	ecb0 8a10 	vldmiaeq	r0!, {s16-s31}
 80034d0:	f380 8809 	msr	PSP, r0
 80034d4:	f3bf 8f6f 	isb	sy
 80034d8:	4770      	bx	lr
 80034da:	bf00      	nop
 80034dc:	f3af 8000 	nop.w

080034e0 <pxCurrentTCBConst>:
 80034e0:	20000d20 	.word	0x20000d20

080034e4 <xPortSysTickHandler>:
{
 80034e4:	b508      	push	{r3, lr}
	__asm volatile
 80034e6:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80034ea:	f383 8811 	msr	BASEPRI, r3
 80034ee:	f3bf 8f6f 	isb	sy
 80034f2:	f3bf 8f4f 	dsb	sy
		if( xTaskIncrementTick() != pdFALSE )
 80034f6:	f7ff fa3b 	bl	8002970 <xTaskIncrementTick>
 80034fa:	b128      	cbz	r0, 8003508 <xPortSysTickHandler+0x24>
			portNVIC_INT_CTRL_REG = portNVIC_PENDSVSET_BIT;
 80034fc:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 8003500:	f04f 5280 	mov.w	r2, #268435456	@ 0x10000000
 8003504:	f8c3 2d04 	str.w	r2, [r3, #3332]	@ 0xd04
	__asm volatile
 8003508:	2300      	movs	r3, #0
 800350a:	f383 8811 	msr	BASEPRI, r3
}
 800350e:	bd08      	pop	{r3, pc}

08003510 <vPortSetupTimerInterrupt>:
	portNVIC_SYSTICK_CTRL_REG = 0UL;
 8003510:	f04f 22e0 	mov.w	r2, #3758153728	@ 0xe000e000
 8003514:	2300      	movs	r3, #0
	portNVIC_SYSTICK_LOAD_REG = ( configSYSTICK_CLOCK_HZ / configTICK_RATE_HZ ) - 1UL;
 8003516:	4906      	ldr	r1, [pc, #24]	@ (8003530 <vPortSetupTimerInterrupt+0x20>)
	portNVIC_SYSTICK_CTRL_REG = 0UL;
 8003518:	6113      	str	r3, [r2, #16]
	portNVIC_SYSTICK_CURRENT_VALUE_REG = 0UL;
 800351a:	6193      	str	r3, [r2, #24]
	portNVIC_SYSTICK_LOAD_REG = ( configSYSTICK_CLOCK_HZ / configTICK_RATE_HZ ) - 1UL;
 800351c:	680b      	ldr	r3, [r1, #0]
 800351e:	4905      	ldr	r1, [pc, #20]	@ (8003534 <vPortSetupTimerInterrupt+0x24>)
 8003520:	fba1 1303 	umull	r1, r3, r1, r3
 8003524:	099b      	lsrs	r3, r3, #6
	portNVIC_SYSTICK_CTRL_REG = ( portNVIC_SYSTICK_CLK_BIT | portNVIC_SYSTICK_INT_BIT | portNVIC_SYSTICK_ENABLE_BIT );
 8003526:	2007      	movs	r0, #7
	portNVIC_SYSTICK_LOAD_REG = ( configSYSTICK_CLOCK_HZ / configTICK_RATE_HZ ) - 1UL;
 8003528:	3b01      	subs	r3, #1
 800352a:	6153      	str	r3, [r2, #20]
	portNVIC_SYSTICK_CTRL_REG = ( portNVIC_SYSTICK_CLK_BIT | portNVIC_SYSTICK_INT_BIT | portNVIC_SYSTICK_ENABLE_BIT );
 800352c:	6110      	str	r0, [r2, #16]
}
 800352e:	4770      	bx	lr
 8003530:	20000000 	.word	0x20000000
 8003534:	10624dd3 	.word	0x10624dd3

08003538 <xPortStartScheduler>:
	configASSERT( portCPUID != portCORTEX_M7_r0p1_ID );
 8003538:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 800353c:	4a3d      	ldr	r2, [pc, #244]	@ (8003634 <xPortStartScheduler+0xfc>)
 800353e:	f8d3 1d00 	ldr.w	r1, [r3, #3328]	@ 0xd00
 8003542:	4291      	cmp	r1, r2
 8003544:	d041      	beq.n	80035ca <xPortStartScheduler+0x92>
	configASSERT( portCPUID != portCORTEX_M7_r0p0_ID );
 8003546:	f8d3 2d00 	ldr.w	r2, [r3, #3328]	@ 0xd00
 800354a:	4b3b      	ldr	r3, [pc, #236]	@ (8003638 <xPortStartScheduler+0x100>)
 800354c:	429a      	cmp	r2, r3
 800354e:	d033      	beq.n	80035b8 <xPortStartScheduler+0x80>
{
 8003550:	b530      	push	{r4, r5, lr}
		ulOriginalPriority = *pucFirstUserPriorityRegister;
 8003552:	4b3a      	ldr	r3, [pc, #232]	@ (800363c <xPortStartScheduler+0x104>)
		ucMaxSysCallPriority = configMAX_SYSCALL_INTERRUPT_PRIORITY & ucMaxPriorityValue;
 8003554:	4c3a      	ldr	r4, [pc, #232]	@ (8003640 <xPortStartScheduler+0x108>)
		ulOriginalPriority = *pucFirstUserPriorityRegister;
 8003556:	781a      	ldrb	r2, [r3, #0]
		ulMaxPRIGROUPValue = portMAX_PRIGROUP_BITS;
 8003558:	483a      	ldr	r0, [pc, #232]	@ (8003644 <xPortStartScheduler+0x10c>)
{
 800355a:	b083      	sub	sp, #12
		*pucFirstUserPriorityRegister = portMAX_8_BIT_VALUE;
 800355c:	21ff      	movs	r1, #255	@ 0xff
		ulOriginalPriority = *pucFirstUserPriorityRegister;
 800355e:	b2d2      	uxtb	r2, r2
 8003560:	9201      	str	r2, [sp, #4]
		*pucFirstUserPriorityRegister = portMAX_8_BIT_VALUE;
 8003562:	7019      	strb	r1, [r3, #0]
		ucMaxPriorityValue = *pucFirstUserPriorityRegister;
 8003564:	781b      	ldrb	r3, [r3, #0]
 8003566:	b2db      	uxtb	r3, r3
 8003568:	f88d 3003 	strb.w	r3, [sp, #3]
		ucMaxSysCallPriority = configMAX_SYSCALL_INTERRUPT_PRIORITY & ucMaxPriorityValue;
 800356c:	f89d 3003 	ldrb.w	r3, [sp, #3]
		while( ( ucMaxPriorityValue & portTOP_BIT_OF_BYTE ) == portTOP_BIT_OF_BYTE )
 8003570:	f89d 2003 	ldrb.w	r2, [sp, #3]
		ucMaxSysCallPriority = configMAX_SYSCALL_INTERRUPT_PRIORITY & ucMaxPriorityValue;
 8003574:	f003 0350 	and.w	r3, r3, #80	@ 0x50
		ulMaxPRIGROUPValue = portMAX_PRIGROUP_BITS;
 8003578:	2107      	movs	r1, #7
		while( ( ucMaxPriorityValue & portTOP_BIT_OF_BYTE ) == portTOP_BIT_OF_BYTE )
 800357a:	0612      	lsls	r2, r2, #24
		ucMaxSysCallPriority = configMAX_SYSCALL_INTERRUPT_PRIORITY & ucMaxPriorityValue;
 800357c:	7023      	strb	r3, [r4, #0]
		ulMaxPRIGROUPValue = portMAX_PRIGROUP_BITS;
 800357e:	6001      	str	r1, [r0, #0]
		while( ( ucMaxPriorityValue & portTOP_BIT_OF_BYTE ) == portTOP_BIT_OF_BYTE )
 8003580:	bf48      	it	mi
 8003582:	2206      	movmi	r2, #6
 8003584:	d50f      	bpl.n	80035a6 <xPortStartScheduler+0x6e>
			ucMaxPriorityValue <<= ( uint8_t ) 0x01;
 8003586:	f89d 3003 	ldrb.w	r3, [sp, #3]
 800358a:	005b      	lsls	r3, r3, #1
 800358c:	b2db      	uxtb	r3, r3
 800358e:	f88d 3003 	strb.w	r3, [sp, #3]
		while( ( ucMaxPriorityValue & portTOP_BIT_OF_BYTE ) == portTOP_BIT_OF_BYTE )
 8003592:	f89d 3003 	ldrb.w	r3, [sp, #3]
 8003596:	061b      	lsls	r3, r3, #24
 8003598:	4611      	mov	r1, r2
 800359a:	f102 32ff 	add.w	r2, r2, #**********
 800359e:	d4f2      	bmi.n	8003586 <xPortStartScheduler+0x4e>
			configASSERT( ( portMAX_PRIGROUP_BITS - ulMaxPRIGROUPValue ) == configPRIO_BITS );
 80035a0:	2903      	cmp	r1, #3
 80035a2:	d01b      	beq.n	80035dc <xPortStartScheduler+0xa4>
 80035a4:	6001      	str	r1, [r0, #0]
	__asm volatile
 80035a6:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80035aa:	f383 8811 	msr	BASEPRI, r3
 80035ae:	f3bf 8f6f 	isb	sy
 80035b2:	f3bf 8f4f 	dsb	sy
 80035b6:	e7fe      	b.n	80035b6 <xPortStartScheduler+0x7e>
 80035b8:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80035bc:	f383 8811 	msr	BASEPRI, r3
 80035c0:	f3bf 8f6f 	isb	sy
 80035c4:	f3bf 8f4f 	dsb	sy
	configASSERT( portCPUID != portCORTEX_M7_r0p0_ID );
 80035c8:	e7fe      	b.n	80035c8 <xPortStartScheduler+0x90>
 80035ca:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80035ce:	f383 8811 	msr	BASEPRI, r3
 80035d2:	f3bf 8f6f 	isb	sy
 80035d6:	f3bf 8f4f 	dsb	sy
	configASSERT( portCPUID != portCORTEX_M7_r0p1_ID );
 80035da:	e7fe      	b.n	80035da <xPortStartScheduler+0xa2>
		*pucFirstUserPriorityRegister = ulOriginalPriority;
 80035dc:	9b01      	ldr	r3, [sp, #4]
 80035de:	4a17      	ldr	r2, [pc, #92]	@ (800363c <xPortStartScheduler+0x104>)
	portNVIC_SYSPRI2_REG |= portNVIC_PENDSV_PRI;
 80035e0:	f04f 24e0 	mov.w	r4, #3758153728	@ 0xe000e000
		ulMaxPRIGROUPValue &= portPRIORITY_GROUP_MASK;
 80035e4:	f44f 7140 	mov.w	r1, #768	@ 0x300
		*pucFirstUserPriorityRegister = ulOriginalPriority;
 80035e8:	b2db      	uxtb	r3, r3
		ulMaxPRIGROUPValue &= portPRIORITY_GROUP_MASK;
 80035ea:	6001      	str	r1, [r0, #0]
		*pucFirstUserPriorityRegister = ulOriginalPriority;
 80035ec:	7013      	strb	r3, [r2, #0]
	portNVIC_SYSPRI2_REG |= portNVIC_PENDSV_PRI;
 80035ee:	f8d4 3d20 	ldr.w	r3, [r4, #3360]	@ 0xd20
 80035f2:	f443 0370 	orr.w	r3, r3, #15728640	@ 0xf00000
 80035f6:	f8c4 3d20 	str.w	r3, [r4, #3360]	@ 0xd20
	portNVIC_SYSPRI2_REG |= portNVIC_SYSTICK_PRI;
 80035fa:	f8d4 3d20 	ldr.w	r3, [r4, #3360]	@ 0xd20
 80035fe:	f043 4370 	orr.w	r3, r3, #4026531840	@ 0xf0000000
 8003602:	f8c4 3d20 	str.w	r3, [r4, #3360]	@ 0xd20
	vPortSetupTimerInterrupt();
 8003606:	f7ff ff83 	bl	8003510 <vPortSetupTimerInterrupt>
	uxCriticalNesting = 0;
 800360a:	4b0f      	ldr	r3, [pc, #60]	@ (8003648 <xPortStartScheduler+0x110>)
 800360c:	2500      	movs	r5, #0
 800360e:	601d      	str	r5, [r3, #0]
	vPortEnableVFP();
 8003610:	f7ff feaa 	bl	8003368 <vPortEnableVFP>
	*( portFPCCR ) |= portASPEN_AND_LSPEN_BITS;
 8003614:	f8d4 3f34 	ldr.w	r3, [r4, #3892]	@ 0xf34
 8003618:	f043 4340 	orr.w	r3, r3, #3221225472	@ 0xc0000000
 800361c:	f8c4 3f34 	str.w	r3, [r4, #3892]	@ 0xf34
	prvPortStartFirstTask();
 8003620:	f7ff fe8e 	bl	8003340 <prvPortStartFirstTask>
	vTaskSwitchContext();
 8003624:	f7ff fb1e 	bl	8002c64 <vTaskSwitchContext>
	prvTaskExitError();
 8003628:	f7ff fea8 	bl	800337c <prvTaskExitError>
}
 800362c:	4628      	mov	r0, r5
 800362e:	b003      	add	sp, #12
 8003630:	bd30      	pop	{r4, r5, pc}
 8003632:	bf00      	nop
 8003634:	410fc271 	.word	0x410fc271
 8003638:	410fc270 	.word	0x410fc270
 800363c:	e000e400 	.word	0xe000e400
 8003640:	20000e54 	.word	0x20000e54
 8003644:	20000e50 	.word	0x20000e50
 8003648:	2000000c 	.word	0x2000000c

0800364c <vPortValidateInterruptPriority>:
	{
	uint32_t ulCurrentInterrupt;
	uint8_t ucCurrentPriority;

		/* Obtain the number of the currently executing interrupt. */
		__asm volatile( "mrs %0, ipsr" : "=r"( ulCurrentInterrupt ) :: "memory" );
 800364c:	f3ef 8305 	mrs	r3, IPSR

		/* Is the interrupt number a user defined interrupt? */
		if( ulCurrentInterrupt >= portFIRST_USER_INTERRUPT_NUMBER )
 8003650:	2b0f      	cmp	r3, #15
 8003652:	d90e      	bls.n	8003672 <vPortValidateInterruptPriority+0x26>
		{
			/* Look up the interrupt's priority. */
			ucCurrentPriority = pcInterruptPriorityRegisters[ ulCurrentInterrupt ];
 8003654:	4911      	ldr	r1, [pc, #68]	@ (800369c <vPortValidateInterruptPriority+0x50>)
			interrupt entry is as fast and simple as possible.

			The following links provide detailed information:
			http://www.freertos.org/RTOS-Cortex-M3-M4.html
			http://www.freertos.org/FAQHelp.html */
			configASSERT( ucCurrentPriority >= ucMaxSysCallPriority );
 8003656:	4a12      	ldr	r2, [pc, #72]	@ (80036a0 <vPortValidateInterruptPriority+0x54>)
			ucCurrentPriority = pcInterruptPriorityRegisters[ ulCurrentInterrupt ];
 8003658:	5c5b      	ldrb	r3, [r3, r1]
			configASSERT( ucCurrentPriority >= ucMaxSysCallPriority );
 800365a:	7812      	ldrb	r2, [r2, #0]
 800365c:	429a      	cmp	r2, r3
 800365e:	d908      	bls.n	8003672 <vPortValidateInterruptPriority+0x26>
 8003660:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8003664:	f383 8811 	msr	BASEPRI, r3
 8003668:	f3bf 8f6f 	isb	sy
 800366c:	f3bf 8f4f 	dsb	sy
 8003670:	e7fe      	b.n	8003670 <vPortValidateInterruptPriority+0x24>
		configuration then the correct setting can be achieved on all Cortex-M
		devices by calling NVIC_SetPriorityGrouping( 0 ); before starting the
		scheduler.  Note however that some vendor specific peripheral libraries
		assume a non-zero priority group setting, in which cases using a value
		of zero will result in unpredictable behaviour. */
		configASSERT( ( portAIRCR_REG & portPRIORITY_GROUP_MASK ) <= ulMaxPRIGROUPValue );
 8003672:	f04f 23e0 	mov.w	r3, #3758153728	@ 0xe000e000
 8003676:	4a0b      	ldr	r2, [pc, #44]	@ (80036a4 <vPortValidateInterruptPriority+0x58>)
 8003678:	f8d3 3d0c 	ldr.w	r3, [r3, #3340]	@ 0xd0c
 800367c:	6812      	ldr	r2, [r2, #0]
 800367e:	f403 63e0 	and.w	r3, r3, #1792	@ 0x700
 8003682:	4293      	cmp	r3, r2
 8003684:	d908      	bls.n	8003698 <vPortValidateInterruptPriority+0x4c>
 8003686:	f04f 0350 	mov.w	r3, #80	@ 0x50
 800368a:	f383 8811 	msr	BASEPRI, r3
 800368e:	f3bf 8f6f 	isb	sy
 8003692:	f3bf 8f4f 	dsb	sy
 8003696:	e7fe      	b.n	8003696 <vPortValidateInterruptPriority+0x4a>
	}
 8003698:	4770      	bx	lr
 800369a:	bf00      	nop
 800369c:	e000e3f0 	.word	0xe000e3f0
 80036a0:	20000e54 	.word	0x20000e54
 80036a4:	20000e50 	.word	0x20000e50

080036a8 <prvInsertBlockIntoFreeList>:
	xBlockAllocatedBit = ( ( size_t ) 1 ) << ( ( sizeof( size_t ) * heapBITS_PER_BYTE ) - 1 );
}
/*-----------------------------------------------------------*/

static void prvInsertBlockIntoFreeList( BlockLink_t *pxBlockToInsert )
{
 80036a8:	b410      	push	{r4}
BlockLink_t *pxIterator;
uint8_t *puc;

	/* Iterate through the list until a block is found that has a higher address
	than the block being inserted. */
	for( pxIterator = &xStart; pxIterator->pxNextFreeBlock < pxBlockToInsert; pxIterator = pxIterator->pxNextFreeBlock )
 80036aa:	4b15      	ldr	r3, [pc, #84]	@ (8003700 <prvInsertBlockIntoFreeList+0x58>)
 80036ac:	461a      	mov	r2, r3
 80036ae:	681b      	ldr	r3, [r3, #0]
 80036b0:	4283      	cmp	r3, r0
 80036b2:	d3fb      	bcc.n	80036ac <prvInsertBlockIntoFreeList+0x4>
	}

	/* Do the block being inserted, and the block it is being inserted after
	make a contiguous block of memory? */
	puc = ( uint8_t * ) pxIterator;
	if( ( puc + pxIterator->xBlockSize ) == ( uint8_t * ) pxBlockToInsert )
 80036b4:	6854      	ldr	r4, [r2, #4]
	{
		pxIterator->xBlockSize += pxBlockToInsert->xBlockSize;
 80036b6:	6841      	ldr	r1, [r0, #4]
	if( ( puc + pxIterator->xBlockSize ) == ( uint8_t * ) pxBlockToInsert )
 80036b8:	eb02 0c04 	add.w	ip, r2, r4
 80036bc:	4560      	cmp	r0, ip
 80036be:	d013      	beq.n	80036e8 <prvInsertBlockIntoFreeList+0x40>
	}

	/* Do the block being inserted, and the block it is being inserted before
	make a contiguous block of memory? */
	puc = ( uint8_t * ) pxBlockToInsert;
	if( ( puc + pxBlockToInsert->xBlockSize ) == ( uint8_t * ) pxIterator->pxNextFreeBlock )
 80036c0:	1844      	adds	r4, r0, r1
 80036c2:	42a3      	cmp	r3, r4
 80036c4:	d006      	beq.n	80036d4 <prvInsertBlockIntoFreeList+0x2c>
			pxBlockToInsert->pxNextFreeBlock = pxEnd;
		}
	}
	else
	{
		pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock;
 80036c6:	6003      	str	r3, [r0, #0]

	/* If the block being inserted plugged a gab, so was merged with the block
	before and the block after, then it's pxNextFreeBlock pointer will have
	already been set, and should not be set here as that would make it point
	to itself. */
	if( pxIterator != pxBlockToInsert )
 80036c8:	4282      	cmp	r2, r0
	{
		pxIterator->pxNextFreeBlock = pxBlockToInsert;
 80036ca:	bf18      	it	ne
 80036cc:	6010      	strne	r0, [r2, #0]
	}
	else
	{
		mtCOVERAGE_TEST_MARKER();
	}
}
 80036ce:	f85d 4b04 	ldr.w	r4, [sp], #4
 80036d2:	4770      	bx	lr
		if( pxIterator->pxNextFreeBlock != pxEnd )
 80036d4:	4c0b      	ldr	r4, [pc, #44]	@ (8003704 <prvInsertBlockIntoFreeList+0x5c>)
 80036d6:	6824      	ldr	r4, [r4, #0]
 80036d8:	42a3      	cmp	r3, r4
 80036da:	d0f4      	beq.n	80036c6 <prvInsertBlockIntoFreeList+0x1e>
			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 80036dc:	e9d3 3400 	ldrd	r3, r4, [r3]
			pxBlockToInsert->xBlockSize += pxIterator->pxNextFreeBlock->xBlockSize;
 80036e0:	4421      	add	r1, r4
			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 80036e2:	6003      	str	r3, [r0, #0]
			pxBlockToInsert->xBlockSize += pxIterator->pxNextFreeBlock->xBlockSize;
 80036e4:	6041      	str	r1, [r0, #4]
			pxBlockToInsert->pxNextFreeBlock = pxIterator->pxNextFreeBlock->pxNextFreeBlock;
 80036e6:	e7ef      	b.n	80036c8 <prvInsertBlockIntoFreeList+0x20>
		pxIterator->xBlockSize += pxBlockToInsert->xBlockSize;
 80036e8:	4421      	add	r1, r4
	if( ( puc + pxBlockToInsert->xBlockSize ) == ( uint8_t * ) pxIterator->pxNextFreeBlock )
 80036ea:	1850      	adds	r0, r2, r1
 80036ec:	4283      	cmp	r3, r0
		pxIterator->xBlockSize += pxBlockToInsert->xBlockSize;
 80036ee:	6051      	str	r1, [r2, #4]
	if( ( puc + pxBlockToInsert->xBlockSize ) == ( uint8_t * ) pxIterator->pxNextFreeBlock )
 80036f0:	d1ed      	bne.n	80036ce <prvInsertBlockIntoFreeList+0x26>
		if( pxIterator->pxNextFreeBlock != pxEnd )
 80036f2:	4804      	ldr	r0, [pc, #16]	@ (8003704 <prvInsertBlockIntoFreeList+0x5c>)
 80036f4:	6800      	ldr	r0, [r0, #0]
 80036f6:	4283      	cmp	r3, r0
 80036f8:	d0e9      	beq.n	80036ce <prvInsertBlockIntoFreeList+0x26>
		pxBlockToInsert = pxIterator;
 80036fa:	4610      	mov	r0, r2
 80036fc:	e7ee      	b.n	80036dc <prvInsertBlockIntoFreeList+0x34>
 80036fe:	bf00      	nop
 8003700:	20000e70 	.word	0x20000e70
 8003704:	20000e6c 	.word	0x20000e6c

08003708 <pvPortMalloc>:
{
 8003708:	e92d 43f8 	stmdb	sp!, {r3, r4, r5, r6, r7, r8, r9, lr}
		if( pxEnd == NULL )
 800370c:	4e40      	ldr	r6, [pc, #256]	@ (8003810 <pvPortMalloc+0x108>)
{
 800370e:	4604      	mov	r4, r0
	vTaskSuspendAll();
 8003710:	f7ff f920 	bl	8002954 <vTaskSuspendAll>
		if( pxEnd == NULL )
 8003714:	6833      	ldr	r3, [r6, #0]
 8003716:	2b00      	cmp	r3, #0
 8003718:	d05c      	beq.n	80037d4 <pvPortMalloc+0xcc>
		if( ( xWantedSize & xBlockAllocatedBit ) == 0 )
 800371a:	4b3e      	ldr	r3, [pc, #248]	@ (8003814 <pvPortMalloc+0x10c>)
 800371c:	681d      	ldr	r5, [r3, #0]
 800371e:	422c      	tst	r4, r5
 8003720:	d12e      	bne.n	8003780 <pvPortMalloc+0x78>
			if( xWantedSize > 0 )
 8003722:	b36c      	cbz	r4, 8003780 <pvPortMalloc+0x78>
				xWantedSize += xHeapStructSize;
 8003724:	f104 0108 	add.w	r1, r4, #8
				if( ( xWantedSize & portBYTE_ALIGNMENT_MASK ) != 0x00 )
 8003728:	0760      	lsls	r0, r4, #29
					xWantedSize += ( portBYTE_ALIGNMENT - ( xWantedSize & portBYTE_ALIGNMENT_MASK ) );
 800372a:	bf1c      	itt	ne
 800372c:	f021 0107 	bicne.w	r1, r1, #7
 8003730:	3108      	addne	r1, #8
			if( ( xWantedSize > 0 ) && ( xWantedSize <= xFreeBytesRemaining ) )
 8003732:	b329      	cbz	r1, 8003780 <pvPortMalloc+0x78>
 8003734:	f8df 80f4 	ldr.w	r8, [pc, #244]	@ 800382c <pvPortMalloc+0x124>
 8003738:	f8d8 7000 	ldr.w	r7, [r8]
 800373c:	428f      	cmp	r7, r1
 800373e:	d31f      	bcc.n	8003780 <pvPortMalloc+0x78>
				pxBlock = xStart.pxNextFreeBlock;
 8003740:	4835      	ldr	r0, [pc, #212]	@ (8003818 <pvPortMalloc+0x110>)
 8003742:	6804      	ldr	r4, [r0, #0]
				while( ( pxBlock->xBlockSize < xWantedSize ) && ( pxBlock->pxNextFreeBlock != NULL ) )
 8003744:	e003      	b.n	800374e <pvPortMalloc+0x46>
 8003746:	6823      	ldr	r3, [r4, #0]
 8003748:	b123      	cbz	r3, 8003754 <pvPortMalloc+0x4c>
					pxPreviousBlock = pxBlock;
 800374a:	4620      	mov	r0, r4
					pxBlock = pxBlock->pxNextFreeBlock;
 800374c:	461c      	mov	r4, r3
				while( ( pxBlock->xBlockSize < xWantedSize ) && ( pxBlock->pxNextFreeBlock != NULL ) )
 800374e:	6862      	ldr	r2, [r4, #4]
 8003750:	428a      	cmp	r2, r1
 8003752:	d3f8      	bcc.n	8003746 <pvPortMalloc+0x3e>
				if( pxBlock != pxEnd )
 8003754:	6833      	ldr	r3, [r6, #0]
 8003756:	42a3      	cmp	r3, r4
 8003758:	d012      	beq.n	8003780 <pvPortMalloc+0x78>
					if( ( pxBlock->xBlockSize - xWantedSize ) > heapMINIMUM_BLOCK_SIZE )
 800375a:	1a53      	subs	r3, r2, r1
					pxPreviousBlock->pxNextFreeBlock = pxBlock->pxNextFreeBlock;
 800375c:	6826      	ldr	r6, [r4, #0]
					pvReturn = ( void * ) ( ( ( uint8_t * ) pxPreviousBlock->pxNextFreeBlock ) + xHeapStructSize );
 800375e:	f8d0 9000 	ldr.w	r9, [r0]
					pxPreviousBlock->pxNextFreeBlock = pxBlock->pxNextFreeBlock;
 8003762:	6006      	str	r6, [r0, #0]
					if( ( pxBlock->xBlockSize - xWantedSize ) > heapMINIMUM_BLOCK_SIZE )
 8003764:	2b10      	cmp	r3, #16
 8003766:	d916      	bls.n	8003796 <pvPortMalloc+0x8e>
						pxNewBlockLink = ( void * ) ( ( ( uint8_t * ) pxBlock ) + xWantedSize );
 8003768:	1860      	adds	r0, r4, r1
						configASSERT( ( ( ( size_t ) pxNewBlockLink ) & portBYTE_ALIGNMENT_MASK ) == 0 );
 800376a:	0742      	lsls	r2, r0, #29
 800376c:	d00e      	beq.n	800378c <pvPortMalloc+0x84>
 800376e:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8003772:	f383 8811 	msr	BASEPRI, r3
 8003776:	f3bf 8f6f 	isb	sy
 800377a:	f3bf 8f4f 	dsb	sy
 800377e:	e7fe      	b.n	800377e <pvPortMalloc+0x76>
	( void ) xTaskResumeAll();
 8003780:	f7ff fa30 	bl	8002be4 <xTaskResumeAll>
void *pvReturn = NULL;
 8003784:	2600      	movs	r6, #0
}
 8003786:	4630      	mov	r0, r6
 8003788:	e8bd 83f8 	ldmia.w	sp!, {r3, r4, r5, r6, r7, r8, r9, pc}
						pxNewBlockLink->xBlockSize = pxBlock->xBlockSize - xWantedSize;
 800378c:	6043      	str	r3, [r0, #4]
						pxBlock->xBlockSize = xWantedSize;
 800378e:	6061      	str	r1, [r4, #4]
						prvInsertBlockIntoFreeList( pxNewBlockLink );
 8003790:	f7ff ff8a 	bl	80036a8 <prvInsertBlockIntoFreeList>
					xFreeBytesRemaining -= pxBlock->xBlockSize;
 8003794:	6862      	ldr	r2, [r4, #4]
					if( xFreeBytesRemaining < xMinimumEverFreeBytesRemaining )
 8003796:	4b21      	ldr	r3, [pc, #132]	@ (800381c <pvPortMalloc+0x114>)
 8003798:	6819      	ldr	r1, [r3, #0]
					xFreeBytesRemaining -= pxBlock->xBlockSize;
 800379a:	1abf      	subs	r7, r7, r2
					if( xFreeBytesRemaining < xMinimumEverFreeBytesRemaining )
 800379c:	428f      	cmp	r7, r1
					xNumberOfSuccessfulAllocations++;
 800379e:	4920      	ldr	r1, [pc, #128]	@ (8003820 <pvPortMalloc+0x118>)
						xMinimumEverFreeBytesRemaining = xFreeBytesRemaining;
 80037a0:	bf38      	it	cc
 80037a2:	601f      	strcc	r7, [r3, #0]
					pxBlock->xBlockSize |= xBlockAllocatedBit;
 80037a4:	432a      	orrs	r2, r5
					pxBlock->pxNextFreeBlock = NULL;
 80037a6:	2300      	movs	r3, #0
 80037a8:	e9c4 3200 	strd	r3, r2, [r4]
					xNumberOfSuccessfulAllocations++;
 80037ac:	680b      	ldr	r3, [r1, #0]
					xFreeBytesRemaining -= pxBlock->xBlockSize;
 80037ae:	f8c8 7000 	str.w	r7, [r8]
					xNumberOfSuccessfulAllocations++;
 80037b2:	3301      	adds	r3, #1
					pvReturn = ( void * ) ( ( ( uint8_t * ) pxPreviousBlock->pxNextFreeBlock ) + xHeapStructSize );
 80037b4:	f109 0608 	add.w	r6, r9, #8
					xNumberOfSuccessfulAllocations++;
 80037b8:	600b      	str	r3, [r1, #0]
	( void ) xTaskResumeAll();
 80037ba:	f7ff fa13 	bl	8002be4 <xTaskResumeAll>
	configASSERT( ( ( ( size_t ) pvReturn ) & ( size_t ) portBYTE_ALIGNMENT_MASK ) == 0 );
 80037be:	0773      	lsls	r3, r6, #29
 80037c0:	d0e1      	beq.n	8003786 <pvPortMalloc+0x7e>
 80037c2:	f04f 0350 	mov.w	r3, #80	@ 0x50
 80037c6:	f383 8811 	msr	BASEPRI, r3
 80037ca:	f3bf 8f6f 	isb	sy
 80037ce:	f3bf 8f4f 	dsb	sy
 80037d2:	e7fe      	b.n	80037d2 <pvPortMalloc+0xca>
	uxAddress = ( size_t ) ucHeap;
 80037d4:	4b13      	ldr	r3, [pc, #76]	@ (8003824 <pvPortMalloc+0x11c>)
	uxAddress -= xHeapStructSize;
 80037d6:	4a14      	ldr	r2, [pc, #80]	@ (8003828 <pvPortMalloc+0x120>)
	xMinimumEverFreeBytesRemaining = pxFirstFreeBlock->xBlockSize;
 80037d8:	4910      	ldr	r1, [pc, #64]	@ (800381c <pvPortMalloc+0x114>)
	xBlockAllocatedBit = ( ( size_t ) 1 ) << ( ( sizeof( size_t ) * heapBITS_PER_BYTE ) - 1 );
 80037da:	4f0e      	ldr	r7, [pc, #56]	@ (8003814 <pvPortMalloc+0x10c>)
	if( ( uxAddress & portBYTE_ALIGNMENT_MASK ) != 0 )
 80037dc:	075d      	lsls	r5, r3, #29
		uxAddress += ( portBYTE_ALIGNMENT - 1 );
 80037de:	bf18      	it	ne
 80037e0:	3307      	addne	r3, #7
	xStart.pxNextFreeBlock = ( void * ) pucAlignedHeap;
 80037e2:	4d0d      	ldr	r5, [pc, #52]	@ (8003818 <pvPortMalloc+0x110>)
	uxAddress &= ~( ( size_t ) portBYTE_ALIGNMENT_MASK );
 80037e4:	f022 0207 	bic.w	r2, r2, #7
		uxAddress &= ~( ( size_t ) portBYTE_ALIGNMENT_MASK );
 80037e8:	bf18      	it	ne
 80037ea:	f023 0307 	bicne.w	r3, r3, #7
	xStart.pxNextFreeBlock = ( void * ) pucAlignedHeap;
 80037ee:	602b      	str	r3, [r5, #0]
 80037f0:	4618      	mov	r0, r3
	pxFirstFreeBlock->xBlockSize = uxAddress - ( size_t ) pxFirstFreeBlock;
 80037f2:	1ad3      	subs	r3, r2, r3
	xMinimumEverFreeBytesRemaining = pxFirstFreeBlock->xBlockSize;
 80037f4:	600b      	str	r3, [r1, #0]
	xFreeBytesRemaining = pxFirstFreeBlock->xBlockSize;
 80037f6:	490d      	ldr	r1, [pc, #52]	@ (800382c <pvPortMalloc+0x124>)
	pxEnd = ( void * ) uxAddress;
 80037f8:	6032      	str	r2, [r6, #0]
	xFreeBytesRemaining = pxFirstFreeBlock->xBlockSize;
 80037fa:	600b      	str	r3, [r1, #0]
	xStart.xBlockSize = ( size_t ) 0;
 80037fc:	2100      	movs	r1, #0
 80037fe:	6069      	str	r1, [r5, #4]
	xBlockAllocatedBit = ( ( size_t ) 1 ) << ( ( sizeof( size_t ) * heapBITS_PER_BYTE ) - 1 );
 8003800:	f04f 4500 	mov.w	r5, #2147483648	@ 0x80000000
	pxEnd->pxNextFreeBlock = NULL;
 8003804:	e9c2 1100 	strd	r1, r1, [r2]
	xBlockAllocatedBit = ( ( size_t ) 1 ) << ( ( sizeof( size_t ) * heapBITS_PER_BYTE ) - 1 );
 8003808:	603d      	str	r5, [r7, #0]
	pxFirstFreeBlock->pxNextFreeBlock = pxEnd;
 800380a:	e9c0 2300 	strd	r2, r3, [r0]
}
 800380e:	e786      	b.n	800371e <pvPortMalloc+0x16>
 8003810:	20000e6c 	.word	0x20000e6c
 8003814:	20000e58 	.word	0x20000e58
 8003818:	20000e70 	.word	0x20000e70
 800381c:	20000e64 	.word	0x20000e64
 8003820:	20000e60 	.word	0x20000e60
 8003824:	20000e78 	.word	0x20000e78
 8003828:	20004a70 	.word	0x20004a70
 800382c:	20000e68 	.word	0x20000e68

08003830 <vPortFree>:
	if( pv != NULL )
 8003830:	b1d0      	cbz	r0, 8003868 <vPortFree+0x38>
		configASSERT( ( pxLink->xBlockSize & xBlockAllocatedBit ) != 0 );
 8003832:	4a1c      	ldr	r2, [pc, #112]	@ (80038a4 <vPortFree+0x74>)
 8003834:	f850 3c04 	ldr.w	r3, [r0, #-4]
 8003838:	6812      	ldr	r2, [r2, #0]
 800383a:	4213      	tst	r3, r2
 800383c:	d00b      	beq.n	8003856 <vPortFree+0x26>
		configASSERT( pxLink->pxNextFreeBlock == NULL );
 800383e:	f850 1c08 	ldr.w	r1, [r0, #-8]
 8003842:	b191      	cbz	r1, 800386a <vPortFree+0x3a>
 8003844:	f04f 0350 	mov.w	r3, #80	@ 0x50
 8003848:	f383 8811 	msr	BASEPRI, r3
 800384c:	f3bf 8f6f 	isb	sy
 8003850:	f3bf 8f4f 	dsb	sy
 8003854:	e7fe      	b.n	8003854 <vPortFree+0x24>
 8003856:	f04f 0350 	mov.w	r3, #80	@ 0x50
 800385a:	f383 8811 	msr	BASEPRI, r3
 800385e:	f3bf 8f6f 	isb	sy
 8003862:	f3bf 8f4f 	dsb	sy
		configASSERT( ( pxLink->xBlockSize & xBlockAllocatedBit ) != 0 );
 8003866:	e7fe      	b.n	8003866 <vPortFree+0x36>
 8003868:	4770      	bx	lr
{
 800386a:	b500      	push	{lr}
				pxLink->xBlockSize &= ~xBlockAllocatedBit;
 800386c:	ea23 0302 	bic.w	r3, r3, r2
{
 8003870:	b083      	sub	sp, #12
				pxLink->xBlockSize &= ~xBlockAllocatedBit;
 8003872:	f840 3c04 	str.w	r3, [r0, #-4]
 8003876:	9001      	str	r0, [sp, #4]
				vTaskSuspendAll();
 8003878:	f7ff f86c 	bl	8002954 <vTaskSuspendAll>
					xFreeBytesRemaining += pxLink->xBlockSize;
 800387c:	4a0a      	ldr	r2, [pc, #40]	@ (80038a8 <vPortFree+0x78>)
 800387e:	9801      	ldr	r0, [sp, #4]
 8003880:	6811      	ldr	r1, [r2, #0]
 8003882:	f850 3c04 	ldr.w	r3, [r0, #-4]
					prvInsertBlockIntoFreeList( ( ( BlockLink_t * ) pxLink ) );
 8003886:	3808      	subs	r0, #8
					xFreeBytesRemaining += pxLink->xBlockSize;
 8003888:	440b      	add	r3, r1
 800388a:	6013      	str	r3, [r2, #0]
					prvInsertBlockIntoFreeList( ( ( BlockLink_t * ) pxLink ) );
 800388c:	f7ff ff0c 	bl	80036a8 <prvInsertBlockIntoFreeList>
					xNumberOfSuccessfulFrees++;
 8003890:	4a06      	ldr	r2, [pc, #24]	@ (80038ac <vPortFree+0x7c>)
 8003892:	6813      	ldr	r3, [r2, #0]
 8003894:	3301      	adds	r3, #1
 8003896:	6013      	str	r3, [r2, #0]
}
 8003898:	b003      	add	sp, #12
 800389a:	f85d eb04 	ldr.w	lr, [sp], #4
				( void ) xTaskResumeAll();
 800389e:	f7ff b9a1 	b.w	8002be4 <xTaskResumeAll>
 80038a2:	bf00      	nop
 80038a4:	20000e58 	.word	0x20000e58
 80038a8:	20000e68 	.word	0x20000e68
 80038ac:	20000e5c 	.word	0x20000e5c

080038b0 <memset>:
 80038b0:	4402      	add	r2, r0
 80038b2:	4603      	mov	r3, r0
 80038b4:	4293      	cmp	r3, r2
 80038b6:	d100      	bne.n	80038ba <memset+0xa>
 80038b8:	4770      	bx	lr
 80038ba:	f803 1b01 	strb.w	r1, [r3], #1
 80038be:	e7f9      	b.n	80038b4 <memset+0x4>

080038c0 <__libc_init_array>:
 80038c0:	b570      	push	{r4, r5, r6, lr}
 80038c2:	4d0d      	ldr	r5, [pc, #52]	@ (80038f8 <__libc_init_array+0x38>)
 80038c4:	4c0d      	ldr	r4, [pc, #52]	@ (80038fc <__libc_init_array+0x3c>)
 80038c6:	1b64      	subs	r4, r4, r5
 80038c8:	10a4      	asrs	r4, r4, #2
 80038ca:	2600      	movs	r6, #0
 80038cc:	42a6      	cmp	r6, r4
 80038ce:	d109      	bne.n	80038e4 <__libc_init_array+0x24>
 80038d0:	4d0b      	ldr	r5, [pc, #44]	@ (8003900 <__libc_init_array+0x40>)
 80038d2:	4c0c      	ldr	r4, [pc, #48]	@ (8003904 <__libc_init_array+0x44>)
 80038d4:	f000 f826 	bl	8003924 <_init>
 80038d8:	1b64      	subs	r4, r4, r5
 80038da:	10a4      	asrs	r4, r4, #2
 80038dc:	2600      	movs	r6, #0
 80038de:	42a6      	cmp	r6, r4
 80038e0:	d105      	bne.n	80038ee <__libc_init_array+0x2e>
 80038e2:	bd70      	pop	{r4, r5, r6, pc}
 80038e4:	f855 3b04 	ldr.w	r3, [r5], #4
 80038e8:	4798      	blx	r3
 80038ea:	3601      	adds	r6, #1
 80038ec:	e7ee      	b.n	80038cc <__libc_init_array+0xc>
 80038ee:	f855 3b04 	ldr.w	r3, [r5], #4
 80038f2:	4798      	blx	r3
 80038f4:	3601      	adds	r6, #1
 80038f6:	e7f2      	b.n	80038de <__libc_init_array+0x1e>
 80038f8:	080039a4 	.word	0x080039a4
 80038fc:	080039a4 	.word	0x080039a4
 8003900:	080039a4 	.word	0x080039a4
 8003904:	080039a8 	.word	0x080039a8

08003908 <memcpy>:
 8003908:	440a      	add	r2, r1
 800390a:	4291      	cmp	r1, r2
 800390c:	f100 33ff 	add.w	r3, r0, #**********
 8003910:	d100      	bne.n	8003914 <memcpy+0xc>
 8003912:	4770      	bx	lr
 8003914:	b510      	push	{r4, lr}
 8003916:	f811 4b01 	ldrb.w	r4, [r1], #1
 800391a:	f803 4f01 	strb.w	r4, [r3, #1]!
 800391e:	4291      	cmp	r1, r2
 8003920:	d1f9      	bne.n	8003916 <memcpy+0xe>
 8003922:	bd10      	pop	{r4, pc}

08003924 <_init>:
 8003924:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8003926:	bf00      	nop
 8003928:	bcf8      	pop	{r3, r4, r5, r6, r7}
 800392a:	bc08      	pop	{r3}
 800392c:	469e      	mov	lr, r3
 800392e:	4770      	bx	lr

08003930 <_fini>:
 8003930:	b5f8      	push	{r3, r4, r5, r6, r7, lr}
 8003932:	bf00      	nop
 8003934:	bcf8      	pop	{r3, r4, r5, r6, r7}
 8003936:	bc08      	pop	{r3}
 8003938:	469e      	mov	lr, r3
 800393a:	4770      	bx	lr
