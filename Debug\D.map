Archive member included to satisfy reference by file (symbol)

D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o (exit)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o) (__stdio_exit_handler)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (_fwalk_sglue)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (__sread)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o (memset)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_close_r)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o) (errno)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o) (_impure_ptr)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_lseek_r)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_read_r)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_write_r)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-errno.o)
                              ./Core/Src/syscalls.o (__errno)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o (__libc_init_array)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (__retarget_lock_init_recursive)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memcpy-stub.o)
                              ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o (memcpy)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o) (_free_r)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (_malloc_r)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o) (__malloc_lock)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (_fflush_r)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o) (_sbrk_r)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_ldivmod.o)
                              ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o (__aeabi_ldivmod)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_uldivmod.o)
                              ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o (__aeabi_uldivmod)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_udivmoddi4.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_ldivmod.o) (__udivmoddi4)
D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_dvmd_tls.o)
                              D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_ldivmod.o) (__aeabi_ldiv0)

Discarded input sections

 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crti.o
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crti.o
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crti.o
 .data          0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o
 .rodata        0x00000000       0x24 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text          0x00000000       0x7c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o
 .ARM.extab     0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o
 .ARM.exidx     0x00000000       0x10 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o
 .ARM.attributes
                0x00000000       0x20 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/adc.o
 .text          0x00000000        0x0 ./Core/Src/adc.o
 .data          0x00000000        0x0 ./Core/Src/adc.o
 .bss           0x00000000        0x0 ./Core/Src/adc.o
 .text.HAL_ADC_MspDeInit
                0x00000000       0x50 ./Core/Src/adc.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/dma.o
 .text          0x00000000        0x0 ./Core/Src/dma.o
 .data          0x00000000        0x0 ./Core/Src/dma.o
 .bss           0x00000000        0x0 ./Core/Src/dma.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x29b ./Core/Src/dma.o
 .debug_macro   0x00000000       0x2e ./Core/Src/dma.o
 .debug_macro   0x00000000       0x2f ./Core/Src/dma.o
 .debug_macro   0x00000000       0x22 ./Core/Src/dma.o
 .debug_macro   0x00000000       0x8e ./Core/Src/dma.o
 .debug_macro   0x00000000       0x51 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x103 ./Core/Src/dma.o
 .debug_macro   0x00000000       0x6a ./Core/Src/dma.o
 .debug_macro   0x00000000      0x1df ./Core/Src/dma.o
 .debug_macro   0x00000000       0x1c ./Core/Src/dma.o
 .debug_macro   0x00000000       0x22 ./Core/Src/dma.o
 .debug_macro   0x00000000       0xfb ./Core/Src/dma.o
 .debug_macro   0x00000000     0x1011 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x11f ./Core/Src/dma.o
 .debug_macro   0x00000000    0x15ea5 ./Core/Src/dma.o
 .debug_macro   0x00000000       0x6d ./Core/Src/dma.o
 .debug_macro   0x00000000     0x3693 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x190 ./Core/Src/dma.o
 .debug_macro   0x00000000       0x5c ./Core/Src/dma.o
 .debug_macro   0x00000000      0x980 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x9e9 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x115 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x13e ./Core/Src/dma.o
 .debug_macro   0x00000000       0xa5 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x174 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x287 ./Core/Src/dma.o
 .debug_macro   0x00000000       0x5f ./Core/Src/dma.o
 .debug_macro   0x00000000      0x236 ./Core/Src/dma.o
 .debug_macro   0x00000000      0xb5b ./Core/Src/dma.o
 .debug_macro   0x00000000      0x38b ./Core/Src/dma.o
 .debug_macro   0x00000000      0x176 ./Core/Src/dma.o
 .debug_macro   0x00000000       0xf9 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x12c ./Core/Src/dma.o
 .debug_macro   0x00000000      0x21e ./Core/Src/dma.o
 .debug_macro   0x00000000       0x2e ./Core/Src/dma.o
 .debug_macro   0x00000000      0x127 ./Core/Src/dma.o
 .debug_macro   0x00000000       0x7e ./Core/Src/dma.o
 .debug_macro   0x00000000       0x89 ./Core/Src/dma.o
 .debug_macro   0x00000000      0x8ed ./Core/Src/dma.o
 .debug_macro   0x00000000       0x4d ./Core/Src/dma.o
 .debug_macro   0x00000000      0x12d ./Core/Src/dma.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/freertos.o
 .text          0x00000000        0x0 ./Core/Src/freertos.o
 .data          0x00000000        0x0 ./Core/Src/freertos.o
 .bss           0x00000000        0x0 ./Core/Src/freertos.o
 .bss.Task02TaskHandle
                0x00000000        0x4 ./Core/Src/freertos.o
 .bss.Task01TaskHandle
                0x00000000        0x4 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x190 ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x22 ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x8e ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x51 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x103 ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x6a ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x1df ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x29b ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x2e ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x2f ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x1c ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x22 ./Core/Src/freertos.o
 .debug_macro   0x00000000       0xfb ./Core/Src/freertos.o
 .debug_macro   0x00000000     0x1011 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x11f ./Core/Src/freertos.o
 .debug_macro   0x00000000    0x15ea5 ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x6d ./Core/Src/freertos.o
 .debug_macro   0x00000000     0x3693 ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x5c ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x980 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x9e9 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x115 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x13e ./Core/Src/freertos.o
 .debug_macro   0x00000000       0xa5 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x174 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x287 ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x5f ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x236 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0xb5b ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x38b ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x176 ./Core/Src/freertos.o
 .debug_macro   0x00000000       0xf9 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x12c ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x21e ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x2e ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x127 ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x7e ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x89 ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x8ed ./Core/Src/freertos.o
 .debug_macro   0x00000000       0x4d ./Core/Src/freertos.o
 .debug_macro   0x00000000      0x12d ./Core/Src/freertos.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/gpio.o
 .text          0x00000000        0x0 ./Core/Src/gpio.o
 .data          0x00000000        0x0 ./Core/Src/gpio.o
 .bss           0x00000000        0x0 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x29b ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x2e ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x2f ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x22 ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x8e ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x51 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x103 ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x6a ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x1df ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x1c ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x22 ./Core/Src/gpio.o
 .debug_macro   0x00000000       0xfb ./Core/Src/gpio.o
 .debug_macro   0x00000000     0x1011 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x11f ./Core/Src/gpio.o
 .debug_macro   0x00000000    0x15ea5 ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x6d ./Core/Src/gpio.o
 .debug_macro   0x00000000     0x3693 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x190 ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x5c ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x980 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x9e9 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x115 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x13e ./Core/Src/gpio.o
 .debug_macro   0x00000000       0xa5 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x174 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x287 ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x5f ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x236 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0xb5b ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x38b ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x176 ./Core/Src/gpio.o
 .debug_macro   0x00000000       0xf9 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x12c ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x21e ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x2e ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x127 ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x7e ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x89 ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x8ed ./Core/Src/gpio.o
 .debug_macro   0x00000000       0x4d ./Core/Src/gpio.o
 .debug_macro   0x00000000      0x12d ./Core/Src/gpio.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/main.o
 .text          0x00000000        0x0 ./Core/Src/main.o
 .data          0x00000000        0x0 ./Core/Src/main.o
 .bss           0x00000000        0x0 ./Core/Src/main.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/main.o
 .debug_macro   0x00000000      0x29b ./Core/Src/main.o
 .debug_macro   0x00000000       0x2e ./Core/Src/main.o
 .debug_macro   0x00000000       0x2f ./Core/Src/main.o
 .debug_macro   0x00000000       0x22 ./Core/Src/main.o
 .debug_macro   0x00000000       0x8e ./Core/Src/main.o
 .debug_macro   0x00000000       0x51 ./Core/Src/main.o
 .debug_macro   0x00000000      0x103 ./Core/Src/main.o
 .debug_macro   0x00000000       0x6a ./Core/Src/main.o
 .debug_macro   0x00000000      0x1df ./Core/Src/main.o
 .debug_macro   0x00000000       0x1c ./Core/Src/main.o
 .debug_macro   0x00000000       0x22 ./Core/Src/main.o
 .debug_macro   0x00000000       0xfb ./Core/Src/main.o
 .debug_macro   0x00000000     0x1011 ./Core/Src/main.o
 .debug_macro   0x00000000      0x11f ./Core/Src/main.o
 .debug_macro   0x00000000    0x15ea5 ./Core/Src/main.o
 .debug_macro   0x00000000       0x6d ./Core/Src/main.o
 .debug_macro   0x00000000     0x3693 ./Core/Src/main.o
 .debug_macro   0x00000000      0x190 ./Core/Src/main.o
 .debug_macro   0x00000000       0x5c ./Core/Src/main.o
 .debug_macro   0x00000000      0x980 ./Core/Src/main.o
 .debug_macro   0x00000000      0x9e9 ./Core/Src/main.o
 .debug_macro   0x00000000      0x115 ./Core/Src/main.o
 .debug_macro   0x00000000      0x13e ./Core/Src/main.o
 .debug_macro   0x00000000       0xa5 ./Core/Src/main.o
 .debug_macro   0x00000000      0x174 ./Core/Src/main.o
 .debug_macro   0x00000000      0x287 ./Core/Src/main.o
 .debug_macro   0x00000000       0x5f ./Core/Src/main.o
 .debug_macro   0x00000000      0x236 ./Core/Src/main.o
 .debug_macro   0x00000000      0xb5b ./Core/Src/main.o
 .debug_macro   0x00000000      0x38b ./Core/Src/main.o
 .debug_macro   0x00000000      0x176 ./Core/Src/main.o
 .debug_macro   0x00000000       0xf9 ./Core/Src/main.o
 .debug_macro   0x00000000      0x12c ./Core/Src/main.o
 .debug_macro   0x00000000      0x21e ./Core/Src/main.o
 .debug_macro   0x00000000       0x2e ./Core/Src/main.o
 .debug_macro   0x00000000      0x127 ./Core/Src/main.o
 .debug_macro   0x00000000       0x7e ./Core/Src/main.o
 .debug_macro   0x00000000       0x89 ./Core/Src/main.o
 .debug_macro   0x00000000      0x8ed ./Core/Src/main.o
 .debug_macro   0x00000000       0x4d ./Core/Src/main.o
 .debug_macro   0x00000000      0x12d ./Core/Src/main.o
 .debug_macro   0x00000000      0x15a ./Core/Src/main.o
 .debug_macro   0x00000000       0xc9 ./Core/Src/main.o
 .debug_macro   0x00000000       0x1c ./Core/Src/main.o
 .debug_macro   0x00000000       0x26 ./Core/Src/main.o
 .debug_macro   0x00000000      0x4c5 ./Core/Src/main.o
 .debug_macro   0x00000000       0xb5 ./Core/Src/main.o
 .debug_macro   0x00000000       0xaa ./Core/Src/main.o
 .debug_macro   0x00000000       0x74 ./Core/Src/main.o
 .debug_macro   0x00000000       0xdd ./Core/Src/main.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_hal_msp.o
 .text          0x00000000        0x0 ./Core/Src/stm32f4xx_hal_msp.o
 .data          0x00000000        0x0 ./Core/Src/stm32f4xx_hal_msp.o
 .bss           0x00000000        0x0 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x29b ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x2e ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x2f ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x22 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x8e ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x51 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x103 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x6a ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x1df ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x1c ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x22 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0xfb ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000     0x1011 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x11f ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000    0x15ea5 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x6d ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000     0x3693 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x190 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x5c ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x980 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x9e9 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x115 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x13e ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0xa5 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x174 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x287 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x5f ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x236 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0xb5b ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x38b ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x176 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0xf9 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x12c ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x21e ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x2e ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x127 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x7e ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x89 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x8ed ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000       0x4d ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00000000      0x12d ./Core/Src/stm32f4xx_hal_msp.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/stm32f4xx_it.o
 .text          0x00000000        0x0 ./Core/Src/stm32f4xx_it.o
 .data          0x00000000        0x0 ./Core/Src/stm32f4xx_it.o
 .bss           0x00000000        0x0 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x29b ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x2e ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x2f ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x22 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x8e ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x51 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x103 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x6a ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x1df ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x1c ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x22 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0xfb ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000     0x1011 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x11f ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000    0x15ea5 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x6d ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000     0x3693 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x190 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x5c ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x980 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x9e9 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x115 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x13e ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0xa5 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x174 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x287 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x5f ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x236 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0xb5b ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x38b ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x176 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0xf9 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x12c ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x21e ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x2e ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x127 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x7e ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x89 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x8ed ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x4d ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x12d ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x169 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x15a ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0xc9 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x1c ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0x26 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000      0x4c5 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0xb5 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00000000       0xaa ./Core/Src/stm32f4xx_it.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/syscalls.o
 .text          0x00000000        0x0 ./Core/Src/syscalls.o
 .data          0x00000000        0x0 ./Core/Src/syscalls.o
 .bss           0x00000000        0x0 ./Core/Src/syscalls.o
 .text.initialise_monitor_handles
                0x00000000        0x4 ./Core/Src/syscalls.o
 .text._getpid  0x00000000        0x4 ./Core/Src/syscalls.o
 .text._kill    0x00000000       0x10 ./Core/Src/syscalls.o
 .text._exit    0x00000000        0xc ./Core/Src/syscalls.o
 .text._read    0x00000000       0x1c ./Core/Src/syscalls.o
 .text._write   0x00000000       0x1c ./Core/Src/syscalls.o
 .text._close   0x00000000        0x8 ./Core/Src/syscalls.o
 .text._fstat   0x00000000        0xc ./Core/Src/syscalls.o
 .text._isatty  0x00000000        0x4 ./Core/Src/syscalls.o
 .text._lseek   0x00000000        0x4 ./Core/Src/syscalls.o
 .text._open    0x00000000        0xc ./Core/Src/syscalls.o
 .text._wait    0x00000000       0x10 ./Core/Src/syscalls.o
 .text._unlink  0x00000000       0x10 ./Core/Src/syscalls.o
 .text._times   0x00000000        0x8 ./Core/Src/syscalls.o
 .text._stat    0x00000000        0xc ./Core/Src/syscalls.o
 .text._link    0x00000000       0x10 ./Core/Src/syscalls.o
 .text._fork    0x00000000       0x10 ./Core/Src/syscalls.o
 .text._execve  0x00000000       0x10 ./Core/Src/syscalls.o
 .data.environ  0x00000000        0x4 ./Core/Src/syscalls.o
 .bss.__env     0x00000000        0x4 ./Core/Src/syscalls.o
 .debug_info    0x00000000      0x7a5 ./Core/Src/syscalls.o
 .debug_abbrev  0x00000000      0x206 ./Core/Src/syscalls.o
 .debug_loclists
                0x00000000      0x2cd ./Core/Src/syscalls.o
 .debug_aranges
                0x00000000       0xa8 ./Core/Src/syscalls.o
 .debug_rnglists
                0x00000000       0x85 ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x274 ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x22 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x5b ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x2a ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x94 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x43 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x34 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x57 ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x190 ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x370 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x16 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x4a ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x34 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x10 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x58 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x8e ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x1c ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x185 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x10 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x3c ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x10 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x10 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x10 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x6a ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x1c ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x52 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x22 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x10 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x52 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0xcf ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x1c ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x3d ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x35 ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x12c ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x16 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x16 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x29 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x10 ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x242 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x1c ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x10 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x10 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x16 ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x146 ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x103 ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x1df ./Core/Src/syscalls.o
 .debug_macro   0x00000000      0x18a ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0x16 ./Core/Src/syscalls.o
 .debug_macro   0x00000000       0xce ./Core/Src/syscalls.o
 .debug_line    0x00000000      0xa36 ./Core/Src/syscalls.o
 .debug_str     0x00000000     0x99b7 ./Core/Src/syscalls.o
 .comment       0x00000000       0x44 ./Core/Src/syscalls.o
 .debug_frame   0x00000000      0x190 ./Core/Src/syscalls.o
 .ARM.attributes
                0x00000000       0x34 ./Core/Src/syscalls.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/sysmem.o
 .text          0x00000000        0x0 ./Core/Src/sysmem.o
 .data          0x00000000        0x0 ./Core/Src/sysmem.o
 .bss           0x00000000        0x0 ./Core/Src/sysmem.o
 .text._sbrk    0x00000000       0x44 ./Core/Src/sysmem.o
 .bss.__sbrk_heap_end
                0x00000000        0x4 ./Core/Src/sysmem.o
 .debug_info    0x00000000      0x186 ./Core/Src/sysmem.o
 .debug_abbrev  0x00000000       0xdc ./Core/Src/sysmem.o
 .debug_loclists
                0x00000000       0x5a ./Core/Src/sysmem.o
 .debug_aranges
                0x00000000       0x20 ./Core/Src/sysmem.o
 .debug_rnglists
                0x00000000       0x13 ./Core/Src/sysmem.o
 .debug_macro   0x00000000      0x112 ./Core/Src/sysmem.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x10 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x22 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x5b ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x2a ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x94 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x43 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x34 ./Core/Src/sysmem.o
 .debug_macro   0x00000000      0x190 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x57 ./Core/Src/sysmem.o
 .debug_macro   0x00000000      0x370 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x16 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x4a ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x34 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x10 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x58 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x8e ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x1c ./Core/Src/sysmem.o
 .debug_macro   0x00000000      0x185 ./Core/Src/sysmem.o
 .debug_macro   0x00000000      0x23c ./Core/Src/sysmem.o
 .debug_macro   0x00000000      0x103 ./Core/Src/sysmem.o
 .debug_macro   0x00000000       0x6a ./Core/Src/sysmem.o
 .debug_macro   0x00000000      0x1df ./Core/Src/sysmem.o
 .debug_line    0x00000000      0x69b ./Core/Src/sysmem.o
 .debug_str     0x00000000     0x7755 ./Core/Src/sysmem.o
 .comment       0x00000000       0x44 ./Core/Src/sysmem.o
 .debug_frame   0x00000000       0x28 ./Core/Src/sysmem.o
 .ARM.attributes
                0x00000000       0x34 ./Core/Src/sysmem.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/system_stm32f4xx.o
 .text          0x00000000        0x0 ./Core/Src/system_stm32f4xx.o
 .data          0x00000000        0x0 ./Core/Src/system_stm32f4xx.o
 .bss           0x00000000        0x0 ./Core/Src/system_stm32f4xx.o
 .text.SystemCoreClockUpdate
                0x00000000       0x70 ./Core/Src/system_stm32f4xx.o
 .rodata.APBPrescTable
                0x00000000        0x8 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x2e ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x2f ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x22 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x8e ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x51 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x103 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x6a ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x1df ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x1c ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x22 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0xfb ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000     0x1011 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x11f ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000    0x15ea5 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x6d ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x29b ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000     0x3693 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x190 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x5c ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x980 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x9e9 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x115 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x13e ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0xa5 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x174 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x287 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x5f ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x236 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0xb5b ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x38b ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x176 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0xf9 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x12c ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x21e ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x2e ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x127 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x7e ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x89 ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x8ed ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000       0x4d ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00000000      0x12d ./Core/Src/system_stm32f4xx.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .group         0x00000000        0xc ./Core/Src/tim.o
 .text          0x00000000        0x0 ./Core/Src/tim.o
 .data          0x00000000        0x0 ./Core/Src/tim.o
 .bss           0x00000000        0x0 ./Core/Src/tim.o
 .text.HAL_TIM_Base_MspDeInit
                0x00000000       0x20 ./Core/Src/tim.o
 .debug_macro   0x00000000      0xad8 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x29b ./Core/Src/tim.o
 .debug_macro   0x00000000       0x2e ./Core/Src/tim.o
 .debug_macro   0x00000000       0x2f ./Core/Src/tim.o
 .debug_macro   0x00000000       0x22 ./Core/Src/tim.o
 .debug_macro   0x00000000       0x8e ./Core/Src/tim.o
 .debug_macro   0x00000000       0x51 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x103 ./Core/Src/tim.o
 .debug_macro   0x00000000       0x6a ./Core/Src/tim.o
 .debug_macro   0x00000000      0x1df ./Core/Src/tim.o
 .debug_macro   0x00000000       0x1c ./Core/Src/tim.o
 .debug_macro   0x00000000       0x22 ./Core/Src/tim.o
 .debug_macro   0x00000000       0xfb ./Core/Src/tim.o
 .debug_macro   0x00000000     0x1011 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x11f ./Core/Src/tim.o
 .debug_macro   0x00000000    0x15ea5 ./Core/Src/tim.o
 .debug_macro   0x00000000       0x6d ./Core/Src/tim.o
 .debug_macro   0x00000000     0x3693 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x190 ./Core/Src/tim.o
 .debug_macro   0x00000000       0x5c ./Core/Src/tim.o
 .debug_macro   0x00000000      0x980 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x9e9 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x115 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x13e ./Core/Src/tim.o
 .debug_macro   0x00000000       0xa5 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x174 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x287 ./Core/Src/tim.o
 .debug_macro   0x00000000       0x5f ./Core/Src/tim.o
 .debug_macro   0x00000000      0x236 ./Core/Src/tim.o
 .debug_macro   0x00000000      0xb5b ./Core/Src/tim.o
 .debug_macro   0x00000000      0x38b ./Core/Src/tim.o
 .debug_macro   0x00000000      0x176 ./Core/Src/tim.o
 .debug_macro   0x00000000       0xf9 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x12c ./Core/Src/tim.o
 .debug_macro   0x00000000      0x21e ./Core/Src/tim.o
 .debug_macro   0x00000000       0x2e ./Core/Src/tim.o
 .debug_macro   0x00000000      0x127 ./Core/Src/tim.o
 .debug_macro   0x00000000       0x7e ./Core/Src/tim.o
 .debug_macro   0x00000000       0x89 ./Core/Src/tim.o
 .debug_macro   0x00000000      0x8ed ./Core/Src/tim.o
 .debug_macro   0x00000000       0x4d ./Core/Src/tim.o
 .debug_macro   0x00000000      0x12d ./Core/Src/tim.o
 .text          0x00000000       0x14 ./Core/Startup/startup_stm32f407zetx.o
 .data          0x00000000        0x0 ./Core/Startup/startup_stm32f407zetx.o
 .bss           0x00000000        0x0 ./Core/Startup/startup_stm32f407zetx.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_MspInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_MspDeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_DeInit
                0x00000000       0x3c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_GetTickPrio
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_SetTickFreq
                0x00000000       0x28 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_GetTickFreq
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_Delay
                0x00000000       0x24 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_SuspendTick
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_ResumeTick
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_GetHalVersion
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_GetREVID
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_GetDEVID
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_DBGMCU_EnableDBGSleepMode
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_DBGMCU_DisableDBGSleepMode
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_DBGMCU_EnableDBGStopMode
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_DBGMCU_DisableDBGStopMode
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_DBGMCU_EnableDBGStandbyMode
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_DBGMCU_DisableDBGStandbyMode
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_EnableCompensationCell
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_DisableCompensationCell
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_GetUIDw0
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_GetUIDw1
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .text.HAL_GetUIDw2
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_MspInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_MspDeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_DeInit
                0x00000000       0x3c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_Start
                0x00000000      0x108 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_Stop
                0x00000000       0x38 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_PollForConversion
                0x00000000       0xb0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_PollForEvent
                0x00000000       0x7c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_Start_IT
                0x00000000      0x114 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_Stop_IT
                0x00000000       0x48 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_Start_DMA
                0x00000000      0x15c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_Stop_DMA
                0x00000000       0x7c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_GetValue
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_ConvCpltCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_ConvHalfCpltCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.ADC_DMAHalfConvCplt
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_LevelOutOfWindowCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_ErrorCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_IRQHandler
                0x00000000      0x120 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.ADC_DMAError
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.ADC_DMAConvCplt
                0x00000000       0x6c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_AnalogWDGConfig
                0x00000000       0x68 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_GetState
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .text.HAL_ADC_GetError
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.ADC_MultiModeDMAError
                0x00000000       0x14 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.ADC_MultiModeDMAHalfConvCplt
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.ADC_MultiModeDMAConvCplt
                0x00000000       0x58 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedStart
                0x00000000       0xe4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedStart_IT
                0x00000000       0xec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedStop
                0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedPollForConversion
                0x00000000       0x94 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedStop_IT
                0x00000000       0x64 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedGetValue
                0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_MultiModeStart_DMA
                0x00000000      0x118 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_MultiModeStop_DMA
                0x00000000       0x68 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_MultiModeGetValue
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedConvCpltCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_InjectedConfigChannel
                0x00000000      0x198 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .text.HAL_ADCEx_MultiModeConfigChannel
                0x00000000       0x58 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_info    0x00000000      0xc82 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_abbrev  0x00000000      0x2e1 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_loclists
                0x00000000      0x764 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_aranges
                0x00000000       0x90 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_rnglists
                0x00000000       0x6c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x1ec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_line    0x00000000     0x1234 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_str     0x00000000    0xced52 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .debug_frame   0x00000000      0x1a8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_NVIC_DisableIRQ
                0x00000000       0x24 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_NVIC_SystemReset
                0x00000000       0x24 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_MPU_Disable
                0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_MPU_Enable
                0x00000000       0x20 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_MPU_EnableRegion
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_MPU_DisableRegion
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_MPU_ConfigRegion
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_CORTEX_ClearEvent
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_NVIC_GetPriorityGrouping
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_NVIC_GetPriority
                0x00000000       0x68 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_NVIC_SetPendingIRQ
                0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_NVIC_GetPendingIRQ
                0x00000000       0x24 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_NVIC_ClearPendingIRQ
                0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_NVIC_GetActive
                0x00000000       0x24 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_SYSTICK_CLKSourceConfig
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_SYSTICK_Callback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .text.HAL_SYSTICK_IRQHandler
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_DeInit
                0x00000000       0x7c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_Start
                0x00000000       0x58 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_Start_IT
                0x00000000       0x78 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_Abort
                0x00000000       0x94 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_Abort_IT
                0x00000000       0x24 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_PollForTransfer
                0x00000000      0x13c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_RegisterCallback
                0x00000000       0x50 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_UnRegisterCallback
                0x00000000       0x70 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_GetState
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .text.HAL_DMA_GetError
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .text.HAL_DMAEx_MultiBufferStart
                0x00000000       0x64 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .text.HAL_DMAEx_MultiBufferStart_IT
                0x00000000      0x330 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .text.HAL_DMAEx_ChangeMemory
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_info    0x00000000      0x585 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_abbrev  0x00000000      0x1b9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_loclists
                0x00000000      0x6a0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_aranges
                0x00000000       0x30 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_rnglists
                0x00000000       0x38 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x1ec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_line    0x00000000      0xe1a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_str     0x00000000    0xce8e5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .debug_frame   0x00000000       0x84 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text.HAL_EXTI_SetConfigLine
                0x00000000       0xa0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text.HAL_EXTI_GetConfigLine
                0x00000000       0x94 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text.HAL_EXTI_ClearConfigLine
                0x00000000       0x70 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text.HAL_EXTI_RegisterCallback
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text.HAL_EXTI_GetHandle
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text.HAL_EXTI_IRQHandler
                0x00000000       0x2c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text.HAL_EXTI_GetPending
                0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text.HAL_EXTI_ClearPending
                0x00000000       0x14 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .text.HAL_EXTI_GenerateSWI
                0x00000000       0x14 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_info    0x00000000      0x570 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_abbrev  0x00000000      0x1f3 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_loclists
                0x00000000      0x37a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_aranges
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_rnglists
                0x00000000       0x45 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x1ec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_line    0x00000000      0xb5d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_str     0x00000000    0xce77d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .debug_frame   0x00000000       0xd4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.FLASH_SetErrorCode
                0x00000000       0x78 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_Program_IT
                0x00000000       0xb8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_EndOfOperationCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_OperationErrorCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_IRQHandler
                0x00000000       0xc0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_Unlock
                0x00000000       0x28 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_Lock
                0x00000000       0x14 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_OB_Unlock
                0x00000000       0x24 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_OB_Lock
                0x00000000       0x14 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_OB_Launch
                0x00000000       0x58 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_GetError
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.FLASH_WaitForLastOperation
                0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .text.HAL_FLASH_Program
                0x00000000       0xd0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .bss.pFlash    0x00000000       0x20 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_info    0x00000000      0x809 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_abbrev  0x00000000      0x377 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_loclists
                0x00000000      0x30d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_aranges
                0x00000000       0x80 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_rnglists
                0x00000000       0xd3 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x1f2 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_line    0x00000000      0xeb3 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_str     0x00000000    0xce901 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .debug_frame   0x00000000      0x138 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .text.HAL_FLASHEx_OBProgram
                0x00000000       0xc0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .text.HAL_FLASHEx_OBGetConfig
                0x00000000       0x38 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .text.FLASH_Erase_Sector
                0x00000000       0x3c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .text.HAL_FLASHEx_Erase_IT
                0x00000000       0x78 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .text.FLASH_FlushCaches
                0x00000000       0x58 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .text.HAL_FLASHEx_Erase
                0x00000000       0xc4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_info    0x00000000      0x8cd ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_abbrev  0x00000000      0x33d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_loclists
                0x00000000      0x2d7 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_aranges
                0x00000000       0x48 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_rnglists
                0x00000000       0xa2 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x1f2 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_line    0x00000000      0xc6c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_str     0x00000000    0xce951 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .debug_frame   0x00000000       0x98 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_info    0x00000000       0x70 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_abbrev  0x00000000       0x28 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_aranges
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x1ec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_line    0x00000000      0x761 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .debug_str     0x00000000    0xce556 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .text.HAL_GPIO_DeInit
                0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .text.HAL_GPIO_ReadPin
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .text.HAL_GPIO_TogglePin
                0x00000000       0x14 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .text.HAL_GPIO_LockPin
                0x00000000       0x2c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .text.HAL_GPIO_EXTI_Callback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .text.HAL_GPIO_EXTI_IRQHandler
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_DeInit
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_EnableBkUpAccess
                0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_DisableBkUpAccess
                0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_ConfigPVD
                0x00000000       0x78 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_EnablePVD
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_DisablePVD
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_EnableWakeUpPin
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_DisableWakeUpPin
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_EnterSLEEPMode
                0x00000000       0x24 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_EnterSTOPMode
                0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_EnterSTANDBYMode
                0x00000000       0x20 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_PVDCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_PVD_IRQHandler
                0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_EnableSleepOnExit
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_DisableSleepOnExit
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_EnableSEVOnPend
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .text.HAL_PWR_DisableSEVOnPend
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_info    0x00000000      0x705 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_abbrev  0x00000000      0x1a0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_loclists
                0x00000000       0x29 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_aranges
                0x00000000       0xa0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_rnglists
                0x00000000       0x73 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x204 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_line    0x00000000      0x9ec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_str     0x00000000    0xce947 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .debug_frame   0x00000000      0x150 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableBkUpReg
                0x00000000       0x38 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableBkUpReg
                0x00000000       0x38 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .text.HAL_PWREx_EnableFlashPowerDown
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .text.HAL_PWREx_DisableFlashPowerDown
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .text.HAL_PWREx_GetVoltageRange
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .text.HAL_PWREx_ControlVoltageScaling
                0x00000000       0x64 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_info    0x00000000      0x3e4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_abbrev  0x00000000      0x198 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_loclists
                0x00000000       0x7d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_aranges
                0x00000000       0x48 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_rnglists
                0x00000000       0x4c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x204 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_line    0x00000000      0x8d8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_str     0x00000000    0xce810 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .debug_frame   0x00000000       0xa0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_DeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_MCOConfig
                0x00000000       0xa0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_EnableCSS
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_DisableCSS
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_GetHCLKFreq
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_GetPCLK1Freq
                0x00000000       0x20 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_GetPCLK2Freq
                0x00000000       0x20 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_GetOscConfig
                0x00000000       0x8c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_GetClockConfig
                0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_CSSCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .text.HAL_RCC_NMI_IRQHandler
                0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_PeriphCLKConfig
                0x00000000      0x13c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetPeriphCLKConfig
                0x00000000       0x30 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_GetPeriphCLKFreq
                0x00000000       0x54 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_EnablePLLI2S
                0x00000000       0x64 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .text.HAL_RCCEx_DisablePLLI2S
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .text.HAL_RCC_DeInit
                0x00000000      0x124 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_info    0x00000000      0x651 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_abbrev  0x00000000      0x1c4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_loclists
                0x00000000      0x267 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_aranges
                0x00000000       0x48 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_rnglists
                0x00000000       0x3f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x1ec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_line    0x00000000      0xca9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_str     0x00000000    0xce863 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .debug_frame   0x00000000       0xb0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_OC1_SetConfig
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_OC3_SetConfig
                0x00000000       0x64 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_OC4_SetConfig
                0x00000000       0x50 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_SlaveTimer_SetConfig.constprop.0
                0x00000000       0xd4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_MspInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_MspDeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_DeInit
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_Start
                0x00000000       0x6c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_Stop
                0x00000000       0x28 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_Start_IT
                0x00000000       0x74 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_Stop_IT
                0x00000000       0x30 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_Start_DMA
                0x00000000       0xb0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_Stop_DMA
                0x00000000       0x3c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_MspInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_MspDeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_DeInit
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_Start
                0x00000000       0xd0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_Stop
                0x00000000       0xa0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_Start_IT
                0x00000000       0xf4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_Stop_IT
                0x00000000       0xd8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_Start_DMA
                0x00000000      0x1bc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_Stop_DMA
                0x00000000       0xe8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_MspInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_MspDeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_DeInit
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_Start
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_Stop
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_Start_IT
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_Stop_IT
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_Start_DMA
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_Stop_DMA
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_MspInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_MspDeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_DeInit
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_Start
                0x00000000      0x110 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_Stop
                0x00000000       0x80 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_Start_IT
                0x00000000      0x11c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_Stop_IT
                0x00000000      0x118 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_Start_DMA
                0x00000000      0x23c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_Stop_DMA
                0x00000000      0x114 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_MspInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_MspDeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_DeInit
                0x00000000       0x50 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Start
                0x00000000       0x94 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Stop
                0x00000000       0x80 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Start_IT
                0x00000000       0xa4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Stop_IT
                0x00000000       0x90 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_MspInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_MspDeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_DeInit
                0x00000000       0x50 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Start
                0x00000000       0xc4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Stop
                0x00000000       0xc0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Start_IT
                0x00000000       0xf0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Stop_IT
                0x00000000       0xd8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Start_DMA
                0x00000000      0x1c4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Stop_DMA
                0x00000000      0x100 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_MultiWriteStart
                0x00000000       0xec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_WriteStart
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_WriteStop
                0x00000000       0x80 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_MultiReadStart
                0x00000000       0xec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_ReadStart
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_DMABurst_ReadStop
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_GenerateEvent
                0x00000000       0x2c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_ConfigOCrefClear
                0x00000000       0xe0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_ConfigTI1Input
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_SlaveConfigSynchro
                0x00000000       0x4c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_SlaveConfigSynchro_IT
                0x00000000       0x4c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_ReadCapturedValue
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PeriodElapsedCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_DMAPeriodElapsedCplt
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PeriodElapsedHalfCpltCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_DMAPeriodElapsedHalfCplt
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_DelayElapsedCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_CaptureCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_DMACaptureCplt
                0x00000000       0x94 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_CaptureHalfCpltCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_DMACaptureHalfCplt
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_PulseFinishedCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_DMADelayPulseCplt
                0x00000000       0x84 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_DMADelayPulseHalfCplt
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_TriggerCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IRQHandler
                0x00000000      0x138 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_DMATriggerCplt
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_TriggerHalfCpltCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_DMATriggerHalfCplt
                0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_ErrorCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_DMAError
                0x00000000       0x84 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Base_GetState
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_GetState
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_GetState
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_GetState
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_GetState
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_GetState
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_GetActiveChannel
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_GetChannelState
                0x00000000       0x28 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_DMABurstState
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_Init
                0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_Init
                0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_Init
                0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_Init
                0x00000000       0x60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_Encoder_Init
                0x00000000       0xac ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_OC2_SetConfig
                0x00000000       0x64 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OC_ConfigChannel
                0x00000000       0x58 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_PWM_ConfigChannel
                0x00000000       0xc0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_TI1_SetConfig
                0x00000000       0x78 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_IC_ConfigChannel
                0x00000000      0x128 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.HAL_TIM_OnePulse_ConfigChannel
                0x00000000      0x108 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_ETR_SetConfig
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .text.TIM_CCxChannelCmd
                0x00000000       0x20 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.TIM_DMAErrorCCxN
                0x00000000       0x64 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.TIM_DMADelayPulseNCplt
                0x00000000       0x6c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_MspInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_Init
                0x00000000       0xc8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_MspDeInit
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_DeInit
                0x00000000       0x50 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_Start
                0x00000000       0xac ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_Stop
                0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_Start_IT
                0x00000000       0xb4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_Stop_IT
                0x00000000       0x4c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_Start_DMA
                0x00000000       0xdc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_Stop_DMA
                0x00000000       0x4c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OCN_Start
                0x00000000       0xc8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OCN_Stop
                0x00000000       0x8c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OCN_Start_IT
                0x00000000       0xe4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OCN_Stop_IT
                0x00000000       0xbc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OCN_Start_DMA
                0x00000000      0x188 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OCN_Stop_DMA
                0x00000000       0xb4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_PWMN_Start
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_PWMN_Stop
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_PWMN_Start_IT
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_PWMN_Stop_IT
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_PWMN_Start_DMA
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_PWMN_Stop_DMA
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OnePulseN_Start
                0x00000000       0x84 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OnePulseN_Stop
                0x00000000       0x7c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OnePulseN_Start_IT
                0x00000000       0x94 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_OnePulseN_Stop_IT
                0x00000000       0x8c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_ConfigCommutEvent
                0x00000000       0x6c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_ConfigCommutEvent_IT
                0x00000000       0x6c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_ConfigCommutEvent_DMA
                0x00000000       0x84 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_ConfigBreakDeadTime
                0x00000000       0x54 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_RemapConfig
                0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_CommutCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.TIMEx_DMACommutationCplt
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_CommutHalfCpltCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.TIMEx_DMACommutationHalfCplt
                0x00000000       0x10 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_BreakCallback
                0x00000000        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_HallSensor_GetState
                0x00000000        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .text.HAL_TIMEx_GetChannelNState
                0x00000000       0x28 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x29b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x2f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x8e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x51 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x103 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x6a ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x1df ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0xfb ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000     0x1011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x11f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000    0x15ea5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x6d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000     0x3693 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x190 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x980 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x9e9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x115 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x13e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0xa5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x174 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x287 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x236 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0xb5b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x38b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x176 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0xf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x12c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x21e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x127 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x7e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x89 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x8ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000       0x4d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00000000      0x12d ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .group         0x00000000        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .text          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .data          0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .bss           0x00000000        0x0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .debug_info    0x00000000       0x22 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .debug_abbrev  0x00000000       0x12 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .debug_aranges
                0x00000000       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .debug_macro   0x00000000       0x11 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .debug_macro   0x00000000      0xad8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .debug_line    0x00000000       0x57 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .debug_str     0x00000000     0x2e70 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .comment       0x00000000       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .ARM.attributes
                0x00000000       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.TimerCallback
                0x00000000       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .rodata.osKernelGetInfo.str1.4
                0x00000000       0x11 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osKernelGetInfo
                0x00000000       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osKernelGetState
                0x00000000       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osKernelLock
                0x00000000       0x2c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osKernelUnlock
                0x00000000       0x38 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osKernelRestoreLock
                0x00000000       0x4c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osKernelGetTickCount
                0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osKernelGetTickFreq
                0x00000000        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osKernelGetSysTimerCount
                0x00000000       0x48 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osKernelGetSysTimerFreq
                0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadGetName
                0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadGetId
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadGetState
                0x00000000       0x2c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadGetStackSpace
                0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadSetPriority
                0x00000000       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadGetPriority
                0x00000000       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadYield
                0x00000000       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadSuspend
                0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadResume
                0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadExit
                0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadTerminate
                0x00000000       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadGetCount
                0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadEnumerate
                0x00000000       0x74 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadFlagsSet
                0x00000000       0x78 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadFlagsClear
                0x00000000       0x5c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadFlagsGet
                0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osThreadFlagsWait
                0x00000000       0xa8 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osDelayUntil
                0x00000000       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osTimerNew
                0x00000000       0x7c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osTimerGetName
                0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osTimerStart
                0x00000000       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osTimerStop
                0x00000000       0x40 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osTimerIsRunning
                0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osTimerDelete
                0x00000000       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osEventFlagsNew
                0x00000000       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osEventFlagsSet
                0x00000000       0x5c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osEventFlagsClear
                0x00000000       0x40 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osEventFlagsGet
                0x00000000       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osEventFlagsWait
                0x00000000       0x5c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osEventFlagsDelete
                0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMutexNew
                0x00000000       0xa0 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMutexAcquire
                0x00000000       0x48 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMutexRelease
                0x00000000       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMutexGetOwner
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMutexDelete
                0x00000000       0x2c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osSemaphoreNew
                0x00000000       0xc0 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osSemaphoreAcquire
                0x00000000       0x64 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osSemaphoreRelease
                0x00000000       0x54 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osSemaphoreGetCount
                0x00000000       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osSemaphoreDelete
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMessageQueueNew
                0x00000000       0x70 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMessageQueuePut
                0x00000000       0x64 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMessageQueueGet
                0x00000000       0x64 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMessageQueueGetCapacity
                0x00000000        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMessageQueueGetMsgSize
                0x00000000        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMessageQueueGetCount
                0x00000000       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMessageQueueGetSpace
                0x00000000       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMessageQueueReset
                0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMessageQueueDelete
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMemoryPoolNew
                0x00000000      0x180 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMemoryPoolGetName
                0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMemoryPoolAlloc
                0x00000000       0xb8 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMemoryPoolFree
                0x00000000       0xc0 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMemoryPoolGetCapacity
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMemoryPoolGetBlockSize
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMemoryPoolGetCount
                0x00000000       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMemoryPoolGetSpace
                0x00000000       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .text.osMemoryPoolDelete
                0x00000000       0x6c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .rodata.CSWTCH.59
                0x00000000       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x5b ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x2a ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x94 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x43 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x190 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x57 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x370 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x4a ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x185 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x3c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x74 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0xaa ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x2e ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x2f ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000     0x1011 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x11f ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000    0x15ea5 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x6d ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x29b ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000     0x3693 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x980 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x9e9 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x115 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x13e ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0xa5 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x174 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x287 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x5f ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x236 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0xb5b ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x38b ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x176 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0xf9 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x12c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x21e ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x2e ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x127 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x7e ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x89 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x8ed ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000       0x4d ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00000000      0x12d ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_info    0x00000000       0x77 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_abbrev  0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_aranges
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0xe3 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000      0x190 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0x51 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0xaa ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_macro   0x00000000       0x43 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_line    0x00000000      0x52a ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .debug_str     0x00000000     0x8476 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .comment       0x00000000       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .ARM.attributes
                0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.vEventGroupClearBitsCallback
                0x00000000       0x48 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.xEventGroupCreateStatic
                0x00000000       0x4c ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.xEventGroupCreate
                0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.xEventGroupWaitBits
                0x00000000      0x130 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.xEventGroupClearBits
                0x00000000       0x48 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.xEventGroupClearBitsFromISR
                0x00000000       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.xEventGroupGetBitsFromISR
                0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.xEventGroupSetBits
                0x00000000       0x94 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.xEventGroupSync
                0x00000000       0xf0 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.vEventGroupSetBitsCallback
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.vEventGroupDelete
                0x00000000       0x50 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.xEventGroupSetBitsFromISR
                0x00000000       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.uxEventGroupGetNumber
                0x00000000        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .text.vEventGroupSetNumber
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_info    0x00000000     0x11bb ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_abbrev  0x00000000      0x3f6 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_loclists
                0x00000000      0xab9 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_aranges
                0x00000000       0x88 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_rnglists
                0x00000000       0xc4 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0x1e2 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x2a ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x5b ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x94 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x43 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0x11c ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x9b ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x57 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0x370 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x4a ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0x185 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x29 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0xaa ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_macro   0x00000000       0x91 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_line    0x00000000     0x118a ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_str     0x00000000     0xc082 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .comment       0x00000000       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .debug_frame   0x00000000      0x1ac ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .ARM.attributes
                0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x2a ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x5b ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x94 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x43 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000      0x11c ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x9b ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x57 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000      0x370 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x4a ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000      0x185 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x29 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueGenericCreate
                0x00000000       0x50 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueGetMutexHolder
                0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueGetMutexHolderFromISR
                0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueCreateCountingSemaphoreStatic
                0x00000000       0x48 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueCreateCountingSemaphore
                0x00000000       0x3c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueCreateMutexStatic
                0x00000000       0x6c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueGiveMutexRecursive
                0x00000000       0x40 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueCreateMutex
                0x00000000       0x3c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueGiveFromISR
                0x00000000       0xac ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueSemaphoreTake
                0x00000000      0x1ac ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueTakeMutexRecursive
                0x00000000       0x40 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueuePeek
                0x00000000      0x170 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueReceiveFromISR
                0x00000000       0xb4 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueuePeekFromISR
                0x00000000       0x8c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.uxQueueMessagesWaiting
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.uxQueueSpacesAvailable
                0x00000000       0x2c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.uxQueueMessagesWaitingFromISR
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.vQueueDelete
                0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.uxQueueGetQueueNumber
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.vQueueSetQueueNumber
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.ucQueueGetQueueType
                0x00000000        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueIsQueueEmptyFromISR
                0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueIsQueueFullFromISR
                0x00000000       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.pcQueueGetName
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.vQueueUnregisterQueue
                0x00000000       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x2a ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x5b ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x94 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x43 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000      0x11c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x9b ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x57 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000      0x370 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x4a ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000      0x185 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x29 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x3c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00000000       0xaa ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.prvReadBytesFromBuffer
                0x00000000       0x78 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.prvWriteBytesToBuffer.part.0
                0x00000000       0x74 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.prvWriteMessageToBuffer
                0x00000000       0x60 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferGenericCreate
                0x00000000       0xa8 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferGenericCreateStatic
                0x00000000       0xc8 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.vStreamBufferDelete
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferReset
                0x00000000       0x88 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferSetTriggerLevel
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferSpacesAvailable
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferBytesAvailable
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferSend
                0x00000000      0x138 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferSendFromISR
                0x00000000       0xac ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferReceive
                0x00000000      0x110 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferNextMessageLengthBytes
                0x00000000       0x64 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferReceiveFromISR
                0x00000000       0xc8 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferIsEmpty
                0x00000000       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferIsFull
                0x00000000       0x38 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferSendCompletedFromISR
                0x00000000       0x50 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.xStreamBufferReceiveCompletedFromISR
                0x00000000       0x50 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.uxStreamBufferGetStreamBufferNumber
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.vStreamBufferSetStreamBufferNumber
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .text.ucStreamBufferGetStreamBufferType
                0x00000000        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_info    0x00000000     0x2157 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_abbrev  0x00000000      0x46b ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_loclists
                0x00000000     0x160a ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_aranges
                0x00000000       0xc8 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_rnglists
                0x00000000      0x11c ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0x1e0 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x51 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x61 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x2a ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x43 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0x190 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0x370 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x4a ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0x185 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x3c ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0xaa ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_macro   0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_line    0x00000000     0x1de4 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_str     0x00000000     0xc154 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .comment       0x00000000       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .debug_frame   0x00000000      0x304 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .ARM.attributes
                0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskDelete
                0x00000000      0x104 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.eTaskGetState
                0x00000000       0x84 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.uxTaskPriorityGet
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.uxTaskPriorityGetFromISR
                0x00000000       0x38 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskPrioritySet
                0x00000000       0xcc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskResume
                0x00000000       0x9c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskResumeFromISR
                0x00000000       0xb4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskEndScheduler
                0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskGetTickCountFromISR
                0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.uxTaskGetNumberOfTasks
                0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.pcTaskGetName
                0x00000000       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskCatchUpTicks
                0x00000000       0x48 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskDelayUntil
                0x00000000       0xac ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskSuspend
                0x00000000       0xdc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskPlaceOnUnorderedEventList
                0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskRemoveFromUnorderedEventList
                0x00000000       0x88 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskSetTimeOutState
                0x00000000       0x38 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.uxTaskGetTaskNumber
                0x00000000        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskSetTaskNumber
                0x00000000        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskGetInfo
                0x00000000       0xb8 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.prvListTasksWithinSingleList.part.0
                0x00000000       0x50 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.uxTaskGetSystemState
                0x00000000      0x108 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.uxTaskGetStackHighWaterMark
                0x00000000       0x38 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskGetCurrentTaskHandle
                0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskPriorityInherit
                0x00000000       0x80 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskPriorityDisinheritAfterTimeout
                0x00000000       0xa0 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.uxTaskResetEventItemValue
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.pvTaskIncrementMutexHeldCount
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.ulTaskNotifyTake
                0x00000000       0x68 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskNotifyWait
                0x00000000       0x8c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskGenericNotify
                0x00000000       0xec ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskGenericNotifyFromISR
                0x00000000      0x11c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.vTaskNotifyGiveFromISR
                0x00000000       0xcc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskNotifyStateClear
                0x00000000       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.ulTaskNotifyValueClear
                0x00000000       0x2c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x2a ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x5b ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x94 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x43 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000      0x11c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x9b ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x57 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000      0x370 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x4a ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000      0x185 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x29 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x3c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0xaa ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00000000       0x91 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.xTimerCreate
                0x00000000       0x5c ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.xTimerCreateStatic
                0x00000000       0x98 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.xTimerGetTimerDaemonTaskHandle
                0x00000000       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.xTimerGetPeriod
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.vTimerSetReloadMode
                0x00000000       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.uxTimerGetReloadMode
                0x00000000       0x2c ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.xTimerGetExpiryTime
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.pcTimerGetName
                0x00000000       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.xTimerIsTimerActive
                0x00000000       0x2c ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.pvTimerGetTimerID
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.vTimerSetTimerID
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.xTimerPendFunctionCallFromISR
                0x00000000       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.xTimerPendFunctionCall
                0x00000000       0x40 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.uxTimerGetTimerNumber
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.vTimerSetTimerNumber
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x2a ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x5b ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x94 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x43 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000      0x11c ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x9b ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x57 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000      0x370 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x4a ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000      0x185 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x29 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0xaa ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00000000       0x87 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .text.vPortEndScheduler
                0x00000000       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000      0x190 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000       0x51 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00000000       0xaa ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .group         0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .text          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .data          0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .bss           0x00000000        0x0 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .text.xPortGetFreeHeapSize
                0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .text.xPortGetMinimumEverFreeHeapSize
                0x00000000        0xc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .text.vPortInitialiseBlocks
                0x00000000        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .text.vPortGetHeapStats
                0x00000000       0x84 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000      0xad8 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x2a ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x5b ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x94 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x43 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000      0x11c ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x9b ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x57 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000      0x370 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x4a ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x58 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x8e ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000      0x185 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x29 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000      0x103 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x6a ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000      0x1df ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000      0x169 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000      0x15a ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0xc9 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0x26 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000      0x4c5 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0xb5 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_macro   0x00000000       0xaa ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .text.exit     0x00000000       0x24 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .debug_frame   0x00000000       0x28 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.std      0x00000000       0x6c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.stdio_exit_handler
                0x00000000       0x18 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.cleanup_stdio
                0x00000000       0x40 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock
                0x00000000       0x18 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock
                0x00000000       0x18 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.global_stdio_init.part.0
                0x00000000       0x3c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sfp_lock_acquire
                0x00000000        0xc D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sfp_lock_release
                0x00000000        0xc D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sfp    0x00000000       0xa4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sinit  0x00000000       0x30 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock_all
                0x00000000       0x1c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock_all
                0x00000000       0x1c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .data.__sglue  0x00000000        0xc D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .bss.__sf      0x00000000      0x138 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .bss.__stdio_exit_handler
                0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .debug_frame   0x00000000      0x144 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .text._fwalk_sglue
                0x00000000       0x3c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .debug_frame   0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__sread  0x00000000       0x22 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__seofread
                0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__swrite
                0x00000000       0x38 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__sseek  0x00000000       0x24 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__sclose
                0x00000000        0x8 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .debug_frame   0x00000000       0x88 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .text._close_r
                0x00000000       0x20 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .debug_frame   0x00000000       0x2c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xbc D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .bss.errno     0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .debug_frame   0x00000000       0x38 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .data._impure_ptr
                0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .data._impure_data
                0x00000000       0x4c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .text._lseek_r
                0x00000000       0x24 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .debug_frame   0x00000000       0x2c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .text._read_r  0x00000000       0x24 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .debug_frame   0x00000000       0x2c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .text._write_r
                0x00000000       0x24 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .debug_frame   0x00000000       0x2c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-errno.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-errno.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-errno.o)
 .text.__errno  0x00000000        0xc D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-errno.o)
 .debug_frame   0x00000000       0x20 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-errno.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-errno.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x2 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_init_recursive
                0x00000000        0x2 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x2 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x2 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x2 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_acquire_recursive
                0x00000000        0x2 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x2 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_release_recursive
                0x00000000        0x2 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___arc4random_mutex
                0x00000000        0x1 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___dd_hash_mutex
                0x00000000        0x1 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___tz_mutex
                0x00000000        0x1 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___env_recursive_mutex
                0x00000000        0x1 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___malloc_recursive_mutex
                0x00000000        0x1 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___at_quick_exit_mutex
                0x00000000        0x1 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___atexit_recursive_mutex
                0x00000000        0x1 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___sfp_recursive_mutex
                0x00000000        0x1 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .debug_frame   0x00000000       0xb0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memcpy-stub.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memcpy-stub.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memcpy-stub.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .text._free_r  0x00000000       0x94 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .debug_frame   0x00000000       0x38 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .text.sbrk_aligned
                0x00000000       0x44 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .text._malloc_r
                0x00000000      0x100 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .bss.__malloc_sbrk_start
                0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .bss.__malloc_free_list
                0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .debug_frame   0x00000000       0x50 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .text.__malloc_lock
                0x00000000        0xc D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .text.__malloc_unlock
                0x00000000        0xc D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .debug_frame   0x00000000       0x30 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text.__sflush_r
                0x00000000      0x108 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text._fflush_r
                0x00000000       0x50 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text.fflush   0x00000000       0x28 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .debug_frame   0x00000000       0x5c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .text._sbrk_r  0x00000000       0x20 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .debug_frame   0x00000000       0x2c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .text          0x00000000       0xa0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .debug_frame   0x00000000       0x44 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .ARM.attributes
                0x00000000       0x22 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_udivmoddi4.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_udivmoddi4.o)
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_dvmd_tls.o)
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_dvmd_tls.o)
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtend.o
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtend.o
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtend.o
 .rodata        0x00000000       0x24 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtend.o
 .eh_frame      0x00000000        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtend.o
 .ARM.attributes
                0x00000000       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtend.o
 .text          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtn.o
 .data          0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtn.o
 .bss           0x00000000        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtn.o

Memory Configuration

Name             Origin             Length             Attributes
CCMRAM           0x10000000         0x00010000         xrw
RAM              0x20000000         0x00020000         xrw
FLASH            0x08000000         0x00080000         xr
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crti.o
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard/crt0.o
LOAD ./Core/Src/adc.o
LOAD ./Core/Src/dma.o
LOAD ./Core/Src/freertos.o
LOAD ./Core/Src/gpio.o
LOAD ./Core/Src/main.o
LOAD ./Core/Src/stm32f4xx_hal_msp.o
LOAD ./Core/Src/stm32f4xx_it.o
LOAD ./Core/Src/syscalls.o
LOAD ./Core/Src/sysmem.o
LOAD ./Core/Src/system_stm32f4xx.o
LOAD ./Core/Src/tim.o
LOAD ./Core/Startup/startup_stm32f407zetx.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
LOAD ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/croutine.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/event_groups.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/list.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
LOAD ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
START GROUP
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libm.a
END GROUP
START GROUP
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a
END GROUP
START GROUP
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libnosys.a
END GROUP
START GROUP
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libnosys.a
END GROUP
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtend.o
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtn.o
                0x20020000                        _estack = (ORIGIN (RAM) + LENGTH (RAM))
                0x00000200                        _Min_Heap_Size = 0x200
                0x00000400                        _Min_Stack_Size = 0x400

.isr_vector     0x08000000      0x188
                0x08000000                        . = ALIGN (0x4)
 *(.isr_vector)
 .isr_vector    0x08000000      0x188 ./Core/Startup/startup_stm32f407zetx.o
                0x08000000                g_pfnVectors
                0x08000188                        . = ALIGN (0x4)

.text           0x08000190     0x37ac
                0x08000190                        . = ALIGN (0x4)
 *(.text)
 .text          0x08000190       0x40 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text          0x080001d0       0x30 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_uldivmod.o)
                0x080001d0                __aeabi_uldivmod
 .text          0x08000200      0x2f8 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_udivmoddi4.o)
                0x08000200                __udivmoddi4
 .text          0x080004f8        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_dvmd_tls.o)
                0x080004f8                __aeabi_idiv0
                0x080004f8                __aeabi_ldiv0
 *(.text*)
 *fill*         0x080004fc        0x4 
 .text.MX_ADC1_Init
                0x08000500      0x130 ./Core/Src/adc.o
                0x08000500                MX_ADC1_Init
 .text.HAL_ADC_MspInit
                0x08000630       0xfc ./Core/Src/adc.o
                0x08000630                HAL_ADC_MspInit
 .text.MX_DMA_Init
                0x0800072c       0x34 ./Core/Src/dma.o
                0x0800072c                MX_DMA_Init
 .text.StartDefaultTask
                0x08000760        0xc ./Core/Src/freertos.o
                0x08000760                StartDefaultTask
 .text.MX_FREERTOS_Init
                0x0800076c       0x20 ./Core/Src/freertos.o
                0x0800076c                MX_FREERTOS_Init
 .text.MX_GPIO_Init
                0x0800078c      0x130 ./Core/Src/gpio.o
                0x0800078c                MX_GPIO_Init
 .text.SystemClock_Config
                0x080008bc       0xa8 ./Core/Src/main.o
                0x080008bc                SystemClock_Config
 .text.startup.main
                0x08000964       0x28 ./Core/Src/main.o
                0x08000964                main
 .text.Error_Handler
                0x0800098c        0x4 ./Core/Src/main.o
                0x0800098c                Error_Handler
 .text.HAL_MspInit
                0x08000990       0x40 ./Core/Src/stm32f4xx_hal_msp.o
                0x08000990                HAL_MspInit
 .text.NMI_Handler
                0x080009d0        0x4 ./Core/Src/stm32f4xx_it.o
                0x080009d0                NMI_Handler
 .text.HardFault_Handler
                0x080009d4        0x4 ./Core/Src/stm32f4xx_it.o
                0x080009d4                HardFault_Handler
 .text.MemManage_Handler
                0x080009d8        0x4 ./Core/Src/stm32f4xx_it.o
                0x080009d8                MemManage_Handler
 .text.BusFault_Handler
                0x080009dc        0x4 ./Core/Src/stm32f4xx_it.o
                0x080009dc                BusFault_Handler
 .text.UsageFault_Handler
                0x080009e0        0x4 ./Core/Src/stm32f4xx_it.o
                0x080009e0                UsageFault_Handler
 .text.DebugMon_Handler
                0x080009e4        0x4 ./Core/Src/stm32f4xx_it.o
                0x080009e4                DebugMon_Handler
 .text.SysTick_Handler
                0x080009e8       0x18 ./Core/Src/stm32f4xx_it.o
                0x080009e8                SysTick_Handler
 .text.DMA2_Stream0_IRQHandler
                0x08000a00        0xc ./Core/Src/stm32f4xx_it.o
                0x08000a00                DMA2_Stream0_IRQHandler
 .text.SystemInit
                0x08000a0c       0x14 ./Core/Src/system_stm32f4xx.o
                0x08000a0c                SystemInit
 .text.MX_TIM4_Init
                0x08000a20       0x78 ./Core/Src/tim.o
                0x08000a20                MX_TIM4_Init
 .text.HAL_TIM_Base_MspInit
                0x08000a98       0x30 ./Core/Src/tim.o
                0x08000a98                HAL_TIM_Base_MspInit
 .text.Reset_Handler
                0x08000ac8       0x50 ./Core/Startup/startup_stm32f407zetx.o
                0x08000ac8                Reset_Handler
 .text.Default_Handler
                0x08000b18        0x2 ./Core/Startup/startup_stm32f407zetx.o
                0x08000b18                RTC_Alarm_IRQHandler
                0x08000b18                HASH_RNG_IRQHandler
                0x08000b18                EXTI2_IRQHandler
                0x08000b18                TIM8_CC_IRQHandler
                0x08000b18                TIM1_CC_IRQHandler
                0x08000b18                DMA2_Stream5_IRQHandler
                0x08000b18                DMA1_Stream5_IRQHandler
                0x08000b18                PVD_IRQHandler
                0x08000b18                SDIO_IRQHandler
                0x08000b18                TAMP_STAMP_IRQHandler
                0x08000b18                CAN2_RX1_IRQHandler
                0x08000b18                EXTI3_IRQHandler
                0x08000b18                TIM8_TRG_COM_TIM14_IRQHandler
                0x08000b18                TIM1_UP_TIM10_IRQHandler
                0x08000b18                TIM8_UP_TIM13_IRQHandler
                0x08000b18                I2C3_ER_IRQHandler
                0x08000b18                EXTI0_IRQHandler
                0x08000b18                I2C2_EV_IRQHandler
                0x08000b18                DMA1_Stream2_IRQHandler
                0x08000b18                CAN1_RX0_IRQHandler
                0x08000b18                FPU_IRQHandler
                0x08000b18                OTG_HS_WKUP_IRQHandler
                0x08000b18                CAN2_SCE_IRQHandler
                0x08000b18                DMA2_Stream2_IRQHandler
                0x08000b18                SPI1_IRQHandler
                0x08000b18                TIM6_DAC_IRQHandler
                0x08000b18                TIM1_BRK_TIM9_IRQHandler
                0x08000b18                DCMI_IRQHandler
                0x08000b18                CAN2_RX0_IRQHandler
                0x08000b18                DMA2_Stream3_IRQHandler
                0x08000b18                USART6_IRQHandler
                0x08000b18                USART3_IRQHandler
                0x08000b18                CAN1_RX1_IRQHandler
                0x08000b18                UART5_IRQHandler
                0x08000b18                TIM4_IRQHandler
                0x08000b18                I2C1_EV_IRQHandler
                0x08000b18                DMA1_Stream6_IRQHandler
                0x08000b18                DMA1_Stream1_IRQHandler
                0x08000b18                UART4_IRQHandler
                0x08000b18                TIM3_IRQHandler
                0x08000b18                RCC_IRQHandler
                0x08000b18                TIM8_BRK_TIM12_IRQHandler
                0x08000b18                Default_Handler
                0x08000b18                EXTI15_10_IRQHandler
                0x08000b18                ADC_IRQHandler
                0x08000b18                DMA1_Stream7_IRQHandler
                0x08000b18                TIM7_IRQHandler
                0x08000b18                CAN2_TX_IRQHandler
                0x08000b18                TIM5_IRQHandler
                0x08000b18                DMA2_Stream7_IRQHandler
                0x08000b18                I2C3_EV_IRQHandler
                0x08000b18                EXTI9_5_IRQHandler
                0x08000b18                RTC_WKUP_IRQHandler
                0x08000b18                ETH_WKUP_IRQHandler
                0x08000b18                SPI2_IRQHandler
                0x08000b18                OTG_HS_EP1_IN_IRQHandler
                0x08000b18                DMA1_Stream0_IRQHandler
                0x08000b18                CAN1_TX_IRQHandler
                0x08000b18                EXTI4_IRQHandler
                0x08000b18                FSMC_IRQHandler
                0x08000b18                ETH_IRQHandler
                0x08000b18                OTG_HS_EP1_OUT_IRQHandler
                0x08000b18                WWDG_IRQHandler
                0x08000b18                TIM2_IRQHandler
                0x08000b18                OTG_FS_WKUP_IRQHandler
                0x08000b18                TIM1_TRG_COM_TIM11_IRQHandler
                0x08000b18                OTG_HS_IRQHandler
                0x08000b18                EXTI1_IRQHandler
                0x08000b18                USART2_IRQHandler
                0x08000b18                I2C2_ER_IRQHandler
                0x08000b18                DMA2_Stream1_IRQHandler
                0x08000b18                CAN1_SCE_IRQHandler
                0x08000b18                FLASH_IRQHandler
                0x08000b18                DMA2_Stream4_IRQHandler
                0x08000b18                USART1_IRQHandler
                0x08000b18                OTG_FS_IRQHandler
                0x08000b18                SPI3_IRQHandler
                0x08000b18                DMA1_Stream4_IRQHandler
                0x08000b18                I2C1_ER_IRQHandler
                0x08000b18                DMA2_Stream6_IRQHandler
                0x08000b18                DMA1_Stream3_IRQHandler
 *fill*         0x08000b1a        0x2 
 .text.HAL_InitTick
                0x08000b1c       0x48 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
                0x08000b1c                HAL_InitTick
 .text.HAL_Init
                0x08000b64       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
                0x08000b64                HAL_Init
 .text.HAL_IncTick
                0x08000b98       0x18 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
                0x08000b98                HAL_IncTick
 .text.HAL_GetTick
                0x08000bb0        0xc ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
                0x08000bb0                HAL_GetTick
 .text.HAL_ADC_Init
                0x08000bbc      0x158 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
                0x08000bbc                HAL_ADC_Init
 .text.HAL_ADC_ConfigChannel
                0x08000d14      0x150 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
                0x08000d14                HAL_ADC_ConfigChannel
 .text.HAL_NVIC_SetPriorityGrouping
                0x08000e64       0x24 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
                0x08000e64                HAL_NVIC_SetPriorityGrouping
 .text.HAL_NVIC_SetPriority
                0x08000e88       0x78 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
                0x08000e88                HAL_NVIC_SetPriority
 .text.HAL_NVIC_EnableIRQ
                0x08000f00       0x1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
                0x08000f00                HAL_NVIC_EnableIRQ
 .text.HAL_SYSTICK_Config
                0x08000f1c       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
                0x08000f1c                HAL_SYSTICK_Config
 .text.HAL_DMA_Init
                0x08000f50      0x128 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
                0x08000f50                HAL_DMA_Init
 .text.HAL_DMA_IRQHandler
                0x08001078      0x1ac ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
                0x08001078                HAL_DMA_IRQHandler
 .text.HAL_GPIO_Init
                0x08001224      0x238 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
                0x08001224                HAL_GPIO_Init
 .text.HAL_GPIO_WritePin
                0x0800145c        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
                0x0800145c                HAL_GPIO_WritePin
 .text.HAL_RCC_OscConfig
                0x08001464      0x3c4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
                0x08001464                HAL_RCC_OscConfig
 .text.HAL_RCC_GetSysClockFreq
                0x08001828       0x68 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
                0x08001828                HAL_RCC_GetSysClockFreq
 .text.HAL_RCC_ClockConfig
                0x08001890      0x13c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
                0x08001890                HAL_RCC_ClockConfig
 .text.HAL_TIM_ConfigClockSource
                0x080019cc      0x154 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
                0x080019cc                HAL_TIM_ConfigClockSource
 .text.TIM_Base_SetConfig
                0x08001b20       0xe0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
                0x08001b20                TIM_Base_SetConfig
 .text.HAL_TIM_Base_Init
                0x08001c00       0x5c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
                0x08001c00                HAL_TIM_Base_Init
 .text.HAL_TIMEx_MasterConfigSynchronization
                0x08001c5c       0x80 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
                0x08001c5c                HAL_TIMEx_MasterConfigSynchronization
 .text.osKernelInitialize
                0x08001cdc       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
                0x08001cdc                osKernelInitialize
 .text.osKernelStart
                0x08001d00       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
                0x08001d00                osKernelStart
 .text.osThreadNew
                0x08001d34       0x8c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
                0x08001d34                osThreadNew
 .text.osDelay  0x08001dc0       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
                0x08001dc0                osDelay
 .text.vApplicationGetIdleTaskMemory
                0x08001ddc       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
                0x08001ddc                vApplicationGetIdleTaskMemory
 .text.vApplicationGetTimerTaskMemory
                0x08001df8       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
                0x08001df8                vApplicationGetTimerTaskMemory
 .text.vListInitialise
                0x08001e18       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
                0x08001e18                vListInitialise
 .text.vListInitialiseItem
                0x08001e30        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
                0x08001e30                vListInitialiseItem
 .text.vListInsertEnd
                0x08001e38       0x1c ./Middlewares/Third_Party/FreeRTOS/Source/list.o
                0x08001e38                vListInsertEnd
 .text.vListInsert
                0x08001e54       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
                0x08001e54                vListInsert
 .text.uxListRemove
                0x08001e84       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
                0x08001e84                uxListRemove
 .text.prvCopyDataToQueue
                0x08001eac       0x78 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.prvUnlockQueue
                0x08001f24       0x8c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .text.xQueueGenericReset
                0x08001fb0       0x8c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
                0x08001fb0                xQueueGenericReset
 .text.xQueueGenericCreateStatic
                0x0800203c       0xa8 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
                0x0800203c                xQueueGenericCreateStatic
 .text.xQueueGenericSend
                0x080020e4      0x184 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
                0x080020e4                xQueueGenericSend
 .text.xQueueGenericSendFromISR
                0x08002268       0xc4 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
                0x08002268                xQueueGenericSendFromISR
 .text.xQueueReceive
                0x0800232c      0x174 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
                0x0800232c                xQueueReceive
 .text.vQueueAddToRegistry
                0x080024a0       0x30 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
                0x080024a0                vQueueAddToRegistry
 .text.vQueueWaitForMessageRestricted
                0x080024d0       0x50 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
                0x080024d0                vQueueWaitForMessageRestricted
 .text.prvAddNewTaskToReadyList
                0x08002520      0x10c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.prvAddCurrentTaskToDelayedList
                0x0800262c       0x8c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.prvInitialiseNewTask.constprop.0
                0x080026b8       0xa4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.prvIdleTask
                0x0800275c       0x98 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskCreateStatic
                0x080027f4       0x74 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x080027f4                xTaskCreateStatic
 .text.xTaskCreate
                0x08002868       0x60 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002868                xTaskCreate
 .text.vTaskStartScheduler
                0x080028c8       0x8c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x080028c8                vTaskStartScheduler
 .text.vTaskSuspendAll
                0x08002954       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002954                vTaskSuspendAll
 .text.xTaskGetTickCount
                0x08002964        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002964                xTaskGetTickCount
 .text.xTaskIncrementTick
                0x08002970      0x164 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002970                xTaskIncrementTick
 .text.xTaskResumeAll.part.0
                0x08002ad4      0x110 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .text.xTaskResumeAll
                0x08002be4       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002be4                xTaskResumeAll
 .text.vTaskDelay
                0x08002c04       0x60 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002c04                vTaskDelay
 .text.vTaskSwitchContext
                0x08002c64       0x8c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002c64                vTaskSwitchContext
 .text.vTaskPlaceOnEventList
                0x08002cf0       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002cf0                vTaskPlaceOnEventList
 .text.vTaskPlaceOnEventListRestricted
                0x08002d24       0x3c ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002d24                vTaskPlaceOnEventListRestricted
 .text.xTaskRemoveFromEventList
                0x08002d60       0x88 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002d60                xTaskRemoveFromEventList
 .text.vTaskInternalSetTimeOutState
                0x08002de8       0x18 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002de8                vTaskInternalSetTimeOutState
 .text.xTaskCheckForTimeOut
                0x08002e00       0x84 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002e00                xTaskCheckForTimeOut
 .text.vTaskMissedYield
                0x08002e84        0xc ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002e84                vTaskMissedYield
 .text.xTaskGetSchedulerState
                0x08002e90       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002e90                xTaskGetSchedulerState
 .text.xTaskPriorityDisinherit
                0x08002eb0       0x88 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x08002eb0                xTaskPriorityDisinherit
 .text.prvCheckForValidListAndQueue
                0x08002f38       0x6c ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.xTimerCreateTimerTask
                0x08002fa4       0x64 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
                0x08002fa4                xTimerCreateTimerTask
 .text.xTimerGenericCommand
                0x08003008       0x6c ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
                0x08003008                xTimerGenericCommand
 .text.prvSwitchTimerLists
                0x08003074       0x80 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.prvTimerTask
                0x080030f4      0x24c ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .text.prvPortStartFirstTask
                0x08003340       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .text.vPortEnableVFP
                0x08003368       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .text.prvTaskExitError
                0x0800337c       0x40 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .text.pxPortInitialiseStack
                0x080033bc       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
                0x080033bc                pxPortInitialiseStack
 *fill*         0x080033e4        0xc 
 .text.SVC_Handler
                0x080033f0       0x24 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
                0x080033f0                SVC_Handler
 .text.vPortEnterCritical
                0x08003414       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
                0x08003414                vPortEnterCritical
 .text.vPortExitCritical
                0x08003458       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
                0x08003458                vPortExitCritical
 .text.PendSV_Handler
                0x08003480       0x64 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
                0x08003480                PendSV_Handler
 .text.xPortSysTickHandler
                0x080034e4       0x2c ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
                0x080034e4                xPortSysTickHandler
 .text.vPortSetupTimerInterrupt
                0x08003510       0x28 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
                0x08003510                vPortSetupTimerInterrupt
 .text.xPortStartScheduler
                0x08003538      0x114 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
                0x08003538                xPortStartScheduler
 .text.vPortValidateInterruptPriority
                0x0800364c       0x5c ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
                0x0800364c                vPortValidateInterruptPriority
 .text.prvInsertBlockIntoFreeList
                0x080036a8       0x60 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .text.pvPortMalloc
                0x08003708      0x128 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
                0x08003708                pvPortMalloc
 .text.vPortFree
                0x08003830       0x80 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
                0x08003830                vPortFree
 .text.memset   0x080038b0       0x10 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
                0x080038b0                memset
 .text.__libc_init_array
                0x080038c0       0x48 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
                0x080038c0                __libc_init_array
 .text.memcpy   0x08003908       0x1c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memcpy-stub.o)
                0x08003908                memcpy
 *(.glue_7)
 .glue_7        0x08003924        0x0 linker stubs
 *(.glue_7t)
 .glue_7t       0x08003924        0x0 linker stubs
 *(.eh_frame)
 .eh_frame      0x08003924        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o
 *(.init)
 .init          0x08003924        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crti.o
                0x08003924                _init
 .init          0x08003928        0x8 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtn.o
 *(.fini)
 .fini          0x08003930        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crti.o
                0x08003930                _fini
 .fini          0x08003934        0x8 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtn.o
                0x0800393c                        . = ALIGN (0x4)
                0x0800393c                        _etext = .

.vfp11_veneer   0x0800393c        0x0
 .vfp11_veneer  0x0800393c        0x0 linker stubs

.v4_bx          0x0800393c        0x0
 .v4_bx         0x0800393c        0x0 linker stubs

.iplt           0x0800393c        0x0
 .iplt          0x0800393c        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o

.rodata         0x0800393c       0x60
                0x0800393c                        . = ALIGN (0x4)
 *(.rodata)
 *(.rodata*)
 .rodata.str1.4
                0x0800393c       0x24 ./Core/Src/freertos.o
                                  0xc (size before relaxing)
 .rodata.defaultTask_attributes
                0x08003960       0x24 ./Core/Src/freertos.o
                0x08003960                defaultTask_attributes
 .rodata.AHBPrescTable
                0x08003984       0x10 ./Core/Src/system_stm32f4xx.o
                0x08003984                AHBPrescTable
 .rodata.flagBitshiftOffset.0
                0x08003994        0x8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .rodata.vTaskStartScheduler.str1.4
                0x0800399c        0x5 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .rodata.prvCheckForValidListAndQueue.str1.4
                0x0800399c        0x5 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .rodata.xTimerCreateTimerTask.str1.4
                0x0800399c        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
                0x080039a4                        . = ALIGN (0x4)

.ARM.extab      0x0800399c        0x0
                0x0800399c                        . = ALIGN (0x4)
 *(.ARM.extab* .gnu.linkonce.armextab.*)
                0x0800399c                        . = ALIGN (0x4)

.ARM            0x0800399c        0x8
                0x0800399c                        . = ALIGN (0x4)
                0x0800399c                        __exidx_start = .
 *(.ARM.exidx*)
 .ARM.exidx     0x0800399c        0x8 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_udivmoddi4.o)
                0x080039a4                        __exidx_end = .
                0x080039a4                        . = ALIGN (0x4)

.preinit_array  0x080039a4        0x0
                0x080039a4                        . = ALIGN (0x4)
                0x080039a4                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array*)
                0x080039a4                        PROVIDE (__preinit_array_end = .)
                0x080039a4                        . = ALIGN (0x4)

.init_array     0x080039a4        0x4
                0x080039a4                        . = ALIGN (0x4)
                0x080039a4                        PROVIDE (__init_array_start = .)
 *(SORT_BY_NAME(.init_array.*))
 *(.init_array*)
 .init_array    0x080039a4        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o
                0x080039a8                        PROVIDE (__init_array_end = .)
                0x080039a8                        . = ALIGN (0x4)

.fini_array     0x080039a8        0x4
                0x080039a8                        . = ALIGN (0x4)
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_NAME(.fini_array.*))
 *(.fini_array*)
 .fini_array    0x080039a8        0x4 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o
                [!provide]                        PROVIDE (__fini_array_end = .)
                0x080039ac                        . = ALIGN (0x4)
                0x080039ac                        _sidata = LOADADDR (.data)

.rel.dyn        0x080039ac        0x0
 .rel.iplt      0x080039ac        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o

.data           0x20000000       0x10 load address 0x080039ac
                0x20000000                        . = ALIGN (0x4)
                0x20000000                        _sdata = .
 *(.data)
 *(.data*)
 .data.SystemCoreClock
                0x20000000        0x4 ./Core/Src/system_stm32f4xx.o
                0x20000000                SystemCoreClock
 .data.uwTickFreq
                0x20000004        0x1 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
                0x20000004                uwTickFreq
 *fill*         0x20000005        0x3 
 .data.uwTickPrio
                0x20000008        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
                0x20000008                uwTickPrio
 .data.uxCriticalNesting
                0x2000000c        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 *(.RamFunc)
 *(.RamFunc*)
                0x20000010                        . = ALIGN (0x4)
                0x20000010                        _edata = .
                0x080039bc                        _siccmram = LOADADDR (.ccmram)

.igot.plt       0x20000010        0x0 load address 0x080039bc
 .igot.plt      0x20000010        0x0 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o

.ccmram         0x10000000        0x0 load address 0x080039bc
                0x10000000                        . = ALIGN (0x4)
                0x10000000                        _sccmram = .
 *(.ccmram)
 *(.ccmram*)
                0x10000000                        . = ALIGN (0x4)
                0x10000000                        _eccmram = .
                0x10000000                        . = ALIGN (0x4)

.bss            0x20000010     0x4a68
                0x20000010                        _sbss = .
                0x20000010                        __bss_start__ = _sbss
 *(.bss)
 .bss           0x20000010       0x1c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o
 *(.bss*)
 .bss.hdma_adc1
                0x2000002c       0x60 ./Core/Src/adc.o
                0x2000002c                hdma_adc1
 *fill*         0x2000008c        0x4 
 .bss.hadc1     0x20000090       0x48 ./Core/Src/adc.o
                0x20000090                hadc1
 .bss.defaultTaskHandle
                0x200000d8        0x4 ./Core/Src/freertos.o
                0x200000d8                defaultTaskHandle
 .bss.htim4     0x200000dc       0x48 ./Core/Src/tim.o
                0x200000dc                htim4
 .bss.uwTick    0x20000124        0x4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
                0x20000124                uwTick
 .bss.Timer_Stack.0
                0x20000128      0x400 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .bss.Timer_TCB.1
                0x20000528       0x5c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .bss.Idle_Stack.2
                0x20000584      0x200 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .bss.Idle_TCB.3
                0x20000784       0x5c ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .bss.KernelState
                0x200007e0        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 *fill*         0x200007e4        0x4 
 .bss.xQueueRegistry
                0x200007e8       0x40 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
                0x200007e8                xQueueRegistry
 .bss.uxSchedulerSuspended
                0x20000828        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xNextTaskUnblockTime
                0x2000082c        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.uxTaskNumber
                0x20000830        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xNumOfOverflows
                0x20000834        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xYieldPending
                0x20000838        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xPendedTicks
                0x2000083c        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xSchedulerRunning
                0x20000840        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.uxTopReadyPriority
                0x20000844        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xTickCount
                0x20000848        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.uxCurrentNumberOfTasks
                0x2000084c        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xSuspendedTaskList
                0x20000850       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.uxDeletedTasksWaitingCleanUp
                0x20000864        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xTasksWaitingTermination
                0x20000868       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xPendingReadyList
                0x2000087c       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.pxOverflowDelayedTaskList
                0x20000890        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.pxDelayedTaskList
                0x20000894        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xDelayedTaskList2
                0x20000898       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.xDelayedTaskList1
                0x200008ac       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.pxReadyTasksLists
                0x200008c0      0x460 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .bss.pxCurrentTCB
                0x20000d20        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
                0x20000d20                pxCurrentTCB
 .bss.xStaticTimerQueue.0
                0x20000d24       0x50 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss.ucStaticTimerQueueStorage.1
                0x20000d74       0xa0 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss.xLastTime.2
                0x20000e14        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss.xTimerTaskHandle
                0x20000e18        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss.xTimerQueue
                0x20000e1c        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss.pxOverflowTimerList
                0x20000e20        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss.pxCurrentTimerList
                0x20000e24        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss.xActiveTimerList2
                0x20000e28       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss.xActiveTimerList1
                0x20000e3c       0x14 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .bss.ulMaxPRIGROUPValue
                0x20000e50        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .bss.ucMaxSysCallPriority
                0x20000e54        0x1 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 *fill*         0x20000e55        0x3 
 .bss.xBlockAllocatedBit
                0x20000e58        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .bss.xNumberOfSuccessfulFrees
                0x20000e5c        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .bss.xNumberOfSuccessfulAllocations
                0x20000e60        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .bss.xMinimumEverFreeBytesRemaining
                0x20000e64        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .bss.xFreeBytesRemaining
                0x20000e68        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .bss.pxEnd     0x20000e6c        0x4 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .bss.xStart    0x20000e70        0x8 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .bss.ucHeap    0x20000e78     0x3c00 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 *(COMMON)
                0x20004a78                        . = ALIGN (0x4)
                0x20004a78                        _ebss = .
                0x20004a78                        __bss_end__ = _ebss

._user_heap_stack
                0x20004a78      0x600
                0x20004a78                        . = ALIGN (0x8)
                [!provide]                        PROVIDE (end = .)
                0x20004a78                        PROVIDE (_end = .)
                0x20004c78                        . = (. + _Min_Heap_Size)
 *fill*         0x20004a78      0x200 
                0x20005078                        . = (. + _Min_Stack_Size)
 *fill*         0x20004c78      0x400 
                0x20005078                        . = ALIGN (0x8)

/DISCARD/
 libc.a(*)
 libm.a(*)
 libgcc.a(*)

.ARM.attributes
                0x00000000       0x30
 *(.ARM.attributes)
 .ARM.attributes
                0x00000000       0x22 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crti.o
 .ARM.attributes
                0x00000022       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtbegin.o
 .ARM.attributes
                0x00000056       0x34 ./Core/Src/adc.o
 .ARM.attributes
                0x0000008a       0x34 ./Core/Src/dma.o
 .ARM.attributes
                0x000000be       0x34 ./Core/Src/freertos.o
 .ARM.attributes
                0x000000f2       0x34 ./Core/Src/gpio.o
 .ARM.attributes
                0x00000126       0x34 ./Core/Src/main.o
 .ARM.attributes
                0x0000015a       0x34 ./Core/Src/stm32f4xx_hal_msp.o
 .ARM.attributes
                0x0000018e       0x34 ./Core/Src/stm32f4xx_it.o
 .ARM.attributes
                0x000001c2       0x34 ./Core/Src/system_stm32f4xx.o
 .ARM.attributes
                0x000001f6       0x34 ./Core/Src/tim.o
 .ARM.attributes
                0x0000022a       0x21 ./Core/Startup/startup_stm32f407zetx.o
 .ARM.attributes
                0x0000024b       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .ARM.attributes
                0x0000027f       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .ARM.attributes
                0x000002b3       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .ARM.attributes
                0x000002e7       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .ARM.attributes
                0x0000031b       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .ARM.attributes
                0x0000034f       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .ARM.attributes
                0x00000383       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .ARM.attributes
                0x000003b7       0x34 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .ARM.attributes
                0x000003eb       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .ARM.attributes
                0x0000041f       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .ARM.attributes
                0x00000453       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .ARM.attributes
                0x00000487       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .ARM.attributes
                0x000004bb       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .ARM.attributes
                0x000004ef       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .ARM.attributes
                0x00000523       0x34 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .ARM.attributes
                0x00000557       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .ARM.attributes
                0x0000058b       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .ARM.attributes
                0x000005bf       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memcpy-stub.o)
 .ARM.attributes
                0x000005f3       0x22 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x00000615       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x00000649       0x22 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x0000066b       0x22 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard/crtn.o
OUTPUT(D.elf elf32-littlearm)
LOAD linker stubs
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc.a
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libm.a
LOAD D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a

.debug_info     0x00000000    0x1c11f
 .debug_info    0x00000000      0xc35 ./Core/Src/adc.o
 .debug_info    0x00000c35      0x4db ./Core/Src/dma.o
 .debug_info    0x00001110      0x3e5 ./Core/Src/freertos.o
 .debug_info    0x000014f5      0x528 ./Core/Src/gpio.o
 .debug_info    0x00001a1d      0x610 ./Core/Src/main.o
 .debug_info    0x0000202d      0x4d1 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_info    0x000024fe      0x3df ./Core/Src/stm32f4xx_it.o
 .debug_info    0x000028dd      0x52b ./Core/Src/system_stm32f4xx.o
 .debug_info    0x00002e08      0x9fa ./Core/Src/tim.o
 .debug_info    0x00003802       0x30 ./Core/Startup/startup_stm32f407zetx.o
 .debug_info    0x00003832      0xa1c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_info    0x0000424e      0xe7c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_info    0x000050ca     0x1023 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_info    0x000060ed      0xb2e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_info    0x00006c1b      0x70c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_info    0x00007327      0xae5 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_info    0x00007e0c     0x4735 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_info    0x0000c541     0x1b72 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_info    0x0000e0b3     0x4ed3 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_info    0x00012f86      0x2c3 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_info    0x00013249     0x2e0c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_info    0x00016055     0x3758 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_info    0x000197ad     0x1db2 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_info    0x0001b55f      0x589 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_info    0x0001bae8      0x637 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o

.debug_abbrev   0x00000000     0x4189
 .debug_abbrev  0x00000000      0x231 ./Core/Src/adc.o
 .debug_abbrev  0x00000231      0x12e ./Core/Src/dma.o
 .debug_abbrev  0x0000035f      0x141 ./Core/Src/freertos.o
 .debug_abbrev  0x000004a0      0x16f ./Core/Src/gpio.o
 .debug_abbrev  0x0000060f      0x24e ./Core/Src/main.o
 .debug_abbrev  0x0000085d      0x12e ./Core/Src/stm32f4xx_hal_msp.o
 .debug_abbrev  0x0000098b      0x195 ./Core/Src/stm32f4xx_it.o
 .debug_abbrev  0x00000b20      0x120 ./Core/Src/system_stm32f4xx.o
 .debug_abbrev  0x00000c40      0x1f3 ./Core/Src/tim.o
 .debug_abbrev  0x00000e33       0x24 ./Core/Startup/startup_stm32f407zetx.o
 .debug_abbrev  0x00000e57      0x285 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_abbrev  0x000010dc      0x2f2 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_abbrev  0x000013ce      0x388 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_abbrev  0x00001756      0x337 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_abbrev  0x00001a8d      0x215 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_abbrev  0x00001ca2      0x352 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_abbrev  0x00001ff4      0x38f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_abbrev  0x00002383      0x363 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_abbrev  0x000026e6      0x618 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_abbrev  0x00002cfe      0x10c ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_abbrev  0x00002e0a      0x511 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_abbrev  0x0000331b      0x503 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_abbrev  0x0000381e      0x42a ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_abbrev  0x00003c48      0x2a2 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_abbrev  0x00003eea      0x29f ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o

.debug_loclists
                0x00000000     0xe82f
 .debug_loclists
                0x00000000       0x45 ./Core/Src/adc.o
 .debug_loclists
                0x00000045       0x29 ./Core/Src/freertos.o
 .debug_loclists
                0x0000006e       0x7b ./Core/Src/system_stm32f4xx.o
 .debug_loclists
                0x000000e9       0xf4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_loclists
                0x000001dd      0x7e0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_loclists
                0x000009bd      0x5e7 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_loclists
                0x00000fa4      0x655 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_loclists
                0x000015f9      0x3c9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_loclists
                0x000019c2      0x390 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_loclists
                0x00001d52     0x3e60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_loclists
                0x00005bb2     0x11f6 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_loclists
                0x00006da8     0x3261 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_loclists
                0x0000a009       0x7e ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_loclists
                0x0000a087     0x18fc ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_loclists
                0x0000b983     0x1bb1 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_loclists
                0x0000d534      0xeb8 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_loclists
                0x0000e3ec       0xb0 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_loclists
                0x0000e49c      0x393 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o

.debug_aranges  0x00000000     0x11e0
 .debug_aranges
                0x00000000       0x30 ./Core/Src/adc.o
 .debug_aranges
                0x00000030       0x20 ./Core/Src/dma.o
 .debug_aranges
                0x00000050       0x28 ./Core/Src/freertos.o
 .debug_aranges
                0x00000078       0x20 ./Core/Src/gpio.o
 .debug_aranges
                0x00000098       0x30 ./Core/Src/main.o
 .debug_aranges
                0x000000c8       0x20 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_aranges
                0x000000e8       0x58 ./Core/Src/stm32f4xx_it.o
 .debug_aranges
                0x00000140       0x28 ./Core/Src/system_stm32f4xx.o
 .debug_aranges
                0x00000168       0x30 ./Core/Src/tim.o
 .debug_aranges
                0x00000198       0x28 ./Core/Startup/startup_stm32f407zetx.o
 .debug_aranges
                0x000001c0       0xf0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_aranges
                0x000002b0       0xe0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_aranges
                0x00000390       0xc0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_aranges
                0x00000450       0x78 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_aranges
                0x000004c8       0x58 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_aranges
                0x00000520       0x88 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_aranges
                0x000005a8      0x3a0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_aranges
                0x00000948      0x160 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_aranges
                0x00000aa8      0x268 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_aranges
                0x00000d10       0x40 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_aranges
                0x00000d50      0x128 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_aranges
                0x00000e78      0x1e0 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_aranges
                0x00001058       0xb8 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_aranges
                0x00001110       0x80 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_aranges
                0x00001190       0x50 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o

.debug_rnglists
                0x00000000     0x17b3
 .debug_rnglists
                0x00000000       0x39 ./Core/Src/adc.o
 .debug_rnglists
                0x00000039       0x22 ./Core/Src/dma.o
 .debug_rnglists
                0x0000005b       0x19 ./Core/Src/freertos.o
 .debug_rnglists
                0x00000074       0x2f ./Core/Src/gpio.o
 .debug_rnglists
                0x000000a3       0x44 ./Core/Src/main.o
 .debug_rnglists
                0x000000e7       0x1f ./Core/Src/stm32f4xx_hal_msp.o
 .debug_rnglists
                0x00000106       0x3d ./Core/Src/stm32f4xx_it.o
 .debug_rnglists
                0x00000143       0x19 ./Core/Src/system_stm32f4xx.o
 .debug_rnglists
                0x0000015c       0x2b ./Core/Src/tim.o
 .debug_rnglists
                0x00000187       0x19 ./Core/Startup/startup_stm32f407zetx.o
 .debug_rnglists
                0x000001a0       0xaf ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_rnglists
                0x0000024f       0xc6 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_rnglists
                0x00000315      0x100 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_rnglists
                0x00000415       0x9e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_rnglists
                0x000004b3       0x4b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_rnglists
                0x000004fe       0xca ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_rnglists
                0x000005c8      0x48e ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_rnglists
                0x00000a56      0x184 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_rnglists
                0x00000bda      0x36b ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_rnglists
                0x00000f45       0x2b ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_rnglists
                0x00000f70      0x28c ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_rnglists
                0x000011fc      0x381 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_rnglists
                0x0000157d      0x17b ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_rnglists
                0x000016f8       0x81 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_rnglists
                0x00001779       0x3a ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o

.debug_macro    0x00000000    0x23f1e
 .debug_macro   0x00000000      0x200 ./Core/Src/adc.o
 .debug_macro   0x00000200      0xad8 ./Core/Src/adc.o
 .debug_macro   0x00000cd8      0x29b ./Core/Src/adc.o
 .debug_macro   0x00000f73       0x2e ./Core/Src/adc.o
 .debug_macro   0x00000fa1       0x2f ./Core/Src/adc.o
 .debug_macro   0x00000fd0       0x22 ./Core/Src/adc.o
 .debug_macro   0x00000ff2       0x8e ./Core/Src/adc.o
 .debug_macro   0x00001080       0x51 ./Core/Src/adc.o
 .debug_macro   0x000010d1      0x103 ./Core/Src/adc.o
 .debug_macro   0x000011d4       0x6a ./Core/Src/adc.o
 .debug_macro   0x0000123e      0x1df ./Core/Src/adc.o
 .debug_macro   0x0000141d       0x1c ./Core/Src/adc.o
 .debug_macro   0x00001439       0x22 ./Core/Src/adc.o
 .debug_macro   0x0000145b       0xfb ./Core/Src/adc.o
 .debug_macro   0x00001556     0x1011 ./Core/Src/adc.o
 .debug_macro   0x00002567      0x11f ./Core/Src/adc.o
 .debug_macro   0x00002686    0x15ea5 ./Core/Src/adc.o
 .debug_macro   0x0001852b       0x6d ./Core/Src/adc.o
 .debug_macro   0x00018598     0x3693 ./Core/Src/adc.o
 .debug_macro   0x0001bc2b      0x190 ./Core/Src/adc.o
 .debug_macro   0x0001bdbb       0x5c ./Core/Src/adc.o
 .debug_macro   0x0001be17      0x980 ./Core/Src/adc.o
 .debug_macro   0x0001c797      0x9e9 ./Core/Src/adc.o
 .debug_macro   0x0001d180      0x115 ./Core/Src/adc.o
 .debug_macro   0x0001d295      0x13e ./Core/Src/adc.o
 .debug_macro   0x0001d3d3       0xa5 ./Core/Src/adc.o
 .debug_macro   0x0001d478      0x174 ./Core/Src/adc.o
 .debug_macro   0x0001d5ec      0x287 ./Core/Src/adc.o
 .debug_macro   0x0001d873       0x5f ./Core/Src/adc.o
 .debug_macro   0x0001d8d2      0x236 ./Core/Src/adc.o
 .debug_macro   0x0001db08      0xb5b ./Core/Src/adc.o
 .debug_macro   0x0001e663      0x38b ./Core/Src/adc.o
 .debug_macro   0x0001e9ee      0x176 ./Core/Src/adc.o
 .debug_macro   0x0001eb64       0xf9 ./Core/Src/adc.o
 .debug_macro   0x0001ec5d      0x12c ./Core/Src/adc.o
 .debug_macro   0x0001ed89      0x21e ./Core/Src/adc.o
 .debug_macro   0x0001efa7       0x2e ./Core/Src/adc.o
 .debug_macro   0x0001efd5      0x127 ./Core/Src/adc.o
 .debug_macro   0x0001f0fc       0x7e ./Core/Src/adc.o
 .debug_macro   0x0001f17a       0x89 ./Core/Src/adc.o
 .debug_macro   0x0001f203      0x8ed ./Core/Src/adc.o
 .debug_macro   0x0001faf0       0x4d ./Core/Src/adc.o
 .debug_macro   0x0001fb3d      0x12d ./Core/Src/adc.o
 .debug_macro   0x0001fc6a      0x200 ./Core/Src/dma.o
 .debug_macro   0x0001fe6a      0x280 ./Core/Src/freertos.o
 .debug_macro   0x000200ea      0x169 ./Core/Src/freertos.o
 .debug_macro   0x00020253      0x15a ./Core/Src/freertos.o
 .debug_macro   0x000203ad       0xc9 ./Core/Src/freertos.o
 .debug_macro   0x00020476       0x1c ./Core/Src/freertos.o
 .debug_macro   0x00020492       0x26 ./Core/Src/freertos.o
 .debug_macro   0x000204b8      0x4c5 ./Core/Src/freertos.o
 .debug_macro   0x0002097d       0xb5 ./Core/Src/freertos.o
 .debug_macro   0x00020a32       0xaa ./Core/Src/freertos.o
 .debug_macro   0x00020adc       0x6c ./Core/Src/freertos.o
 .debug_macro   0x00020b48       0x74 ./Core/Src/freertos.o
 .debug_macro   0x00020bbc       0xdd ./Core/Src/freertos.o
 .debug_macro   0x00020c99      0x200 ./Core/Src/gpio.o
 .debug_macro   0x00020e99      0x2b2 ./Core/Src/main.o
 .debug_macro   0x0002114b      0x169 ./Core/Src/main.o
 .debug_macro   0x000212b4       0x66 ./Core/Src/main.o
 .debug_macro   0x0002131a      0x1f6 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_macro   0x00021510      0x268 ./Core/Src/stm32f4xx_it.o
 .debug_macro   0x00021778      0x1ec ./Core/Src/system_stm32f4xx.o
 .debug_macro   0x00021964      0x200 ./Core/Src/tim.o
 .debug_macro   0x00021b64      0x24c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_macro   0x00021db0      0x1ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_macro   0x00021f9d      0x1ec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_macro   0x00022189      0x1f2 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_macro   0x0002237b      0x1f2 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_macro   0x0002256d      0x210 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_macro   0x0002277d      0x1ed ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_macro   0x0002296a      0x1ec ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_macro   0x00022b56      0x3e3 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00022f39       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00022f49       0x20 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00022f69       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00022f79       0xf5 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x0002306e       0x91 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x000230ff       0x8d ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x0002318c       0x9a ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00023226       0x22 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x00023248       0x56 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_macro   0x0002329e      0x187 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_macro   0x00023425      0x216 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x0002363b       0x16 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x00023651       0x87 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_macro   0x000236d8      0x27f ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00023957       0x10 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_macro   0x00023967      0x1dc ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00023b43       0x97 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_macro   0x00023bda      0x196 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_macro   0x00023d70      0x1ae ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o

.debug_line     0x00000000    0x1e73d
 .debug_line    0x00000000      0xa12 ./Core/Src/adc.o
 .debug_line    0x00000a12      0x772 ./Core/Src/dma.o
 .debug_line    0x00001184      0x8e9 ./Core/Src/freertos.o
 .debug_line    0x00001a6d      0x818 ./Core/Src/gpio.o
 .debug_line    0x00002285      0xa34 ./Core/Src/main.o
 .debug_line    0x00002cb9      0x77a ./Core/Src/stm32f4xx_hal_msp.o
 .debug_line    0x00003433      0x93d ./Core/Src/stm32f4xx_it.o
 .debug_line    0x00003d70      0x7f8 ./Core/Src/system_stm32f4xx.o
 .debug_line    0x00004568      0x856 ./Core/Src/tim.o
 .debug_line    0x00004dbe       0x7a ./Core/Startup/startup_stm32f407zetx.o
 .debug_line    0x00004e38      0xb16 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_line    0x0000594e     0x1672 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_line    0x00006fc0      0xdf8 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_line    0x00007db8     0x1409 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_line    0x000091c1      0xd5f ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_line    0x00009f20     0x11da ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_line    0x0000b0fa     0x5163 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_line    0x0001025d     0x1cf9 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_line    0x00011f56     0x33ba ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_line    0x00015310      0x91e ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_line    0x00015c2e     0x294f ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_line    0x0001857d     0x35e3 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_line    0x0001bb60     0x1510 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_line    0x0001d070      0xa35 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_line    0x0001daa5      0xc98 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o

.debug_str      0x00000000    0xdf6ba
 .debug_str     0x00000000    0xdf6ba ./Core/Src/adc.o
                              0xcebc4 (size before relaxing)
 .debug_str     0x000df6ba    0xceb7e ./Core/Src/dma.o
 .debug_str     0x000df6ba    0xd33fa ./Core/Src/freertos.o
 .debug_str     0x000df6ba    0xce72c ./Core/Src/gpio.o
 .debug_str     0x000df6ba    0xd3261 ./Core/Src/main.o
 .debug_str     0x000df6ba    0xceb6e ./Core/Src/stm32f4xx_hal_msp.o
 .debug_str     0x000df6ba    0xd2655 ./Core/Src/stm32f4xx_it.o
 .debug_str     0x000df6ba    0xce711 ./Core/Src/system_stm32f4xx.o
 .debug_str     0x000df6ba    0xced02 ./Core/Src/tim.o
 .debug_str     0x000df6ba       0x58 ./Core/Startup/startup_stm32f407zetx.o
 .debug_str     0x000df6ba    0xcf295 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_str     0x000df6ba    0xcecfe ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_str     0x000df6ba    0xcf011 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_str     0x000df6ba    0xceb2b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_str     0x000df6ba    0xce88c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_str     0x000df6ba    0xceb56 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_str     0x000df6ba    0xcfa9b ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_str     0x000df6ba    0xcf2a7 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_str     0x000df6ba    0xd93cd ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_str     0x000df6ba     0xabe9 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_str     0x000df6ba     0xc73a ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_str     0x000df6ba     0xd1e6 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_str     0x000df6ba     0xcc5b ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_str     0x000df6ba     0x878f ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_str     0x000df6ba     0xb52b ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o

.comment        0x00000000       0x43
 .comment       0x00000000       0x43 ./Core/Src/adc.o
                                 0x44 (size before relaxing)
 .comment       0x00000043       0x44 ./Core/Src/dma.o
 .comment       0x00000043       0x44 ./Core/Src/freertos.o
 .comment       0x00000043       0x44 ./Core/Src/gpio.o
 .comment       0x00000043       0x44 ./Core/Src/main.o
 .comment       0x00000043       0x44 ./Core/Src/stm32f4xx_hal_msp.o
 .comment       0x00000043       0x44 ./Core/Src/stm32f4xx_it.o
 .comment       0x00000043       0x44 ./Core/Src/system_stm32f4xx.o
 .comment       0x00000043       0x44 ./Core/Src/tim.o
 .comment       0x00000043       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .comment       0x00000043       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .comment       0x00000043       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .comment       0x00000043       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .comment       0x00000043       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .comment       0x00000043       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .comment       0x00000043       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .comment       0x00000043       0x44 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .comment       0x00000043       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .comment       0x00000043       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .comment       0x00000043       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .comment       0x00000043       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .comment       0x00000043       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .comment       0x00000043       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .comment       0x00000043       0x44 ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o

.debug_frame    0x00000000     0x33c0
 .debug_frame   0x00000000       0x80 ./Core/Src/adc.o
 .debug_frame   0x00000080       0x30 ./Core/Src/dma.o
 .debug_frame   0x000000b0       0x40 ./Core/Src/freertos.o
 .debug_frame   0x000000f0       0x34 ./Core/Src/gpio.o
 .debug_frame   0x00000124       0x58 ./Core/Src/main.o
 .debug_frame   0x0000017c       0x28 ./Core/Src/stm32f4xx_hal_msp.o
 .debug_frame   0x000001a4       0x9c ./Core/Src/stm32f4xx_it.o
 .debug_frame   0x00000240       0x30 ./Core/Src/system_stm32f4xx.o
 .debug_frame   0x00000270       0x5c ./Core/Src/tim.o
 .debug_frame   0x000002cc      0x1f4 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o
 .debug_frame   0x000004c0      0x2b0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o
 .debug_frame   0x00000770      0x188 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o
 .debug_frame   0x000008f8      0x17c ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o
 .debug_frame   0x00000a74       0xe0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o
 .debug_frame   0x00000b54      0x178 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o
 .debug_frame   0x00000ccc      0xa60 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o
 .debug_frame   0x0000172c      0x3d0 ./Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o
 .debug_frame   0x00001afc      0x874 ./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.o
 .debug_frame   0x00002370       0x88 ./Middlewares/Third_Party/FreeRTOS/Source/list.o
 .debug_frame   0x000023f8      0x3f4 ./Middlewares/Third_Party/FreeRTOS/Source/queue.o
 .debug_frame   0x000027ec      0x698 ./Middlewares/Third_Party/FreeRTOS/Source/tasks.o
 .debug_frame   0x00002e84      0x280 ./Middlewares/Third_Party/FreeRTOS/Source/timers.o
 .debug_frame   0x00003104      0x11c ./Middlewares/Third_Party/FreeRTOS/Source/portable/GCC/ARM_CM4F/port.o
 .debug_frame   0x00003220       0xcc ./Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.o
 .debug_frame   0x000032ec       0x20 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .debug_frame   0x0000330c       0x2c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .debug_frame   0x00003338       0x28 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/../../../../arm-none-eabi/lib/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memcpy-stub.o)
 .debug_frame   0x00003360       0x2c D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x0000338c       0x34 D:/STM32CUBE/STM32CubeIDE_1.19.0/STM32CubeIDE/plugins/com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.13.3.rel1.win32_1.0.0.202411081344/tools/bin/../lib/gcc/arm-none-eabi/13.3.1/thumb/v7e-m+fp/hard\libgcc.a(_udivmoddi4.o)

.debug_line_str
                0x00000000       0x3c
 .debug_line_str
                0x00000000       0x3c ./Core/Startup/startup_stm32f407zetx.o
